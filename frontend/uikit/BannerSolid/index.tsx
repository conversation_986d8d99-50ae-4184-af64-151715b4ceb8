import React, { <PERSON>actNode, useState } from 'react'
import { Pressable, StyleSheet, Text as NativeText, View } from 'react-native'

import { Row } from '@zeal/uikit/Row'

import { uuid } from '@zeal/toolkit/Crypto'

import { colors } from '../colors'
import { Column } from '../Column'
import { useLanguage } from '../Language/LanguageContext'
import { getFontStyles, styles as textStyles } from '../Text'

const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.surfaceDefault,
        overflow: 'hidden',
        paddingVertical: 10,
        paddingHorizontal: 12,
    },
    containerRounded: {
        borderRadius: 12,
    },
    title_basic: {
        flex: 1,
    },
    title_light: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textPrimary,
    },
    title_outline: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textPrimary,
    },
    title_neutral: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textStatusNeutralOnColor,
    },
    title_success: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textStatusSuccessOnColor,
    },
    title_critical: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textStatusCriticalOnColor,
    },
    title_warning: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textStatusWarningOnColor,
    },
    subtitle_light: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textSecondary,
    },
    subtitle_outline: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textSecondary,
    },
    subtitle_neutral: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textPrimary,
    },
    subtitle_success: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textPrimary,
    },
    subtitle_critical: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textPrimary,
    },
    subtitle_warning: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textPrimary,
    },
    variant_light: {
        backgroundColor: colors.surfaceDefault,
    },
    variant_neutral: {
        backgroundColor: colors.backgroundAlertNeutral,
    },
    variant_success: {
        backgroundColor: colors.backgroundAlertSuccess,
    },
    variant_critical: {
        backgroundColor: colors.backgroundAlertCritical,
    },
    variant_warning: {
        backgroundColor: colors.backgroundAlertWarning,
    },
    variant_outline: {
        backgroundColor: colors.surfaceDefault,
        borderWidth: 1,
        borderColor: colors.gray50,
    },

    hover_variant_light: { backgroundColor: colors.surfaceHover },
    hover_variant_neutral: {
        backgroundColor: colors.backgroundAlertNeutralHover,
    },
    hover_variant_success: {
        backgroundColor: colors.backgroundAlertSuccessHover,
    },
    hover_variant_critical: {
        backgroundColor: colors.backgroundAlertCriticalHover,
    },
    hover_variant_warning: {
        backgroundColor: colors.backgroundAlertWarningHover,
    },
    hover_variant_outline: {
        borderColor: colors.gray20,
    },

    active_variant_light: {
        backgroundColor: colors.gray50,
    },
    active_variant_neutral: {
        backgroundColor: colors.backgroundAlertNeutralClicked,
    },
    active_variant_success: {
        backgroundColor: colors.backgroundAlertSuccessClicked,
    },
    active_variant_critical: {
        backgroundColor: colors.backgroundAlertCriticalClicked,
    },
    active_variant_warning: {
        backgroundColor: colors.backgroundAlertWarningClicked,
    },
    active_variant_outline: {
        borderColor: colors.gray50,
        backgroundColor: colors.gray70,
    },
})

type Variant =
    | 'light'
    | 'neutral'
    | 'success'
    | 'critical'
    | 'warning'
    | 'outline'

type Props = {
    variant: Variant
    title: ReactNode
    subtitle?: ReactNode
    right?: ReactNode
    left?: ReactNode
    rounded?: boolean

    onClick?: () => void
}

export const BannerSolid = ({
    title,
    subtitle,
    variant,
    rounded,
    right,
    left,
    onClick,
}: Props) => {
    const [labelId] = useState(uuid())
    const [descriptionId] = useState(uuid())
    const { currentSelectedLanguage } = useLanguage()
    const fontStyles = getFontStyles('regular', currentSelectedLanguage)

    const content = (pressed?: boolean, hovered?: boolean) => {
        return (
            <View
                aria-labelledby={labelId}
                aria-describedby={descriptionId}
                style={[
                    styles.container,
                    styles[`variant_${variant}`],
                    hovered && styles[`hover_variant_${variant}`],
                    pressed && styles[`active_variant_${variant}`],
                    rounded && styles.containerRounded,
                ]}
            >
                <View>
                    <Column spacing={8}>
                        <Row spacing={8} alignX="stretch">
                            {left}
                            {title && (
                                <NativeText
                                    id={labelId}
                                    style={[
                                        styles[`title_basic`],
                                        styles[`title_${variant}`],
                                        fontStyles,
                                    ]}
                                >
                                    {title}
                                </NativeText>
                            )}
                            {right}
                        </Row>

                        {subtitle && (
                            <NativeText
                                id={descriptionId}
                                style={styles[`subtitle_${variant}`]}
                            >
                                {subtitle}
                            </NativeText>
                        )}
                    </Column>
                </View>
            </View>
        )
    }

    if (onClick) {
        return (
            <Pressable onPress={onClick}>
                {({ pressed, hovered }) => content(pressed, hovered)}
            </Pressable>
        )
    }

    return content()
}
