import React, { ReactNode } from 'react'
import { Pressable, StyleSheet, Text, View } from 'react-native'

import { Color, colors } from '@zeal/uikit/colors'
import { Extractor } from '@zeal/uikit/Extractor'
import { Spacer } from '@zeal/uikit/Spacer'

import { notReachable } from '@zeal/toolkit'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

import { LightArrowRight2 } from '../Icon/LightArrowRight2'
import { useLanguage } from '../Language/LanguageContext'
import { getFontStyles, styles as textStyles } from '../Text'

const styles = StyleSheet.create({
    limitBanner: {
        position: 'relative',
        overflow: 'hidden',
        paddingVertical: 10,
        paddingHorizontal: 12,
        borderRadius: 8,
    },
    container: {
        flexDirection: 'row',
        columnGap: 4,
        alignItems: 'center',
    },
    // @ts-ignore
    nonPressable: {
        ...(() => {
            switch (ZealPlatform.OS) {
                case 'ios':
                case 'android':
                    return {}
                case 'web':
                    return { cursor: 'auto' }
                /* istanbul ignore next */
                default:
                    return notReachable(ZealPlatform)
            }
        })(),
    },

    variant_default: {
        backgroundColor: colors.surfaceDefault,
    },
    variant_warning: {
        backgroundColor: colors.backgroundAlertWarning,
    },
    variant_neutral: {
        backgroundColor: colors.backgroundAlertNeutral,
    },
    variant_critical: {
        backgroundColor: colors.backgroundAlertCritical,
    },

    text_default: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textPrimary,
    },
    text_warning: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textStatusWarningOnColor,
    },
    text_neutral: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textStatusNeutralOnColor,
    },
    text_critical: {
        ...textStyles.variant_paragraph,
        ...textStyles.color_textStatusCriticalOnColor,
    },

    hover_default: {
        backgroundColor: colors.surfaceDefault,
    },
    hover_warning: {
        backgroundColor: colors.backgroundAlertWarningHover,
    },
    hover_neutral: {
        backgroundColor: colors.backgroundAlertNeutralHover,
    },
    hover_critical: {
        backgroundColor: colors.backgroundAlertCriticalHover,
    },
})

type Variant = Extractor<keyof typeof styles, 'variant'>

type Props = {
    variant: Variant
    icon: (renderProps: { size: number; color: Color }) => ReactNode
    title: ReactNode
    onClick: (() => void) | null
}

const ICON_SIZE = 20

const ICON_COLOR: Record<Variant, Color> = {
    default: 'textPrimary',
    warning: 'iconStatusWarningOnColor',
    neutral: 'iconStatusNeutralOnColor',
    critical: 'iconStatusCriticalOnColor',
}

export const BannerLimit = ({ variant, icon, title, onClick }: Props) => {
    const { currentSelectedLanguage } = useLanguage()
    const fontStyles = getFontStyles('regular', currentSelectedLanguage)

    return (
        <Pressable
            onPress={onClick}
            style={({ hovered }) => [
                styles.limitBanner,
                styles[`variant_${variant}`],
                !onClick && styles.nonPressable,
                onClick && hovered && styles[`hover_${variant}`],
            ]}
        >
            <View style={styles.container}>
                <View>
                    {icon({ size: ICON_SIZE, color: ICON_COLOR[variant] })}
                </View>
                <Text style={[styles[`text_${variant}`], fontStyles]}>
                    {title}
                </Text>
                <Spacer />
                {onClick && (
                    <View>
                        <LightArrowRight2
                            size={ICON_SIZE}
                            color={ICON_COLOR[variant]}
                        />
                    </View>
                )}
            </View>
        </Pressable>
    )
}
