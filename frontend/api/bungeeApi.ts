import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { ValidObject } from '@zeal/toolkit/Result'
import { joinURL } from '@zeal/toolkit/URL/joinURL'
import * as Web3 from '@zeal/toolkit/Web3'

import { processFetchFailure, processFetchResponse } from './interceptors'

declare const BungeeRawWitnessSymbol: unique symbol
declare const BungeeRawRequestTypeSymbol: unique symbol

export type BungeeRawRequestType = string & {
    __brand: typeof BungeeRawRequestTypeSymbol
}
export type BungeeRawWitness = ValidObject & {
    __brand: typeof BungeeRawWitnessSymbol
}

const API_KEY = 'D1mx54qg0a6ZVmk7BCW9B35GK6p1ABYzaAspIkze' // FIXME :: @resetko-zeal get our own API key

export const BUNGEE_BASE_URL =
    'https://public-backend.bungee.exchange/api/v1/bungee' // FIXME :: @resetko-zeal get prod endpoint

const AFFILIATE_ID =
    '609913096e193d62cecd16f5c236d0c459f6a18be1fef75aad43e6cbff367039708902197e0b2b78b1d76cb0837ad0b318baedceb5fef75aad43e6cb'

export type Paths = {
    get: Record<
        '/quote',
        {
            query: {
                userAddress: Web3.address.Address
                originChainId: string
                destinationChainId: string
                inputToken: Web3.address.Address
                inputAmount: string
                receiverAddress: Web3.address.Address
                outputToken: Web3.address.Address
            }
        }
    > &
        Record<
            '/status',
            {
                query: { requestHash: Hexadecimal }
            }
        >
    post: Record<
        '/submit',
        {
            query: undefined
            body: {
                request: BungeeRawWitness
                requestType: BungeeRawRequestType
                userSignature: Hexadecimal
                quoteId: string
            }
        }
    >
}

export const get = <T extends keyof Paths['get']>(
    path: T,
    params: { query?: Paths['get'][T]['query'] },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(BUNGEE_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(Object.entries(params.query))}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'GET',
        headers: {
            'api-key': API_KEY,
            affiliate: AFFILIATE_ID,
        },
        signal,
    })
        .catch((error) => processFetchFailure({ error }))
        .then((response) =>
            processFetchResponse({ params, method: 'GET', response, url })
        )
        .then((response) => response.json())
        .then((response) => {
            if (response.success) {
                return response.result
            }

            throw response
        })
}

export const post = <T extends keyof Paths['post']>(
    path: T,
    params: {
        query?: Paths['post'][T]['query']
        body: Paths['post'][T]['body']
    },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(BUNGEE_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(Object.entries(params.query))}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'POST',
        body: JSON.stringify(params.body),
        headers: {
            'Content-Type': 'application/json',
            'api-key': API_KEY,
            affiliate: AFFILIATE_ID,
        },
        signal,
    })
        .catch((error) => processFetchFailure({ error }))
        .then((response) =>
            processFetchResponse({ params, method: 'POST', response, url })
        )
        .then((response) => response.json())
        .then((response) => {
            if (response.success) {
                return response.result
            }

            throw response
        })
}
