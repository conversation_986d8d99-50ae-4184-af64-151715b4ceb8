import { hexToBytes, pad as viemPad } from 'viem' // eslint-disable-line no-restricted-imports

import {
    failure,
    match,
    nullableOf,
    oneOf,
    Result,
    string,
    success,
} from '../Result'

export type Hexadecimal = `0x${string}` // TODO @resetko-zeal with TypeScript's current capabilities, there isn't a clean way to create a Hexadecimal type that accepts '0x123' but rejects '0x' without using branded types

export const remove0x = (str: string): string => str.replace(/^0x/i, '')

const padToEven = (hex: string): string =>
    hex.padStart(hex.length + (hex.length % 2), '0')

export const pad = (hex: Hexadecimal): Hexadecimal => viemPad(hex)

export const unpad = (hex: Hexadecimal): Hexadecimal => {
    const unpadded = remove0x(hex).replace(/^0+/, '')
    return `0x${unpadded || '0'}`
}

const normalize = (hex: string): string =>
    padToEven(remove0x(hex).toLowerCase())

const HEXADECIMAL_REGEXP = /^(0x)?[0-9a-fA-F]+$/gi

export type StringValueNotHexadecimal = {
    type: 'string_value_is_not_hexadecimal'
    value: unknown
}

export const parse = (input: unknown): Result<unknown, Hexadecimal> =>
    string(input).andThen(parseFromString)

export const nullableParse = (
    input: unknown
): Result<unknown, Hexadecimal | null> =>
    oneOf(input, [nullableOf(input, parse), match(input, '0x').map(() => null)])

export const parserHexAsNumber = (input: unknown): Result<unknown, number> =>
    parse(input).andThen((hex) => {
        const num = Number(hex)
        return Number.isNaN(num)
            ? failure({ type: 'hex_not_a_number', value: input })
            : success(num)
    })

export const parseHexAsBigInt = (input: unknown): Result<unknown, bigint> =>
    parse(input).map(toBigInt)

export const parseFromString = (
    str: string
): Result<StringValueNotHexadecimal, Hexadecimal> => {
    const matches = str.trim().match(HEXADECIMAL_REGEXP)
    const normalized = normalize(str)

    return matches
        ? success(`0x${normalized}` as const)
        : failure({
              type: 'string_value_is_not_hexadecimal',
              value: str,
          })
}

export const concat = (...hexes: Hexadecimal[]): Hexadecimal =>
    `0x${hexes.map(remove0x).join('')}`

export const fromBuffer = (buffer: Uint8Array): Hexadecimal => {
    const hexes = Array.from(new Uint8Array(buffer))
        .map((item) => item.toString(16).padStart(2, '0'))
        .join('')

    return `0x${hexes}`
}

export const toBuffer = (hex: Hexadecimal): Uint8Array => hexToBytes(hex)

export const fromBigInt = (value: bigint): Hexadecimal =>
    `0x${normalize(value.toString(16))}`

export const fromNumber = (int: number): Hexadecimal =>
    `0x${Number(int).toString(16)}`

export const toBigInt = (hex: Hexadecimal): bigint => BigInt(hex)
