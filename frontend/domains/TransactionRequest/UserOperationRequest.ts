import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { Network } from '@zeal/domains/Network'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SubmittedUserOperation } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import { SimulationResult } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import {
    BoostedUserOperationWithSignature,
    MetaTransactionData,
    UserOperationWithSignature,
} from '@zeal/domains/UserOperation'

export type SimulatedWithDeploymentBundleUserOperationRequest = {
    type: 'simulated_safe_deployment_bundle_user_operation_request'
    network: Network
    account: Account
    dApp: DAppSiteInfo | null
    rpcRequests: EthSendTransaction[]

    metaTransactionDatas: MetaTransactionData[]
    addOwnerMetaTransactionData: MetaTransactionData
    initCode: Hexadecimal.Hexadecimal
    entrypoint: Web3.address.Address

    simulationResult: SimulationResult
}

export type SimulatedWithoutDeploymentBundleUserOperationRequest = {
    type: 'simulated_safe_without_deployment_bundle_user_operation_request'
    network: Network
    account: Account
    dApp: DAppSiteInfo | null
    rpcRequests: EthSendTransaction[]

    metaTransactionDatas: MetaTransactionData[]
    initCode: null
    entrypoint: Web3.address.Address

    simulationResult: SimulationResult
}

export type SimulatedWithAddOwnerUserOperationRequest = {
    type: 'simulated_safe_with_add_owner_user_operation_request'
    network: Network
    account: Account
    dApp: DAppSiteInfo | null
    rpcRequests: EthSendTransaction[]

    metaTransactionDatas: MetaTransactionData[]
    addOwnerMetaTransactionData: MetaTransactionData
    initCode: null
    entrypoint: Web3.address.Address

    simulationResult: SimulationResult
}

export type SignedUserOperationRequest = {
    type: 'signed_safe_user_operation_request'
    network: Network
    account: Account
    dApp: DAppSiteInfo | null
    rpcRequests: EthSendTransaction[]

    userOperationWithSignature:
        | UserOperationWithSignature
        | BoostedUserOperationWithSignature

    simulationResult: SimulationResult
}

export type SubmittedToBundlerUserOperationRequest = {
    type: 'submitted_safe_user_operation_request'
    network: Network
    account: Account
    dApp: DAppSiteInfo | null
    rpcRequests: EthSendTransaction[]

    submittedUserOperation: SubmittedUserOperation
    simulationResult: SimulationResult
}

export type SimulatedUserOperationRequest =
    | SimulatedWithDeploymentBundleUserOperationRequest
    | SimulatedWithoutDeploymentBundleUserOperationRequest
    | SimulatedWithAddOwnerUserOperationRequest
