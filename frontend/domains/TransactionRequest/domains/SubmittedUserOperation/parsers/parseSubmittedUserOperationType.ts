import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import {
    bigint,
    failure,
    match,
    number,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    SubmittedUserOperation,
    SubmittedUserOperationBundled,
    SubmittedUserOperationCompleted,
    SubmittedUserOperationFailed,
    SubmittedUserOperationPending,
    SubmittedUserOperationRejected,
    SubmittedUserOperationType,
} from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'

const MAP: Record<SubmittedUserOperationType, true> = {
    user_operation: true,
    boosted_user_operation: true,
}

export const parseSubmittedUserOperationType = (
    input: unknown
): Result<unknown, SubmittedUserOperationType> =>
    string(input).andThen((str) =>
        MAP[str as SubmittedUserOperationType]
            ? success(str as SubmittedUserOperationType)
            : failure({
                  type: 'unknown_submitted_user_operation_type',
                  value: str,
              })
    )

export const parseSubmittedUserOperation = (
    input: unknown
): Result<unknown, SubmittedUserOperation> =>
    oneOf(input, [
        parseSubmittedUserOperationPending(input),
        parseSubmittedUserOperationBundled(input),
        parseSubmittedUserOperationCompleted(input),
        parseSubmittedUserOperationRejected(input),
        parseSubmittedUserOperationFailed(input),
    ])

export const parseSubmittedUserOperationFailed = (
    input: unknown
): Result<unknown, SubmittedUserOperationFailed> =>
    object(input).andThen((obj) =>
        shape({
            type: parseSubmittedUserOperationType(obj.type),
            userOperationHash: Hexadecimal.parse(obj.userOperationHash),
            bundleTransactionHash: Hexadecimal.parse(obj.bundleTransactionHash),
            message: string(obj.message),
            sender: Web3.address.parse(obj.sender),
            state: match(obj.state, 'failed'),
            queuedAt: number(obj.queuedAt),
            failedAt: number(obj.failedAt),
            submittedBlock: bigint(obj.submittedBlock),
        })
    )

export const parseSubmittedUserOperationRejected = (
    input: unknown
): Result<unknown, SubmittedUserOperationRejected> =>
    object(input).andThen((obj) =>
        shape({
            type: parseSubmittedUserOperationType(obj.type),
            userOperationHash: Hexadecimal.parse(obj.userOperationHash),
            message: string(obj.message),
            sender: Web3.address.parse(obj.sender),
            state: match(obj.state, 'rejected'),
            queuedAt: number(obj.queuedAt),
            rejectedAt: number(obj.rejectedAt),
            submittedBlock: bigint(obj.submittedBlock),
        })
    )

export const parseSubmittedUserOperationCompleted = (
    input: unknown
): Result<unknown, SubmittedUserOperationCompleted> =>
    object(input).andThen((obj) =>
        shape({
            type: parseSubmittedUserOperationType(obj.type),
            userOperationHash: Hexadecimal.parse(obj.userOperationHash),
            bundleTransactionHash: Hexadecimal.parse(obj.bundleTransactionHash),
            sender: Web3.address.parse(obj.sender),
            state: match(obj.state, 'completed'),
            queuedAt: number(obj.queuedAt),
            completedAt: number(obj.completedAt),
            submittedBlock: bigint(obj.submittedBlock),
        })
    )

export const parseSubmittedUserOperationBundled = (
    input: unknown
): Result<unknown, SubmittedUserOperationBundled> =>
    object(input).andThen((obj) =>
        shape({
            type: parseSubmittedUserOperationType(obj.type),
            userOperationHash: Hexadecimal.parse(obj.userOperationHash),
            bundleTransactionHash: Hexadecimal.parse(obj.bundleTransactionHash),
            sender: Web3.address.parse(obj.sender),
            state: match(obj.state, 'bundled'),
            queuedAt: number(obj.queuedAt),
            submittedBlock: bigint(obj.submittedBlock),
        })
    )

export const parseSubmittedUserOperationPending = (
    input: unknown
): Result<unknown, SubmittedUserOperationPending> =>
    object(input).andThen((obj) =>
        shape({
            type: parseSubmittedUserOperationType(obj.type),
            userOperationHash: Hexadecimal.parse(obj.userOperationHash),
            sender: Web3.address.parse(obj.sender),
            state: match(obj.state, 'pending'),
            queuedAt: number(obj.queuedAt),
            submittedBlock: bigint(obj.submittedBlock),
        })
    )
