import { parse } from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

import { SigningKeyStore } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { Network, NetworkRPCMap } from '@zeal/domains/Network'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { fetchRPCResponse } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { signEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/signEthSendTransaction'
import {
    CustomPresetRequestFee,
    EstimatedFee,
} from '@zeal/domains/Transactions/domains/FeeForecast'

import { SubmitedTransactionQueued } from '../SubmitedTransaction'

export const signAndSubmitTransaction = async ({
    senderAddress,
    fee,
    network,
    networkRPCMap,
    nonce,
    sendTransactionRequest,
    keyStore,
    sessionPassword,
    gas,
    actionSource,
    signal,
}: {
    senderAddress: Web3.address.Address
    fee: EstimatedFee | CustomPresetRequestFee
    network: Network
    networkRPCMap: NetworkRPCMap
    nonce: number
    sendTransactionRequest: EthSendTransaction
    keyStore: SigningKeyStore
    sessionPassword: string
    gas: string
    actionSource: ActionSource2
    signal?: AbortSignal
}): Promise<SubmitedTransactionQueued> => {
    const rawTransaction = await signEthSendTransaction({
        sendTransactionRequest,
        network,
        keyStore,
        sessionPassword,
        fee,
        networkRPCMap,
        nonce,
        gas,
    })

    const rpcResponse = await fetchRPCResponse({
        network,
        networkRPCMap,
        request: rawTransaction,
        actionSource,
        signal,
    })

    const transactionHash = parse(rpcResponse).getSuccessResultOrThrow(
        'failed to parse RPC response for sendRawTransactioni'
    )

    const submitedTransaction: SubmitedTransactionQueued = {
        state: 'queued',
        hash: transactionHash,
        submittedNonce: nonce,
        queuedAt: Date.now(),
        senderAddress,
    }

    return submitedTransaction
}
