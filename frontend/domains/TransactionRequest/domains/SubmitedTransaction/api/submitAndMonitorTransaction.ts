import * as Web3 from '@zeal/toolkit/Web3'

import { SigningKeyStore } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { Network, NetworkRPCMap } from '@zeal/domains/Network'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import {
    CustomPresetRequestFee,
    EstimatedFee,
} from '@zeal/domains/Transactions/domains/FeeForecast'

import { monitorSubmitedTransaction } from './monitorSubmitedTransactions'
import { signAndSubmitTransaction } from './signAndSubmitTransaction'

import {
    SubmitedTransactionCompleted,
    SubmitedTransactionFailed,
    SubmitedTransactionReplaced,
} from '../SubmitedTransaction'

export const submitAndMonitorTransaction = async ({
    senderAddress,
    fee,
    network,
    networkRPCMap,
    nonce,
    sendTransactionRequest,
    keyStore,
    sessionPassword,
    gas,
    actionSource,
    signal,
}: {
    senderAddress: Web3.address.Address
    fee: EstimatedFee | CustomPresetRequestFee
    network: Network
    networkRPCMap: NetworkRPCMap
    nonce: number
    sendTransactionRequest: EthSendTransaction
    keyStore: SigningKeyStore
    sessionPassword: string
    gas: string
    actionSource: ActionSource2
    signal?: AbortSignal
}): Promise<
    | SubmitedTransactionCompleted
    | SubmitedTransactionFailed
    | SubmitedTransactionReplaced
> => {
    const submitedTransaction = await signAndSubmitTransaction({
        senderAddress,
        fee,
        network,
        networkRPCMap,
        nonce,
        sendTransactionRequest,
        keyStore,
        sessionPassword,
        gas,
        actionSource,
        signal,
    })

    return monitorSubmitedTransaction({
        submitedTransaction,
        network,
        networkRPCMap,
    })
}
