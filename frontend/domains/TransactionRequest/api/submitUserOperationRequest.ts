import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkRPCMap } from '@zeal/domains/Network'
import {
    SignedUserOperationRequest,
    SubmittedToBundlerUserOperationRequest,
} from '@zeal/domains/TransactionRequest'
import { submitUserOperationToBundler } from '@zeal/domains/UserOperation/api/submitUserOperationToBundler'

export const submitUserOperationRequest = async ({
    userOperationRequest,
    networkRPCMap,
    actionSource,
    signal,
}: {
    userOperationRequest: SignedUserOperationRequest
    networkRPCMap: NetworkRPCMap
    actionSource: ActionSource2
    signal?: AbortSignal
}): Promise<SubmittedToBundlerUserOperationRequest> => {
    const { network, userOperationWithSignature } = userOperationRequest

    const submittedUserOperation = await submitUserOperationToBundler({
        network,
        userOperationWithSignature,
        networkRPCMap,
        actionSource,
        signal,
    })

    return {
        type: 'submitted_safe_user_operation_request',
        network: userOperationRequest.network,
        simulationResult: userOperationRequest.simulationResult,
        account: userOperationRequest.account,
        dApp: userOperationRequest.dApp,
        rpcRequests: userOperationRequest.rpcRequests,
        submittedUserOperation,
    }
}
