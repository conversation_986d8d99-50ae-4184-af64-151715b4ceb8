import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'

import { AccountsMap } from '@zeal/domains/Account'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { captureTransactionFailureError } from '@zeal/domains/Error/helpers/captureTransactionFailureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { Submited } from '@zeal/domains/TransactionRequest'
import { SubmitedTransactionReplaced } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction'
import { fetchTransaction } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/api/fetchTransaction'
import { FetchTransactionResultByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchTransactionResult'
import { keystoreToUserEventType } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout, State as LayoutState } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    transactionRequest: Submited
    state: State
    accounts: AccountsMap
    keystores: KeyStoreMap
    networkMap: NetworkMap
    installationId: string
    networkRPCMap: NetworkRPCMap
    actionSource: ActionSource2
    fetchTransactionResultByRequest: FetchTransactionResultByRequest
    onMsg: (msg: Msg) => void
    defaultCurrencyConfig: DefaultCurrencyConfig
}

type Msg =
    | MsgOf<typeof Layout>
    | MsgOf<typeof Modal>
    | {
          type: 'transaction_request_replaced'
          transactionRequest: Submited
          submitedTransaction: SubmitedTransactionReplaced
      }

export type State = LayoutState

const POLL_INTERVAL_MS = 1000

const STUCK_REPORT_THRESHOLD_MS = 15 * 1000

export const CheckConfirmation = ({
    transactionRequest,
    state,
    accounts,
    keystores,
    networkMap,
    networkRPCMap,
    actionSource,
    onMsg,
    fetchTransactionResultByRequest,
    installationId,
    defaultCurrencyConfig,
}: Props) => {
    const [startTimestamp] = useState(() => Date.now())
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    const [pollable] = usePollableData(
        fetchTransaction,
        {
            type: 'loading',
            params: {
                transaction: transactionRequest.submitedTransaction,
                network: findNetworkByHexChainId(
                    transactionRequest.networkHexId,
                    networkMap
                ),
                networkRPCMap,
            },
        },
        {
            pollIntervalMilliseconds: POLL_INTERVAL_MS,
            stopIf: (pollable) => {
                switch (pollable.type) {
                    case 'loaded':
                    case 'reloading':
                    case 'subsequent_failed': {
                        switch (pollable.data.state) {
                            case 'queued':
                            case 'included_in_block':
                                return false

                            case 'completed':
                            case 'failed':
                            case 'replaced':
                                return true

                            /* istanbul ignore next */
                            default:
                                return notReachable(pollable.data)
                        }
                    }

                    case 'loading':
                    case 'error':
                        return false

                    /* istanbul ignore next */
                    default:
                        return notReachable(pollable)
                }
            },
        }
    )

    const onMsgLive = useLiveRef(onMsg)
    const { account } = transactionRequest
    const keyStore = getKeyStore({
        address: account.address,
        keyStoreMap: keystores,
    })
    const liveKeyStore = useLiveRef(keyStore)

    const transactionStuck = (() => {
        switch (pollable.type) {
            case 'loaded':
            case 'reloading':
            case 'subsequent_failed':
                switch (pollable.data.state) {
                    case 'queued':
                    case 'included_in_block':
                        return (
                            Date.now() - startTimestamp >
                            STUCK_REPORT_THRESHOLD_MS
                        )

                    case 'completed':
                    case 'failed':
                    case 'replaced':
                        return false

                    /* istanbul ignore next */
                    default:
                        return notReachable(pollable.data)
                }
            case 'loading':
            case 'error':
                return false
            default:
                return notReachable(pollable)
        }
    })()

    useEffect(() => {
        if (transactionStuck) {
            postUserEvent({
                type: 'TransactionTakesLongEvent',
                installationId,
                keystoreType: keystoreToUserEventType(liveKeyStore.current),
                network: pollable.params.network.hexChainId,
                source: actionSource.transactionEventSource,
            })
        }
    }, [
        transactionStuck,
        installationId,
        actionSource.transactionEventSource,
        pollable.params.network.hexChainId,
        liveKeyStore,
    ])

    const actionSourceLive = useLiveRef(actionSource)

    useEffect(() => {
        switch (pollable.type) {
            case 'loading':
                break
            case 'loaded':
            case 'reloading':
            case 'subsequent_failed':
                switch (pollable.data.state) {
                    case 'failed':
                        captureTransactionFailureError({
                            actionSource: actionSourceLive.current,
                            keyStore,
                            network: pollable.params.network,
                            transaction: pollable.data,
                        })
                        postUserEvent({
                            type: 'TransactionFailedEvent',
                            installationId,
                            keystoreType: keystoreToUserEventType(keyStore),
                            network: pollable.params.network.hexChainId,
                            state: pollable.data.state,
                            source: actionSourceLive.current
                                .transactionEventSource,
                        })
                        break
                    case 'completed':
                        postUserEvent({
                            type: 'TransactionCompletedEvent',
                            installationId,
                            keystoreType: keystoreToUserEventType(keyStore),
                            network: pollable.params.network.hexChainId,
                            source: actionSourceLive.current
                                .transactionEventSource,
                        })
                        break
                    case 'queued':
                    case 'included_in_block':
                        break
                    case 'replaced':
                        onMsgLive.current({
                            type: 'transaction_request_replaced',
                            transactionRequest,
                            submitedTransaction: pollable.data,
                        })
                        setModal({ type: 'could_not_find_transaction_status' })
                        break
                    /* istanbul ignore next */
                    default:
                        return notReachable(pollable.data)
                }
                break
            case 'error':
                captureError(pollable.error)
                break
            /* istanbul ignore next */
            default:
                return notReachable(pollable)
        }
    }, [
        actionSourceLive,
        installationId,
        keyStore,
        onMsgLive,
        pollable,
        transactionRequest,
    ])

    switch (pollable.type) {
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed':
            return (
                <>
                    <Layout
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        fetchTransactionResultByRequest={
                            fetchTransactionResultByRequest
                        }
                        accounts={accounts}
                        keystores={keystores}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        state={state}
                        transactionRequest={{
                            ...transactionRequest,
                            submitedTransaction: pollable.data,
                        }}
                        actionSource={actionSource}
                        onMsg={onMsg}
                    />
                    <Modal state={modal} onMsg={onMsg} />
                </>
            )

        // If we yet don't have info render what we have initially
        case 'loading':
        case 'error':
            return (
                <>
                    <Layout
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        accounts={accounts}
                        keystores={keystores}
                        fetchTransactionResultByRequest={
                            fetchTransactionResultByRequest
                        }
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        state={state}
                        transactionRequest={{
                            ...transactionRequest,
                            submitedTransaction: pollable.params.transaction,
                        }}
                        actionSource={actionSource}
                        onMsg={onMsg}
                    />
                    <Modal state={modal} onMsg={onMsg} />
                </>
            )

        default:
            return notReachable(pollable)
    }
}
