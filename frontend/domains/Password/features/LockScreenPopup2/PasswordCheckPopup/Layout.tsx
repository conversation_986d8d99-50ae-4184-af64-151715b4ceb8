import { useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { But<PERSON> } from '@zeal/uikit/Button'
import { Header } from '@zeal/uikit/Header'
import { BoldEye } from '@zeal/uikit/Icon/BoldEye'
import { BoldLock } from '@zeal/uikit/Icon/BoldLock'
import { EyeCrossed } from '@zeal/uikit/Icon/EyeCrossed'
import { IconButton } from '@zeal/uikit/IconButton'
import { Input } from '@zeal/uikit/Input'
import { Popup } from '@zeal/uikit/Popup'

import { notReachable } from '@zeal/toolkit'
import {
    EmptyStringError,
    nonEmptyString,
    Result,
    shape,
} from '@zeal/toolkit/Result'

type Props = {
    onMsg: (msg: Msg) => void
    decryptionErrors: DecryptionErrors | null
    installationId: string
}
type Form = { userPassword: string | null; variant: 'password' | 'text' }

type Msg =
    | { type: 'start_decrypting'; validatedForm: ValidatedForm }
    | { type: 'close' }
type ValidatedForm = {
    userPassword: string
}

export type DecryptionErrors = { type: 'decrypt_incorrect_password' }
type FormError = {
    submit?: EmptyStringError
}

const validate = ({
    form,
}: {
    form: Form
}): Result<FormError, ValidatedForm> => {
    return shape({
        submit: nonEmptyString(form.userPassword),
    }).map(({ submit }) => ({ userPassword: submit }))
}

export const Layout = ({ onMsg, decryptionErrors, installationId }: Props) => {
    const [form, setForm] = useState<Form>({
        userPassword: null,
        variant: 'password',
    })

    const validation = validate({ form })
    const errors = validation.getFailureReason() || {}
    const onSubmit = () => {
        switch (validation.type) {
            case 'Failure':
                break
            case 'Success':
                onMsg({
                    type: 'start_decrypting',
                    validatedForm: validation.data,
                })
                break
            /* istanbul ignore next */
            default:
                notReachable(validation)
        }
    }

    return (
        <Popup.Layout
            onMsg={onMsg}
            aria-labelledby="password-check-popup-label"
            aria-describedby="password-check-popup-description"
        >
            <Header
                icon={({ size, color }) => (
                    <BoldLock size={size} color={color} />
                )}
                titleId="password-check-popup-label"
                title={
                    <FormattedMessage
                        id="PasswordCheck.title"
                        defaultMessage="Enter password"
                    />
                }
                subtitle={
                    <FormattedMessage
                        id="PasswordChecker.subtitle"
                        defaultMessage="Please enter your password to verify it’s you"
                    />
                }
                subtitleId="password-check-popup-description"
            />

            <Popup.Content>
                <Input
                    keyboardType="default"
                    onSubmitEditing={onSubmit}
                    variant="regular"
                    type={form.variant}
                    autoFocus={true}
                    value={form.userPassword || ''}
                    onChange={(e) => {
                        setForm({
                            userPassword: e.nativeEvent.text,
                            variant: form.variant,
                        })
                    }}
                    state={decryptionErrors ? 'error' : 'normal'}
                    placeholder=""
                    message={
                        decryptionErrors ? (
                            <InputErrorMsg
                                error={decryptionErrors}
                                installationId={installationId}
                                onMsg={onMsg}
                            />
                        ) : null
                    }
                    rightIconButton={(() => {
                        switch (form.variant) {
                            case 'text':
                                return (
                                    <IconButton
                                        variant="on_light"
                                        onClick={() =>
                                            setForm({
                                                userPassword: form.userPassword,
                                                variant: 'password',
                                            })
                                        }
                                    >
                                        {({ color }) => (
                                            <BoldEye color={color} size={24} />
                                        )}
                                    </IconButton>
                                )
                            case 'password':
                                return (
                                    <IconButton
                                        variant="on_light"
                                        onClick={() =>
                                            setForm({
                                                userPassword: form.userPassword,
                                                variant: 'text',
                                            })
                                        }
                                    >
                                        {({ color }) => (
                                            <EyeCrossed
                                                color={color}
                                                size={24}
                                            />
                                        )}
                                    </IconButton>
                                )

                            /* istanbul ignore next */
                            default:
                                return notReachable(form.variant)
                        }
                    })()}
                />
            </Popup.Content>
            <Popup.Actions>
                <Button
                    size="regular"
                    variant="secondary"
                    onClick={() => onMsg({ type: 'close' })}
                >
                    <FormattedMessage
                        id="actions.cancel"
                        defaultMessage="Cancel"
                    />
                </Button>

                <Button
                    onClick={onSubmit}
                    size="regular"
                    variant="primary"
                    disabled={!!errors.submit}
                >
                    <SubmitCTA error={errors.submit} />
                </Button>
            </Popup.Actions>
        </Popup.Layout>
    )
}

const InputErrorMsg = ({
    error,
}: {
    error: DecryptionErrors
    onMsg: (msg: Msg) => void
    installationId: string
}) => {
    if (!error) {
        return null
    }
    switch (error.type) {
        case 'decrypt_incorrect_password':
            return (
                <FormattedMessage
                    id="lockScreen.passwordIncorrectMessage"
                    defaultMessage="Password is incorrect"
                />
            )
        default:
            return notReachable(error.type)
    }
}

const SubmitCTA = ({ error }: { error: FormError['submit'] }) => {
    if (!error) {
        return (
            <FormattedMessage id="actions.continue" defaultMessage="Continue" />
        )
    }

    switch (error.type) {
        case 'value_is_not_a_string':
        case 'string_is_empty':
            return (
                <FormattedMessage
                    id="lockScreen.passwordRequiredMessage"
                    defaultMessage="Password Required"
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(error)
    }
}
