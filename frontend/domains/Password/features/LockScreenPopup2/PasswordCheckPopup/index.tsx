import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { useLazyLoadableData } from '@zeal/toolkit/LoadableData/LazyLoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'

import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { decryptPassword } from '@zeal/domains/Password/helpers/decryptPassword'

import { Layout } from './Layout'

type Props = {
    onMsg: (msg: Msg) => void
    encryptedPassword: string
    installationId: string
}

type Msg =
    | Extract<MsgOf<typeof Layout>, { type: 'close' }>
    | { type: 'session_password_decrypted'; sessionPassword: string }

export const PasswordCheckPopup = ({
    onMsg,
    encryptedPassword,
    installationId,
}: Props) => {
    const liveMsg = useLiveRef(onMsg)
    const [loadable, setLoadable] = useLazyLoadableData(decryptPassword, {
        type: 'not_asked',
    })

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveMsg.current({
                    type: 'session_password_decrypted',
                    sessionPassword: loadable.data.sessionPassword,
                })
                break
            case 'not_asked':
            case 'loading':
            case 'error':
                break
            /* istanbul ignore next */
            default:
                notReachable(loadable)
        }
    }, [loadable, liveMsg])

    switch (loadable.type) {
        case 'not_asked':
            return (
                <Layout
                    installationId={installationId}
                    decryptionErrors={null}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'start_decrypting':
                                setLoadable({
                                    type: 'loading',
                                    params: {
                                        encrypted: encryptedPassword,
                                        userPassword:
                                            msg.validatedForm.userPassword,
                                    },
                                })
                                break

                            case 'close':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'loading':
            return (
                <Layout
                    installationId={installationId}
                    decryptionErrors={null}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'start_decrypting':
                                if (
                                    msg.validatedForm.userPassword !==
                                    loadable.params.userPassword
                                ) {
                                    setLoadable({
                                        type: 'loading',
                                        params: {
                                            encrypted: encryptedPassword,
                                            userPassword:
                                                msg.validatedForm.userPassword,
                                        },
                                    })
                                }
                                break
                            case 'close':
                                onMsg(msg)
                                break

                                break
                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'loaded':
            // handled in useEffect, closed by onMsg
            return null
        case 'error':
            const parsedAppError = parseAppError(loadable.error)
            switch (parsedAppError.type) {
                case 'decrypt_incorrect_password':
                    return (
                        <Layout
                            installationId={installationId}
                            decryptionErrors={parsedAppError}
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'start_decrypting':
                                        if (
                                            msg.validatedForm.userPassword !==
                                            loadable.params.userPassword
                                        ) {
                                            setLoadable({
                                                type: 'loading',
                                                params: {
                                                    encrypted:
                                                        encryptedPassword,
                                                    userPassword:
                                                        msg.validatedForm
                                                            .userPassword,
                                                },
                                            })
                                        }
                                        break
                                    case 'close':
                                        onMsg(msg)
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        notReachable(msg)
                                }
                            }}
                        />
                    )

                /* istanbul ignore next */
                default:
                    return (
                        <>
                            <AppErrorPopup
                                installationId={installationId}
                                error={parsedAppError}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            setLoadable({
                                                type: 'not_asked',
                                            })
                                            break
                                        case 'try_again_clicked':
                                            setLoadable({
                                                type: 'loading',
                                                params: loadable.params,
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                            <Layout
                                installationId={installationId}
                                decryptionErrors={null}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'start_decrypting':
                                            setLoadable({
                                                type: 'loading',
                                                params: {
                                                    encrypted:
                                                        encryptedPassword,
                                                    userPassword:
                                                        msg.validatedForm
                                                            .userPassword,
                                                },
                                            })
                                            break
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        </>
                    )
            }
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
