import { Modal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

import { PinLockScreen } from '@zeal/domains/Password/features/PinLockScreen'

import { PasswordCheckPopup } from './PasswordCheckPopup'

type Props = {
    onMsg: (msg: Msg) => void
    encryptedPassword: string
    installationId: string
}

type Msg = MsgOf<typeof PinLockScreen> | MsgOf<typeof PasswordCheckPopup>

export const LockScreenPopup2 = ({
    onMsg,
    encryptedPassword,
    installationId,
}: Props) => {
    switch (ZealPlatform.OS) {
        case 'android':
        case 'ios':
            return (
                <Modal>
                    <PinLockScreen
                        variant="default"
                        encryptedPassword={encryptedPassword}
                        installationId={installationId}
                        onMsg={onMsg}
                    />
                </Modal>
            )
        case 'web':
            return (
                <PasswordCheckPopup
                    onMsg={onMsg}
                    encryptedPassword={encryptedPassword}
                    installationId={installationId}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(ZealPlatform)
    }
}
