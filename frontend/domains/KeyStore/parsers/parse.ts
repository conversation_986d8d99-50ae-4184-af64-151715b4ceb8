import { uuid } from '@zeal/toolkit/Crypto'
import {
    boolean,
    match,
    number,
    object,
    oneOf,
    recordStrict,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { parseIndexKey } from '@zeal/domains/Address/helpers/parseIndexKey'
import {
    EOA,
    KeyStore,
    KeyStoreMap,
    LEDGER,
    PrivateKey,
    Safe4337,
    SecretPhrase,
    TrackOnly,
    Trezor,
} from '@zeal/domains/KeyStore'
import { parsePasskey } from '@zeal/domains/KeyStore/domains/Passkey/helpers/parse'

export const parse = (input: unknown): Result<unknown, KeyStoreMap> =>
    object(input)
        .andThen(parseIndexKey)
        .andThen((obj) =>
            recordStrict(obj, {
                keyParser: Web3.address.parse,
                valueParser: (o) =>
                    oneOf(o, [
                        parseKeyStore(o),
                        // as a guard to fail to parse full keystore map
                        success({ type: 'track_only', id: uuid() } as const),
                    ]),
            })
        )

export const parseKeyStore = (input: unknown): Result<unknown, KeyStore> =>
    object(input).andThen((obj) =>
        oneOf(obj, [
            parsePrivateKey(obj),
            parseLedger(obj),
            parseSecretPhrase(obj),
            parseTrezor(obj),
            parseSafe4337(obj),
            parseTrackOnly(obj),
        ])
    )

export const parseEOA = (input: unknown): Result<unknown, EOA> =>
    oneOf(input, [
        parsePrivateKey(input),
        parseLedger(input),
        parseSecretPhrase(input),
        parseTrezor(input),
    ])

const parseTrackOnly = (input: unknown): Result<unknown, TrackOnly> => {
    return object(input).andThen((obj) => {
        return shape({
            id: oneOf('id', [string(obj.id), success(uuid())]),
            type: match(obj.type, 'track_only' as const),
        })
    })
}

const parseTrezor = (input: unknown): Result<unknown, Trezor> => {
    return object(input).andThen((obj) => {
        return shape({
            id: oneOf('id', [string(obj.id), success(uuid())]),
            type: match(obj.type, 'trezor' as const),
            address: Web3.address.parse(obj.address),
            path: string(obj.path),
        })
    })
}

const parseLedger = (input: unknown): Result<unknown, LEDGER> => {
    return object(input).andThen((obj) => {
        return shape({
            id: oneOf('id', [string(obj.id), success(uuid())]),
            type: match(obj.type, 'ledger' as const),
            address: Web3.address.parse(obj.address),
            path: string(obj.path),
        })
    })
}

const parseSecretPhrase = (input: unknown): Result<unknown, SecretPhrase> =>
    object(input).andThen((obj) =>
        shape({
            id: oneOf('id', [string(obj.id), success(uuid())]),
            type: match(obj.type, 'secret_phrase_key' as const),
            bip44Path: string(obj.bip44Path),
            encryptedPhrase: string(obj.encryptedPhrase),
            confirmed: oneOf(obj.confirmed, [
                boolean(obj.confirmed),
                success(true),
            ]),
            googleDriveFile: oneOf(obj.googleDriveFile, [
                object(obj.googleDriveFile).andThen((file) =>
                    shape({
                        id: string(file.id),
                        modifiedTime: number(file.modifiedTime),
                    })
                ),
                success(null),
            ]),
        })
    )

const parsePrivateKey = (input: unknown): Result<unknown, PrivateKey> => {
    return object(input).andThen((obj) =>
        shape({
            id: oneOf('id', [string(obj.id), success(uuid())]),
            type: match(obj.type, 'private_key_store' as const),
            address: string(obj.address).andThen(Web3.address.fromString),
            encryptedPrivateKey: string(obj.encryptedPrivateKey),
        })
    )
}

const parseSafe4337 = (input: unknown): Result<unknown, Safe4337> => {
    return object(input).andThen((obj) =>
        shape({
            id: oneOf('id', [string(obj.id), success(uuid())]),
            type: match(obj.type, 'safe_4337' as const),
            address: Web3.address.parse(obj.address),
            localSignerKeyStore: parsePrivateKey(obj.localSignerKeyStore),

            safeDeplymentConfig: object(obj.safeDeplymentConfig).andThen(
                (config) =>
                    shape({
                        threshold: number(config.threshold),
                        saltNonce: string(config.saltNonce),
                        passkeyOwner: parsePasskey(config.passkeyOwner),
                    })
            ),
        })
    )
}
