import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'

import { AccountsMap } from '@zeal/domains/Account'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { SimulatedUserOperationRequest } from '@zeal/domains/TransactionRequest'
import { SimulateTransactionResponse } from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import {
    BoostedUserOperationWithoutSignature,
    GasAbstractionTransactionFee,
    UserOperationWithoutSignature,
} from '@zeal/domains/UserOperation'
import { fetchCurrentEntrypointNonce } from '@zeal/domains/UserOperation/api/fetchCurrentEntrypointNonce'
import { fetchERC20PaymasterAndData } from '@zeal/domains/UserOperation/api/fetchPaymasterAndData'

import { LoadingLayout } from './LoadingLayout'

type Props = {
    selectedFee: GasAbstractionTransactionFee
    userOperationRequest: SimulatedUserOperationRequest
    simulation: SimulateTransactionResponse
    visualState: VisualState
    networkRPCMap: NetworkRPCMap
    actionSource: ActionSource2
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type VisualState = { type: 'minimised' } | { type: 'maximised' }

type Msg =
    | { type: 'close' }
    | {
          type: 'on_user_operation_constructed'
          userOperation:
              | UserOperationWithoutSignature
              | BoostedUserOperationWithoutSignature
      }
    | MsgOf<typeof LoadingLayout>

const fetch = async ({
    selectedFee,
    userOperationRequest,
    networkRPCMap,
    installationId,
    signal,
}: {
    selectedFee: GasAbstractionTransactionFee
    userOperationRequest: SimulatedUserOperationRequest
    networkRPCMap: NetworkRPCMap
    installationId: string
    signal: AbortSignal
}): Promise<
    UserOperationWithoutSignature | BoostedUserOperationWithoutSignature
> => {
    // TODO :: @Nicvaniek do we need to fetch nonce so many times?
    const entrypointNonce = await fetchCurrentEntrypointNonce({
        network: userOperationRequest.network,
        address: userOperationRequest.account.address,
        entrypoint: userOperationRequest.entrypoint,
        networkRPCMap,
        signal,
    })

    switch (selectedFee.type) {
        case 'native_gas_abstraction_transaction_fee': {
            const { gasPrice, gasEstimate, callData } = selectedFee
            return {
                type: 'user_operation_without_signature',
                callData,
                sender: userOperationRequest.account.address,
                entrypointNonce,
                entrypoint: userOperationRequest.entrypoint,
                initCode: userOperationRequest.initCode,

                maxFeePerGas: gasPrice.maxFeePerGas,
                maxPriorityFeePerGas: gasPrice.maxPriorityFeePerGas,

                callGasLimit: gasEstimate.callGasLimit,
                preVerificationGas: gasEstimate.preVerificationGas,
                verificationGasLimit: gasEstimate.verificationGasLimit,

                paymasterAndData: null,
            }
        }
        case 'erc20_gas_abstraction_transaction_fee': {
            const paymasterAndData = await fetchERC20PaymasterAndData({
                nonce: entrypointNonce,
                network: userOperationRequest.network,
                signal,
                userOperationRequest,
                selectedFee,
                installationId,
            })

            return {
                type: 'user_operation_without_signature',
                callData: selectedFee.callData,
                sender: userOperationRequest.account.address,
                entrypointNonce,
                entrypoint: userOperationRequest.entrypoint,
                initCode: userOperationRequest.initCode,

                maxFeePerGas: selectedFee.gasPrice.maxFeePerGas,
                maxPriorityFeePerGas: selectedFee.gasPrice.maxPriorityFeePerGas,

                callGasLimit: selectedFee.gasEstimate.callGasLimit,
                preVerificationGas: selectedFee.gasEstimate.preVerificationGas,
                verificationGasLimit:
                    selectedFee.gasEstimate.verificationGasLimit,

                paymasterAndData,
            }
        }
        case 'sponsored_gas_abstraction_transaction_fee_2':
            return {
                type: 'boosted_user_operation_without_signature',
                callData: selectedFee.callData,
                sender: userOperationRequest.account.address,
                entrypointNonce,
                entrypoint: userOperationRequest.entrypoint,
                initCode: (userOperationRequest.initCode ||
                    '0x') as Hexadecimal,
                callGasLimit: selectedFee.gasEstimate.callGasLimit,
                verificationGasLimit:
                    selectedFee.gasEstimate.verificationGasLimit,
            }
        /* istanbul ignore next */
        default:
            return notReachable(selectedFee)
    }
}

export const ConstructUserOperation = ({
    userOperationRequest,
    selectedFee,
    simulation,
    visualState,
    actionSource,
    networkRPCMap,
    installationId,
    keyStoreMap,
    networkMap,
    accountsMap,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            userOperationRequest,
            selectedFee,
            networkRPCMap,
            installationId,
        },
    })

    const onMsgLive = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
                break
            case 'loaded':
                onMsgLive.current({
                    type: 'on_user_operation_constructed',
                    userOperation: loadable.data,
                })
                break
            case 'error':
                captureError(loadable.error)
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, onMsgLive])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return (
                <LoadingLayout
                    initialProgress={0}
                    userOperationRequest={userOperationRequest}
                    simulation={simulation}
                    visualState={visualState}
                    actionSource={actionSource}
                    installationId={installationId}
                    networkMap={networkMap}
                    keyStoreMap={keyStoreMap}
                    accountsMap={accountsMap}
                    onMsg={onMsg}
                />
            )
        case 'error':
            const error = parseAppError(loadable.error)
            return (
                <>
                    <LoadingLayout
                        initialProgress={0}
                        installationId={installationId}
                        simulation={simulation}
                        userOperationRequest={userOperationRequest}
                        visualState={visualState}
                        actionSource={actionSource}
                        networkMap={networkMap}
                        keyStoreMap={keyStoreMap}
                        accountsMap={accountsMap}
                        onMsg={onMsg}
                    />
                    <AppErrorPopup
                        installationId={installationId}
                        error={error}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break

                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
