import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Content } from '@zeal/uikit/Content'
import { Group } from '@zeal/uikit/Group'
import { Progress } from '@zeal/uikit/Progress'
import { Screen } from '@zeal/uikit/Screen'
import { Spinner } from '@zeal/uikit/Spinner'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'

import { AccountsMap } from '@zeal/domains/Account'
import { ConnectedMinimized } from '@zeal/domains/DApp/domains/ConnectionState/features/ConnectedMinimized'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap, Safe4337 } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ListItem } from '@zeal/domains/SmartContract/components/ListItem'
import {
    SignedUserOperationRequest,
    SubmittedToBundlerUserOperationRequest,
} from '@zeal/domains/TransactionRequest'
import { submitUserOperationRequest } from '@zeal/domains/TransactionRequest/api/submitUserOperationRequest'
import { ActionBar } from '@zeal/domains/Transactions/components/ActionBar'
import { SimulateTransactionResponse } from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import { SimulatedTransactionContentHeader } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/SimulatedTransactionContentHeader'
import {
    GasPaymentMethod,
    keystoreToUserEventType,
} from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'
import { GasAbstractionTransactionFee } from '@zeal/domains/UserOperation'

import { SafeTransactionInfo } from '../SafeTransactionInfo'

type Props = {
    userOperationRequest: SignedUserOperationRequest
    selectedFee: GasAbstractionTransactionFee
    simulation: SimulateTransactionResponse
    keyStore: Safe4337
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    visualState: VisualState
    actionSource: ActionSource2
    onMsg: (msg: Msg) => void
}

export type VisualState = { type: 'minimised' } | { type: 'maximised' }

type Msg =
    | MsgOf<typeof ConnectedMinimized>
    | { type: 'on_minimize_click' }
    | { type: 'on_close_bundler_submission_error_popup' }
    | {
          type: 'on_user_operation_submitted_to_bundler'
          userOperationRequest: SubmittedToBundlerUserOperationRequest
      }

const getGasPaymentMethod = (
    selectedFee: GasAbstractionTransactionFee
): GasPaymentMethod => {
    switch (selectedFee.type) {
        case 'sponsored_gas_abstraction_transaction_fee_2':
            return { type: 'sponsored' }
        case 'native_gas_abstraction_transaction_fee':
            return { type: 'native' }
        case 'erc20_gas_abstraction_transaction_fee':
            return {
                type: 'erc20',
                token: selectedFee.feeInTokenCurrency.currency.code,
            }
        /* istanbul ignore next */
        default:
            return notReachable(selectedFee)
    }
}

export const SubmitToBundler = ({
    userOperationRequest,
    simulation,
    accountsMap,
    selectedFee,
    keyStore,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    installationId,
    visualState,
    actionSource,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(
        submitUserOperationRequest,
        {
            type: 'loading',
            params: {
                userOperationRequest,
                installationId,
                networkRPCMap,
                actionSource,
            },
        }
    )

    const onMsgLive = useLiveRef(onMsg)
    const keystoreLive = useLiveRef(keyStore)
    const userOperationRequestLive = useLiveRef(userOperationRequest)
    const selectedFeeLive = useLiveRef(selectedFee)

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
            case 'error':
                break
            case 'loaded':
                postUserEvent({
                    type: 'TransactionSubmittedEvent',
                    keystoreType: keystoreToUserEventType(keystoreLive.current),
                    installationId,
                    network:
                        userOperationRequestLive.current.network.hexChainId,
                    source: actionSource.transactionEventSource,
                    keystoreId: keystoreLive.current.id,
                    gasPaymentMethod: getGasPaymentMethod(
                        selectedFeeLive.current
                    ),
                })

                onMsgLive.current({
                    type: 'on_user_operation_submitted_to_bundler',
                    userOperationRequest: loadable.data,
                })
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [
        installationId,
        keystoreLive,
        loadable,
        onMsgLive,
        selectedFeeLive,
        actionSource.transactionEventSource,
        userOperationRequestLive,
    ])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return (
                <Layout
                    installationId={installationId}
                    simulation={simulation}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    userOperationRequest={userOperationRequest}
                    visualState={visualState}
                    actionSource={actionSource}
                    onMsg={onMsg}
                />
            )

        case 'error':
            return (
                <>
                    <Layout
                        installationId={installationId}
                        simulation={simulation}
                        accountsMap={accountsMap}
                        keyStoreMap={keyStoreMap}
                        networkMap={networkMap}
                        userOperationRequest={userOperationRequest}
                        visualState={visualState}
                        actionSource={actionSource}
                        onMsg={onMsg}
                    />

                    <AppErrorPopup
                        installationId={installationId}
                        error={parseAppError(loadable.error)}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg({
                                        type: 'on_close_bundler_submission_error_popup',
                                    })
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break

                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </>
            )

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}

type LayoutProps = {
    userOperationRequest: SignedUserOperationRequest
    installationId: string
    simulation: SimulateTransactionResponse
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    visualState: VisualState
    actionSource: ActionSource2
    onMsg: (msg: Msg) => void
}

const Layout = ({
    userOperationRequest,
    simulation,
    onMsg,
    accountsMap,
    keyStoreMap,
    networkMap,
    visualState,
    actionSource,
    installationId,
}: LayoutProps) => {
    switch (visualState.type) {
        case 'minimised':
            return (
                <ConnectedMinimized
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )
        case 'maximised':
            return (
                <Screen background="light" padding="form" onNavigateBack={null}>
                    <ActionBar
                        title={
                            <FormattedMessage
                                id="submitSafeTransaction.submittingToRelayer.title"
                                defaultMessage="Transaction result"
                            />
                        }
                        account={userOperationRequest.account}
                        network={userOperationRequest.network}
                        actionSourceType={actionSource.type}
                        onMsg={onMsg}
                    />
                    <Column spacing={12} alignY="stretch">
                        <Content
                            header={
                                <SimulatedTransactionContentHeader
                                    simulatedTransaction={
                                        simulation.transaction
                                    }
                                    dAppInfo={userOperationRequest.dApp}
                                />
                            }
                            footer={
                                <Footer
                                    userOperationRequest={userOperationRequest}
                                    networkMap={networkMap}
                                />
                            }
                        >
                            <SafeTransactionInfo
                                installationId={installationId}
                                simulation={simulation}
                                dApp={userOperationRequest.dApp}
                                accounts={accountsMap}
                                keyStores={keyStoreMap}
                                networkMap={networkMap}
                            />
                        </Content>

                        <Actions variant="default">
                            <Button size="regular" variant="secondary" disabled>
                                <FormattedMessage
                                    id="submitTransaction.cancel"
                                    defaultMessage="Cancel"
                                />
                            </Button>

                            <Button size="regular" variant="secondary" disabled>
                                <FormattedMessage
                                    id="submitTransaction.submit"
                                    defaultMessage="Submit"
                                />
                            </Button>
                        </Actions>
                    </Column>
                </Screen>
            )
        default:
            return notReachable(visualState)
    }
}

const Footer = ({
    userOperationRequest,
    networkMap,
}: {
    userOperationRequest: Props['userOperationRequest']
    networkMap: Props['networkMap']
}) => {
    const { simulationResult } = userOperationRequest
    switch (simulationResult.type) {
        case 'failed':
        case 'not_supported':
            return (
                <Progress
                    title={
                        <FormattedMessage
                            id="submitSafeTransaction.state.sign"
                            defaultMessage="Sending"
                        />
                    }
                    variant="neutral"
                    initialProgress={20}
                    progress={40}
                    right={<Spinner variant="regular" size={16} />}
                />
            )
        case 'simulated': {
            const { transaction, checks } = simulationResult.simulation

            switch (transaction.type) {
                case 'BridgeTrx':
                case 'UnknownTransaction':
                case 'FailedTransaction':
                case 'P2PTransaction':
                case 'P2PNftTransaction':
                case 'WithdrawalTrx':
                case 'CardTopUpTrx':
                case 'deploy_earn_account':
                case 'earn_recharge_configured':
                case 'earn_deposit_with_swap':
                case 'earn_deposit_direct_send':
                case 'earn_withdraw':
                case 'earn_recharge_disabled':
                case 'earn_recharge_updated':
                case 'card_cashback_deposit':
                case 'smart_wallet_activation':
                case 'card_top_up_from_earn':
                case 'swaps_io_native_token_swap':
                    return (
                        <Progress
                            title={
                                <FormattedMessage
                                    id="submit-userop.progress.text"
                                    defaultMessage="Sending"
                                />
                            }
                            variant="neutral"
                            initialProgress={20}
                            progress={40}
                            right={<Spinner variant="regular" size={16} />}
                        />
                    )
                case 'ApprovalTransaction':
                case 'SingleNftApprovalTransaction':
                case 'NftCollectionApprovalTransaction':
                    return (
                        <Column spacing={0}>
                            <Group variant="default">
                                <Column spacing={0}>
                                    <Text
                                        variant="paragraph"
                                        weight="regular"
                                        color="textSecondary"
                                    >
                                        <FormattedMessage
                                            id="check-confirmation.approve.footer.for"
                                            defaultMessage="For"
                                        />
                                    </Text>
                                    <ListItem
                                        safetyChecks={checks}
                                        smartContract={transaction.approveTo}
                                        networkMap={networkMap}
                                    />
                                </Column>
                            </Group>

                            <Progress
                                title={
                                    <FormattedMessage
                                        id="submit-userop.progress.text"
                                        defaultMessage="Sending"
                                    />
                                }
                                variant="neutral"
                                initialProgress={20}
                                progress={40}
                                right={<Spinner variant="regular" size={16} />}
                            />
                        </Column>
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(transaction)
            }
        }
        /* istanbul ignore next */
        default:
            return notReachable(simulationResult)
    }
}
