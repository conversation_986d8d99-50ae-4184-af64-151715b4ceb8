import { FormattedMessage } from 'react-intl'

import { Column } from '@zeal/uikit/Column'
import { Content } from '@zeal/uikit/Content'
import { Group } from '@zeal/uikit/Group'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { OffRampTransactionView } from '@zeal/domains/Currency/domains/BankTransfer/components/OffRampTransactionView'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { ConnectedMinimized } from '@zeal/domains/DApp/domains/ConnectionState/features/ConnectedMinimized'
import { TakerSubtextListItemNoBalance } from '@zeal/domains/Earn/components/TakerSubtextListItemNoBalance'
import { KeyStoreMap, Safe4337 } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { NftCollectionListItem } from '@zeal/domains/NFTCollection/components/NftCollectionListItem'
import { NftListItem } from '@zeal/domains/NFTCollection/components/NftListItem'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { TransactionStatusButton } from '@zeal/domains/SafetyCheck/components/TransactionStatusButton'
import { calculateTransactionSafetyChecksResult } from '@zeal/domains/SafetyCheck/helpers/calculateTransactionSafetyChecksResult'
import { ListItem } from '@zeal/domains/SmartContract/components/ListItem'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { SimulatedUserOperationRequest } from '@zeal/domains/TransactionRequest'
import { ActionBar } from '@zeal/domains/Transactions/components/ActionBar'
import { SimulateTransactionResponse } from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import { BridgeTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/BridgeTrx'
import { CardCashbackDepositTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/CardCashbackDepositTrxView'
import { CardTopUpFromEarnTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/CardTopUpFromEarnTrxView'
import { CardTopUpTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/CardTopUpTrxView'
import { EarnDepositTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/EarnDepositTrxView'
import { EarnRechargeConfiguredTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/EarnRechargeConfiguredTrxView'
import { EarnWithdrawalTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/EarnWithdrawalTrxView'
import { EditableApprove } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/EditableApprove'
import { Failed } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/Failed'
import { P2PTransactionView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/P2PTransactionView'
import { SimulatedTransactionContentHeader } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/SimulatedTransactionContentHeader'
import { SmartWalletActivationTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/SmartWalletActivationTrxView'
import { SwapsIONativeTokenSwapView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/SwapsIONativeTokenSwap'
import { Unknown } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/Unknown'
import { GasAbstractionTransactionFeeResponse } from '@zeal/domains/UserOperation'

import { Footer } from './Footer'

type Props = {
    userOperationRequest: SimulatedUserOperationRequest
    installationId: string
    simulation: SimulateTransactionResponse
    initialFeeForecast: GasAbstractionTransactionFeeResponse
    gasCurrencyPresetMap: GasCurrencyPresetMap
    keyStore: Safe4337
    portfolio: ServerPortfolio2 | null
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    visualState: VisualState
    actionSource: ActionSource2
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type VisualState = { type: 'minimised' } | { type: 'maximised' }

type Msg =
    | { type: 'on_minimize_click' }
    | MsgOf<typeof SafeTransactionFooter>
    | MsgOf<typeof ConnectedMinimized>
    | MsgOf<typeof EditableApprove>
    | MsgOf<typeof Footer>

export const Layout = ({
    userOperationRequest,
    accountsMap,
    initialFeeForecast,
    portfolio,
    keyStoreMap,
    networkMap,
    gasCurrencyPresetMap,
    networkRPCMap,
    onMsg,
    keyStore,
    simulation,
    visualState,
    actionSource,
    installationId,
    defaultCurrencyConfig,
}: Props) => {
    const { network, account, dApp } = userOperationRequest
    const dAppSiteInfo: DAppSiteInfo | null = actionSource.dAppSiteInfo || null

    switch (visualState.type) {
        case 'minimised':
            return (
                <ConnectedMinimized
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )

        case 'maximised':
            return (
                <Screen
                    padding="form"
                    background="light"
                    onNavigateBack={() => onMsg({ type: 'on_minimize_click' })}
                >
                    <ActionBar
                        title={
                            <FormattedMessage
                                id="action_bar_title.transaction_request"
                                defaultMessage="Transaction request"
                            />
                        }
                        account={account}
                        actionSourceType={actionSource.type}
                        network={findNetworkByHexChainId(
                            network.hexChainId,
                            networkMap
                        )}
                        onMsg={onMsg}
                    />

                    <Column spacing={12} alignY="stretch">
                        <Content
                            header={
                                <SimulatedTransactionContentHeader
                                    dAppInfo={dAppSiteInfo}
                                    simulatedTransaction={
                                        simulation.transaction
                                    }
                                />
                            }
                            footer={
                                <SafeTransactionFooter
                                    onMsg={onMsg}
                                    networkMap={networkMap}
                                    simulation={simulation}
                                />
                            }
                        >
                            <SafeTransactionInfo
                                userOperationRequest={userOperationRequest}
                                installationId={installationId}
                                simulation={simulation}
                                dApp={dApp}
                                accounts={accountsMap}
                                keyStores={keyStoreMap}
                                networkMap={networkMap}
                                onMsg={onMsg}
                            />
                        </Content>
                        <Footer
                            actionSource={actionSource}
                            networkMap={networkMap}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            networkRPCMap={networkRPCMap}
                            initialFeeForecast={initialFeeForecast}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            userOperationRequest={userOperationRequest}
                            simulation={simulation}
                            portfolio={portfolio}
                            keyStore={keyStore}
                            installationId={installationId}
                            onMsg={onMsg}
                        />
                    </Column>
                </Screen>
            )

        default:
            return notReachable(visualState)
    }
}

const SafeTransactionFooter = ({
    simulation,
    networkMap,
    onMsg,
}: {
    simulation: SimulateTransactionResponse
    networkMap: NetworkMap
    onMsg: (msg: {
        type: 'on_safety_checks_clicked'
        simulation: SimulateTransactionResponse
    }) => void
}) => {
    const { transaction, currencies, checks } = simulation

    const safetyCheckResult = calculateTransactionSafetyChecksResult(checks)

    switch (transaction.type) {
        case 'ApprovalTransaction':
        case 'SingleNftApprovalTransaction':
        case 'NftCollectionApprovalTransaction':
            return (
                <Column spacing={0}>
                    <Group variant="default">
                        <Column spacing={0}>
                            <Text
                                variant="paragraph"
                                weight="regular"
                                color="textSecondary"
                            >
                                <FormattedMessage
                                    id="simulation.approve.footer.for"
                                    defaultMessage="For"
                                />
                            </Text>
                            <ListItem
                                safetyChecks={checks}
                                smartContract={transaction.approveTo}
                                networkMap={networkMap}
                            />
                        </Column>
                    </Group>

                    <TransactionStatusButton
                        safetyCheckResult={safetyCheckResult}
                        knownCurrencies={currencies}
                        onClick={() =>
                            onMsg({
                                type: 'on_safety_checks_clicked',
                                simulation,
                            })
                        }
                    />
                </Column>
            )
        case 'deploy_earn_account':
        case 'earn_recharge_configured':
        case 'earn_deposit_with_swap':
        case 'earn_deposit_direct_send':
        case 'earn_withdraw':
        case 'earn_recharge_disabled':
        case 'earn_recharge_updated':
        case 'card_cashback_deposit':
        case 'smart_wallet_activation':
        case 'WithdrawalTrx':
        case 'CardTopUpTrx':
        case 'card_top_up_from_earn':
        case 'swaps_io_native_token_swap':
            return null
        case 'UnknownTransaction':
        case 'FailedTransaction':
        case 'P2PTransaction':
        case 'P2PNftTransaction':
        case 'BridgeTrx':
            return (
                <TransactionStatusButton
                    safetyCheckResult={safetyCheckResult}
                    knownCurrencies={currencies}
                    onClick={() =>
                        onMsg({
                            type: 'on_safety_checks_clicked',
                            simulation,
                        })
                    }
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(transaction)
    }
}

const SafeTransactionInfo = ({
    simulation,
    dApp,
    accounts,
    keyStores,
    networkMap,
    onMsg,
    installationId,
    userOperationRequest,
}: {
    userOperationRequest: SimulatedUserOperationRequest
    simulation: SimulateTransactionResponse
    dApp: DAppSiteInfo | null
    accounts: AccountsMap
    keyStores: KeyStoreMap
    networkMap: NetworkMap
    onMsg: (msg: Msg) => void
    installationId: string
}) => {
    const { transaction, checks, currencies } = simulation

    switch (transaction.type) {
        case 'earn_recharge_configured':
        case 'earn_recharge_updated':
            return <EarnRechargeConfiguredTrxView transaction={transaction} />
        case 'earn_recharge_disabled':
            return null
        case 'deploy_earn_account':
            return (
                <TakerSubtextListItemNoBalance
                    taker={transaction.taker}
                    takerApyMap={transaction.takerApyMap}
                />
            )
        case 'swaps_io_native_token_swap':
            return (
                <SwapsIONativeTokenSwapView
                    transaction={transaction}
                    networkMap={networkMap}
                />
            )

        case 'earn_deposit_with_swap':
        case 'earn_deposit_direct_send':
            return <EarnDepositTrxView transaction={transaction} />

        case 'earn_withdraw':
            return <EarnWithdrawalTrxView transaction={transaction} />
        case 'card_cashback_deposit':
            return <CardCashbackDepositTrxView transaction={transaction} />
        case 'BridgeTrx':
            return (
                <BridgeTrxView
                    networkMap={networkMap}
                    transaction={transaction}
                />
            )
        case 'card_top_up_from_earn':
            return <CardTopUpFromEarnTrxView transaction={transaction} />
        case 'WithdrawalTrx':
            return (
                <OffRampTransactionView
                    variant={{ type: 'no_status' }}
                    networkMap={networkMap}
                    withdrawalRequest={transaction.withdrawalRequest}
                />
            )
        case 'ApprovalTransaction':
            // TODO: @Nicvaniek - support approval editing for batched transactions
            if (userOperationRequest.rpcRequests.length !== 1) {
                throw new ImperativeError(
                    'Editing approval is not supported for batched transactions'
                )
            }

            const approvalTx = userOperationRequest.rpcRequests[0]
            return (
                <EditableApprove
                    transaction={transaction}
                    originalEthSendTransaction={approvalTx}
                    checks={checks}
                    knownCurrencies={currencies}
                    onMsg={onMsg}
                />
            )
        case 'UnknownTransaction':
            return (
                <Unknown
                    networkMap={networkMap}
                    checks={checks}
                    knownCurrencies={currencies}
                    transaction={transaction}
                />
            )
        case 'FailedTransaction':
            return <Failed dApp={dApp} transaction={transaction} />
        case 'SingleNftApprovalTransaction':
            return (
                <NftListItem
                    networkMap={networkMap}
                    nft={transaction.nft}
                    checks={checks}
                    rightNode={null}
                />
            )
        case 'NftCollectionApprovalTransaction':
            return (
                <NftCollectionListItem
                    networkMap={networkMap}
                    checks={checks}
                    nftCollection={transaction.nftCollectionInfo}
                />
            )
        case 'P2PTransaction':
        case 'P2PNftTransaction':
            return (
                <P2PTransactionView
                    installationId={installationId}
                    networkMap={networkMap}
                    transaction={transaction}
                    dApp={dApp}
                    knownCurrencies={currencies}
                    checks={checks}
                    accounts={accounts}
                    keystores={keyStores}
                />
            )
        case 'CardTopUpTrx':
            return (
                <CardTopUpTrxView transaction={transaction} checks={checks} />
            )
        case 'smart_wallet_activation':
            return <SmartWalletActivationTrxView transaction={transaction} />
        /* istanbul ignore next */
        default:
            return notReachable(transaction)
    }
}
