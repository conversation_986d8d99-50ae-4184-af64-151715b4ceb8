import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import { useLiveRef } from '@zeal/toolkit/React'

import { AccountsMap } from '@zeal/domains/Account'
import { ConnectedMinimized } from '@zeal/domains/DApp/domains/ConnectionState/features/ConnectedMinimized'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { AppAssociationCheckFailed } from '@zeal/domains/Error/domains/Passkey/components/AppAssociationCheckFailed'
import { ScreenLockMissing } from '@zeal/domains/Error/domains/Passkey/components/ScreenLockMissing'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap, Safe4337 } from '@zeal/domains/KeyStore'
import { SignWithPasskeyPopup } from '@zeal/domains/KeyStore/domains/Passkey/components/SignWithPasskeyPopup'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap } from '@zeal/domains/Network'
import {
    SignedUserOperationRequest,
    SimulatedWithAddOwnerUserOperationRequest,
    SimulatedWithDeploymentBundleUserOperationRequest,
} from '@zeal/domains/TransactionRequest'
import { SimulateTransactionResponse } from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import {
    BoostedUserOperationWithoutSignature,
    UserOperationWithoutSignature,
} from '@zeal/domains/UserOperation'
import { calculateUserOperationHash } from '@zeal/domains/UserOperation/helpers/calculateUserOperationHash'
import { signUserOperationHashWithPassKey } from '@zeal/domains/UserOperation/helpers/signUserOperationHashWithPassKey'

import { LoadingLayout } from '../../LoadingLayout'

type Props = {
    userOperationRequest:
        | SimulatedWithDeploymentBundleUserOperationRequest
        | SimulatedWithAddOwnerUserOperationRequest
    userOperationWithoutSignature:
        | UserOperationWithoutSignature
        | BoostedUserOperationWithoutSignature
    installationId: string
    simulation: SimulateTransactionResponse
    sessionPassword: string
    keyStore: Safe4337
    visualState: VisualState
    actionSource: ActionSource2
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    onMsg: (msg: Msg) => void
}

type VisualState = { type: 'minimised' } | { type: 'maximised' }

type Msg =
    | { type: 'close' }
    | { type: 'on_minimize_click' }
    | {
          type: 'on_safe_transaction_signed'
          userOperationRequest: SignedUserOperationRequest
      }
    | { type: 'on_passkey_modal_close' }
    | MsgOf<typeof ConnectedMinimized>

const sign = async ({
    keyStore,
    userOperationRequest,
    userOperationWithoutSignature,
    sessionPassword,
}: {
    keyStore: Safe4337
    userOperationRequest:
        | SimulatedWithDeploymentBundleUserOperationRequest
        | SimulatedWithAddOwnerUserOperationRequest
    userOperationWithoutSignature:
        | UserOperationWithoutSignature
        | BoostedUserOperationWithoutSignature
    sessionPassword: string
}): Promise<SignedUserOperationRequest> => {
    const userOperationHash = calculateUserOperationHash({
        network: userOperationRequest.network,
        userOperation: userOperationWithoutSignature,
        now: Date.now(),
    })
    const signature = await signUserOperationHashWithPassKey({
        passkey: keyStore.safeDeplymentConfig.passkeyOwner,
        userOperationHash,
        sessionPassword,
    })

    const userOperationWithSignature = (() => {
        switch (userOperationWithoutSignature.type) {
            case 'user_operation_without_signature':
                return {
                    ...userOperationWithoutSignature,
                    type: 'user_operation_with_signature' as const,
                    signature,
                }
            case 'boosted_user_operation_without_signature':
                return {
                    ...userOperationWithoutSignature,
                    type: 'boosted_user_operation_with_signature' as const,
                    signature,
                }
            default:
                return notReachable(userOperationWithoutSignature)
        }
    })()

    return {
        ...userOperationRequest,
        type: 'signed_safe_user_operation_request',
        userOperationWithSignature,
    }
}

export const SignUserOperationWithPassKey = ({
    userOperationRequest,
    userOperationWithoutSignature,
    simulation,
    onMsg,
    keyStore,
    sessionPassword,
    visualState,
    actionSource,
    installationId,
    keyStoreMap,
    networkMap,
    accountsMap,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(sign, {
        type: 'loading',
        params: {
            keyStore,
            userOperationRequest,
            sessionPassword,
            userOperationWithoutSignature,
        },
    })

    const onMsgLive = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
                break
            case 'loaded':
                onMsgLive.current({
                    type: 'on_safe_transaction_signed',
                    userOperationRequest: loadable.data,
                })
                break
            case 'error':
                const error = parseAppError(loadable.error)
                switch (error.type) {
                    case 'passkey_operation_cancelled':
                        onMsgLive.current({ type: 'on_passkey_modal_close' })
                        break
                    /* istanbul ignore next */
                    default:
                        break
                }
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, onMsgLive])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            switch (ZealPlatform.OS) {
                case 'ios':
                case 'android':
                    return (
                        <LoadingLayout
                            initialProgress={20}
                            installationId={installationId}
                            simulation={simulation}
                            userOperationRequest={userOperationRequest}
                            visualState={visualState}
                            actionSource={actionSource}
                            networkMap={networkMap}
                            keyStoreMap={keyStoreMap}
                            accountsMap={accountsMap}
                            onMsg={onMsg}
                        />
                    )
                case 'web':
                    return (
                        <>
                            <LoadingLayout
                                initialProgress={20}
                                installationId={installationId}
                                simulation={simulation}
                                userOperationRequest={userOperationRequest}
                                visualState={visualState}
                                actionSource={actionSource}
                                networkMap={networkMap}
                                keyStoreMap={keyStoreMap}
                                accountsMap={accountsMap}
                                onMsg={onMsg}
                            />
                            <SignWithPasskeyPopup onMsg={onMsg} />
                        </>
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(ZealPlatform)
            }
        case 'error':
            const error = parseAppError(loadable.error)

            switch (error.type) {
                case 'passkey_operation_cancelled':
                    return null
                case 'passkey_app_not_associated_with_domain':
                case 'passkey_android_cannot_validate_incoming_request':
                    return (
                        <>
                            <LoadingLayout
                                initialProgress={20}
                                installationId={installationId}
                                simulation={simulation}
                                userOperationRequest={userOperationRequest}
                                visualState={visualState}
                                actionSource={actionSource}
                                networkMap={networkMap}
                                keyStoreMap={keyStoreMap}
                                accountsMap={accountsMap}
                                onMsg={onMsg}
                            />
                            <AppAssociationCheckFailed
                                error={error}
                                variant="passkey_signing"
                                network={userOperationRequest.network}
                                installationId={installationId}
                                location="transaction_signing"
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        case 'on_try_again_clicked':
                                            setLoadable({
                                                type: 'loading',
                                                params: loadable.params,
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        </>
                    )

                case 'passkey_screen_lock_missing':
                    return (
                        <>
                            <LoadingLayout
                                initialProgress={20}
                                installationId={installationId}
                                simulation={simulation}
                                userOperationRequest={userOperationRequest}
                                visualState={visualState}
                                actionSource={actionSource}
                                networkMap={networkMap}
                                keyStoreMap={keyStoreMap}
                                accountsMap={accountsMap}
                                onMsg={onMsg}
                            />
                            <ScreenLockMissing
                                installationId={installationId}
                                location="transaction_signing"
                                error={error}
                                onMsg={onMsg}
                            />
                        </>
                    )
                /* istanbul ignore next */
                default:
                    return (
                        <>
                            <LoadingLayout
                                initialProgress={20}
                                installationId={installationId}
                                simulation={simulation}
                                userOperationRequest={userOperationRequest}
                                visualState={visualState}
                                actionSource={actionSource}
                                networkMap={networkMap}
                                keyStoreMap={keyStoreMap}
                                accountsMap={accountsMap}
                                onMsg={onMsg}
                            />
                            <AppErrorPopup
                                installationId={installationId}
                                error={error}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break

                                        case 'try_again_clicked':
                                            setLoadable({
                                                type: 'loading',
                                                params: loadable.params,
                                            })
                                            break

                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        </>
                    )
            }
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
