import { post } from '@zeal/api/request'

import { notReachable } from '@zeal/toolkit'
import {
    array,
    bigint,
    combine,
    match,
    number,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'

import { fetchStaticCurrencies } from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import { parseKnownCurrencies } from '@zeal/domains/Currency/helpers/parse'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { Network, NetworkMap } from '@zeal/domains/Network'
import { SignMessageRequest } from '@zeal/domains/RPCRequest'
import {
    PermitAllowance,
    SignMessageSimulationResponse,
    SignMessageSimulationResult,
    SimulatedSignMessage,
} from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'
import { parseSmartContract } from '@zeal/domains/SmartContract/parsers/parseSmartContract'
import { parseApprovalAmount } from '@zeal/domains/Transactions/helpers/parseActivityTransaction'

import { parseSimulatedSignMessage } from '../parsers/parseSimulatedSignMessage'

export type FetchSimulatedSignMessage = (params: {
    request: SignMessageRequest
    network: Network
    networkMap: NetworkMap
    dApp: DAppSiteInfo | null
}) => Promise<SignMessageSimulationResult>

/**
 * @deprecated
 * TODO @resetko-zeal kill when BE endpoint is not used
 */
const parsePermitAllowance = (
    input: unknown,
    knownCurrencies: unknown
): Result<unknown, PermitAllowance> =>
    object(input).andThen((obj) =>
        shape({
            amount: parseApprovalAmount(obj.amount, knownCurrencies),
            expiration: object(obj.expiration).andThen((expirationObj) =>
                oneOf(expirationObj, [
                    shape({
                        type: match(
                            expirationObj.type,
                            'FiniteExpiration' as const
                        ),
                        timestamp: number(expirationObj.timestamp),
                    }),
                    shape({
                        type: match(
                            expirationObj.type,
                            'InfiniteExpiration' as const
                        ),
                    }),
                ])
            ),
            unlimitedAmountValue: bigint(obj.unlimitedAmountValue),
            infiniteExpirationValue: bigint(obj.infiniteExpirationValue),
        })
    )

/**
 * @deprecated
 * TODO @resetko-zeal kill when BE endpoint is not used`
 */
const parseSignMessageSimulationResponse = (input: {
    currencies: unknown
    checks: SignMessageSimulationResponse['checks']
    message: unknown
}): Result<unknown, SignMessageSimulationResponse> =>
    shape({
        currencies: parseKnownCurrencies(input.currencies),
        checks: success(input.checks),
        message: object(input.message).andThen((messageDto) =>
            oneOf(messageDto, [
                shape({
                    type: match(messageDto.type, 'UnknownSignMessage' as const),
                    rawMessage: string(messageDto.rawMessage),
                }),
                shape({
                    type: match(messageDto.type, 'PermitSignMessage' as const),
                    approveTo: parseSmartContract(messageDto.approveTo),
                    allowance: parsePermitAllowance(
                        messageDto.allowance,
                        input.currencies
                    ),
                }),
                shape({
                    type: match(
                        messageDto.type,
                        'DaiPermitSignMessage' as const
                    ),
                    approveTo: parseSmartContract(messageDto.approveTo),
                    allowance: parsePermitAllowance(
                        messageDto.allowance,
                        input.currencies
                    ),
                }),
                shape({
                    type: match(messageDto.type, 'Permit2SignMessage' as const),
                    approveTo: parseSmartContract(messageDto.approveTo),
                    allowances: array(messageDto.allowances).andThen((arr) =>
                        combine(
                            arr.map((allowance) =>
                                parsePermitAllowance(
                                    allowance,
                                    input.currencies
                                )
                            )
                        )
                    ),
                }),
            ])
        ),
    })

/**
 * TODO @resetko-zeal current types are more designed to get full response from BE, hence we might need to rework types a little bit to avoid this patching,
 * so we fetch safetychecks from providers, we fetch smart contract info, we parse messages, and then combine those into final payload
 * This may require intemediate type coming from simulation, which won't have smart contract data.
 * https://linear.app/zeal/issue/ZEAL-2719
 */
const patchSmartContractInfo = ({
    fetchedMessage,
    parsedMessage,
}: {
    parsedMessage: SimulatedSignMessage
    fetchedMessage: SimulatedSignMessage
}): SimulatedSignMessage => {
    switch (parsedMessage.type) {
        case 'PermitSignMessage':
        case 'DaiPermitSignMessage':
        case 'Permit2SignMessage':
            if (
                parsedMessage.type === fetchedMessage.type &&
                parsedMessage.approveTo.address ===
                    fetchedMessage.approveTo.address
            ) {
                return {
                    ...parsedMessage,
                    approveTo: fetchedMessage.approveTo,
                }
            }

            return parsedMessage
        case 'OrderCardTopupSignMessage':
        case 'OrderEarnDepositBridge':
        case 'OrderBuySignMessage':
        case 'UnknownSignMessage':
            return parsedMessage

        default:
            return notReachable(parsedMessage)
    }
}

export const fetchSimulatedSignMessage = async ({
    network,
    request,
    dApp,
    networkMap,
}: {
    request: SignMessageRequest
    network: Network
    networkMap: NetworkMap
    dApp: DAppSiteInfo | null
}): Promise<SignMessageSimulationResult> => {
    const currencies = await fetchStaticCurrencies()
    switch (network.type) {
        case 'predefined':
        case 'testnet':
            return post('/wallet/rpc-sign-message/simulate/', {
                body: (() => {
                    switch (request.method) {
                        case 'personal_sign':
                        case 'eth_signTypedData':
                        case 'eth_signTypedData_v3':
                        case 'eth_signTypedData_v4':
                            return request
                        default:
                            return notReachable(request)
                    }
                })(),
                query: { network: network.name },
                requestSource: dApp?.hostname,
            })
                .then((response) =>
                    parseSignMessageSimulationResponse(
                        response
                    ).getSuccessResultOrThrow(
                        'failed to parse sign messagesimulation'
                    )
                )
                .then((response: SignMessageSimulationResponse) => {
                    try {
                        // TODO @resetko-zeal remove safetynet once QA done
                        const now = Date.now()
                        const parsedMessage = parseSimulatedSignMessage({
                            input: request,
                            knownCurrencies: currencies,
                            networkMap,
                            now,
                        }).getSuccessResultOrThrow(
                            'issues in SignMessage parsing'
                        )

                        const message = patchSmartContractInfo({
                            fetchedMessage: response.message,
                            parsedMessage,
                        })

                        return {
                            checks: response.checks,
                            currencies,
                            message,
                        }
                    } catch (error) {
                        captureError(error)
                        return response
                    }
                })
                .then((response) => ({
                    type: 'simulated' as const,
                    simulationResponse: response,
                }))
                .catch((e) => {
                    captureError(e)
                    return { type: 'failed' }
                })

        case 'custom':
            return { type: 'not_supported' }

        default:
            return notReachable(network)
    }
}
