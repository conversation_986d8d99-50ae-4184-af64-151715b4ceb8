import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Divider } from '@zeal/uikit/Divider'
import { FancyButton } from '@zeal/uikit/FancyButton'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Spacer } from '@zeal/uikit/Spacer'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AvatarWithoutBadge as AccountAvatarWithoutBadge } from '@zeal/domains/Account/components/Avatar'
import { CryptoCurrency, Currency } from '@zeal/domains/Currency'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { MaxButton } from '@zeal/domains/Currency/components/MaxButton'
import { SwapsIOQuote } from '@zeal/domains/Currency/domains/SwapsIO'
import { TransferProvider } from '@zeal/domains/Currency/domains/SwapsIO/components/TransferProvider'
import { Taker, TakerPortfolio } from '@zeal/domains/Earn'
import { TakerAvatar } from '@zeal/domains/Earn/components/TakerAvatar'
import { TakerTitle } from '@zeal/domains/Earn/components/TakerTitle'
import { FXRate2 } from '@zeal/domains/FXRate'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { useMoneyFormat } from '@zeal/domains/Money/hooks/useMoneyFormat'
import { NetworkMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'

import {
    FormError,
    getMaxBalance,
    MaxBalanceLoadable,
    Pollable,
    validate,
} from './validation'

type Props = {
    takerPortfolio: TakerPortfolio
    fromAccountPortfolio: ServerPortfolio2
    networkMap: NetworkMap
    pollable: Pollable
    maxBalanceLoadable: MaxBalanceLoadable
    installationId: string
    takerUserCurrencyRate: FXRate2<CryptoCurrency, Currency>

    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_select_from_currency_click' }
    | { type: 'on_amount_change'; amount: string | null }
    | {
          type: 'on_deposit_submited'
          swapsIOQuote: SwapsIOQuote
      }
    | { type: 'on_earn_account_click'; taker: Taker }
    | { type: 'on_from_account_click' }
    | MsgOf<typeof MaxButton>

export const Layout = ({
    pollable,
    takerPortfolio,
    installationId,
    fromAccountPortfolio,
    maxBalanceLoadable,
    networkMap,
    takerUserCurrencyRate,
    onMsg,
}: Props) => {
    const { formatMessage } = useIntl()
    const { formatForInputs } = useMoneyFormat()

    const validationResult = validate({
        pollable,
        maxBalanceLoadable,
        portfolio: fromAccountPortfolio,
    })

    const errors = validationResult.getFailureReason() || {}

    const { fromCurrency } = pollable.params

    const fromToken = getTokenByCryptoCurrency3({
        serverPortfolio: fromAccountPortfolio,
        currency: fromCurrency,
    })

    const from: CryptoMoney = {
        amount: fromFixedWithFraction(
            pollable.params.amount,
            fromCurrency.fraction
        ),
        currency: fromCurrency,
    }

    const maxBalance = getMaxBalance({
        maxBalanceLoadable,
        portfolio: fromAccountPortfolio,
    })

    const isMaxAmount = from.amount === maxBalance.amount

    const fromAmountInDefaultCurrency: FiatMoney | null =
        fromToken.rate && pollable.params.amount
            ? applyRate2({
                  baseAmount: {
                      amount: fromFixedWithFraction(
                          pollable.params.amount,
                          fromCurrency.fraction
                      ),
                      currency: fromCurrency,
                  },
                  rate: fromToken.rate,
              })
            : null

    const quote = (() => {
        switch (pollable.type) {
            case 'loaded':
            case 'reloading':
                return pollable.data.getSuccessResult() || null

            case 'subsequent_failed':
            case 'loading':
            case 'error':
                return null
            /* istanbul ignore next */
            default:
                return notReachable(pollable)
        }
    })()

    const taker = pollable.params.taker

    const toAmount: CryptoMoney = {
        amount: quote?.to.amount ?? 0n,
        currency: taker.cryptoCurrency,
    }

    const toAmountInUserCurrency = applyRate2({
        baseAmount: toAmount,
        rate: takerUserCurrencyRate,
    })

    const takerBalanceInUserCurrency = applyRate2({
        baseAmount: takerPortfolio.assetBalance,
        rate: takerUserCurrencyRate,
    })

    const formattedAmountForInput = formatForInputs({
        money: from,
    })
    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} fill>
                <ActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <ActionBar.Header>
                                    <FormattedMessage
                                        id="earn.desposit_form.title"
                                        defaultMessage="Deposit into Earn"
                                    />
                                </ActionBar.Header>
                            </Row>
                        </Clickable>
                    }
                />

                <Column spacing={0} fill alignY="stretch">
                    <Column spacing={4} fill>
                        <AmountInput
                            top={
                                <Column spacing={0}>
                                    <FancyButton
                                        color="secondary"
                                        rounded
                                        left={() => (
                                            <Row grow shrink spacing={4}>
                                                <AccountAvatarWithoutBadge
                                                    size={12}
                                                    account={
                                                        pollable.params
                                                            .fromAccount
                                                    }
                                                />
                                                <Text
                                                    variant="caption1"
                                                    color="gray40"
                                                    weight="medium"
                                                    ellipsis
                                                >
                                                    {
                                                        pollable.params
                                                            .fromAccount.label
                                                    }
                                                </Text>
                                            </Row>
                                        )}
                                        right={({ color }) => (
                                            <LightArrowDown2
                                                size={16}
                                                color={color}
                                            />
                                        )}
                                        onClick={() => {
                                            onMsg({
                                                type: 'on_from_account_click',
                                            })
                                        }}
                                    />
                                    <Divider variant="secondary" />
                                </Column>
                            }
                            content={{
                                topLeft: (
                                    <IconButton
                                        variant="on_light"
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_select_from_currency_click',
                                            })
                                        }
                                    >
                                        {({ color }) => (
                                            <Row spacing={8}>
                                                <CurrencyAvatar
                                                    key={fromCurrency.id}
                                                    rightBadge={({ size }) => (
                                                        <Badge
                                                            size={size}
                                                            network={findNetworkByHexChainId(
                                                                fromCurrency.networkHexChainId,
                                                                networkMap
                                                            )}
                                                        />
                                                    )}
                                                    currency={fromCurrency}
                                                    size={32}
                                                />
                                                <Row spacing={4}>
                                                    <Text
                                                        ellipsis
                                                        variant="title3"
                                                        color="textPrimary"
                                                        weight="medium"
                                                    >
                                                        {fromCurrency.code}
                                                    </Text>

                                                    <LightArrowDown2
                                                        size={20}
                                                        color={color}
                                                    />
                                                </Row>
                                            </Row>
                                        )}
                                    </IconButton>
                                ),
                                topRight: ({ onBlur, onFocus, isFocused }) => (
                                    <AmountInput.Input
                                        onBlur={onBlur}
                                        onFocus={onFocus}
                                        onChange={(value) =>
                                            onMsg({
                                                type: 'on_amount_change',
                                                amount: value,
                                            })
                                        }
                                        label={formatMessage({
                                            id: 'earn.deposit.amount_to_deposit',
                                            defaultMessage: 'Amount to deposit',
                                        })}
                                        prefix=""
                                        fraction={fromCurrency.fraction}
                                        autoFocus
                                        amount={
                                            !isFocused && isMaxAmount
                                                ? formattedAmountForInput
                                                : pollable.params.amount
                                        }
                                        onSubmitEditing={noop}
                                    />
                                ),
                                bottomRight: fromAmountInDefaultCurrency && (
                                    <Text
                                        variant="footnote"
                                        color="textSecondary"
                                        weight="regular"
                                    >
                                        <FormattedMoneyPrecise
                                            withSymbol
                                            sign={null}
                                            money={fromAmountInDefaultCurrency}
                                        />
                                    </Text>
                                ),
                                bottomLeft: (
                                    <MaxButton
                                        installationId={installationId}
                                        location="earn_deposit"
                                        balance={maxBalance}
                                        onMsg={onMsg}
                                        state={(() => {
                                            switch (maxBalanceLoadable.type) {
                                                case 'loading':
                                                    return 'loading'
                                                case 'error':
                                                case 'loaded':
                                                    return errors.fromToken
                                                        ? 'error'
                                                        : 'normal'
                                                default:
                                                    return notReachable(
                                                        maxBalanceLoadable
                                                    )
                                            }
                                        })()}
                                    />
                                ),
                            }}
                            state={errors.fromToken ? 'error' : 'normal'}
                        />

                        <NextStepSeparator />

                        <AmountInput
                            content={{
                                topRight: () => (
                                    <Row spacing={0}>
                                        <Spacer />
                                        <Text
                                            variant="title3"
                                            weight="medium"
                                            color="textPrimary"
                                        >
                                            <FormattedMoneyPrecise
                                                withSymbol={false}
                                                sign={null}
                                                money={toAmountInUserCurrency}
                                            />
                                        </Text>
                                    </Row>
                                ),
                                topLeft: (
                                    <IconButton
                                        variant="on_light"
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_earn_account_click',
                                                taker: taker,
                                            })
                                        }
                                    >
                                        {({ color }) => (
                                            <Row shrink spacing={8}>
                                                <TakerAvatar
                                                    size={32}
                                                    takerType={taker.type}
                                                />
                                                <Row spacing={4}>
                                                    <Text
                                                        variant="title3"
                                                        color="gray20"
                                                        weight="medium"
                                                    >
                                                        <TakerTitle
                                                            takerType={
                                                                taker.type
                                                            }
                                                        />
                                                    </Text>
                                                    <LightArrowDown2
                                                        size={20}
                                                        color={color}
                                                    />
                                                </Row>
                                            </Row>
                                        )}
                                    </IconButton>
                                ),
                                bottomRight: quote &&
                                    quote.toInDefaultCurrency && (
                                        <Text
                                            variant="footnote"
                                            color="textSecondary"
                                            weight="regular"
                                        >
                                            <FormattedMoneyPrecise
                                                withSymbol
                                                sign={null}
                                                money={
                                                    quote.toInDefaultCurrency
                                                }
                                            />
                                        </Text>
                                    ),
                                bottomLeft: (
                                    <Text
                                        color="textSecondary"
                                        variant="paragraph"
                                        weight="regular"
                                    >
                                        <FormattedMessage
                                            id="currency.swap.max_label"
                                            defaultMessage="Balance: {amount}"
                                            values={{
                                                amount: (
                                                    <FormattedMoneyPrecise
                                                        withSymbol={false}
                                                        sign={null}
                                                        money={
                                                            takerBalanceInUserCurrency
                                                        }
                                                    />
                                                ),
                                            }}
                                        />
                                    </Text>
                                ),
                            }}
                            state="normal"
                        />
                    </Column>

                    <Column spacing={16}>
                        <TransferProvider pollable={pollable} />

                        <Actions variant="default">
                            <Button
                                variant="primary"
                                size="regular"
                                disabled={!!errors.submit}
                                loading={(() => {
                                    switch (errors.submit?.type) {
                                        case 'pollable_reloading':
                                        case 'max_balance_still_loading':
                                            return true

                                        case undefined:
                                        case 'pollable_subsequent_failed':
                                        case 'amount_required':
                                        case 'not_enough_balance':
                                        case 'selected_route_is_no_more_available':
                                        case 'no_routes_found':
                                        case 'above_maximum_limit':
                                        case 'below_minimum_limit':
                                            return false
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(errors.submit)
                                    }
                                })()}
                                onClick={() => {
                                    validationResult.tap(
                                        (earnDepositRequest) => {
                                            onMsg({
                                                type: 'on_deposit_submited',
                                                swapsIOQuote:
                                                    earnDepositRequest,
                                            })
                                        }
                                    )
                                }}
                            >
                                <CTAMessage error={errors.submit} />
                            </Button>
                        </Actions>
                    </Column>
                </Column>
            </Column>
        </Screen>
    )
}

const CTAMessage = ({ error }: { error?: FormError['submit'] }) => {
    if (!error) {
        return (
            <FormattedMessage
                id="earn.deposit.deposit"
                defaultMessage="Deposit"
            />
        )
    }

    switch (error.type) {
        case 'pollable_reloading':
        case 'max_balance_still_loading':
            return null
        case 'pollable_subsequent_failed':
            return (
                <FormattedMessage
                    id="earn.deposit.deposit"
                    defaultMessage="Deposit"
                />
            )
        case 'amount_required':
            return (
                <FormattedMessage
                    id="earn.deposit.enter_amount"
                    defaultMessage="Enter Amount"
                />
            )
        case 'not_enough_balance':
            return (
                <FormattedMessage
                    id="earn.deposit.not_enough_balance"
                    defaultMessage="Not enough balance"
                />
            )
        case 'selected_route_is_no_more_available':
        case 'no_routes_found':
            return (
                <FormattedMessage
                    id="earn.deposit.no_routes_found"
                    defaultMessage="No routes found"
                />
            )

        case 'above_maximum_limit':
            return (
                <FormattedMessage
                    id="submit.error.amount_high"
                    defaultMessage="Amount too high"
                />
            )

        case 'below_minimum_limit':
            return (
                <FormattedMessage
                    id="submit.error.amount_low"
                    defaultMessage="Amount too low"
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(error)
    }
}
