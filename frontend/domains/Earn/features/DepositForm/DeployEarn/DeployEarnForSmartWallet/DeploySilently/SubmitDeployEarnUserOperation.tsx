import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { Earn, NotDeployedTaker } from '@zeal/domains/Earn'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { createAccountDeployTransaction } from '@zeal/domains/Earn/helpers/createAccountDeployTransaction'
import { createCoordinatorDeployTransaction } from '@zeal/domains/Earn/helpers/createCoordinatorDeployTransaction'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { fetchIsContractDeployed } from '@zeal/domains/RPCRequest/api/fetchIsContractDeployed'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { fetchSimulationByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import { submitAndMonitorSponsoredUserOperation } from '@zeal/domains/UserOperation/api/submitAndMonitorSponsoredUserOperation'

type Props = {
    earn: Earn
    owner: Account
    taker: NotDeployedTaker
    sessionPassword: string
    keyStore: Safe4337
    network: Network
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'deploy_earn_user_operation_completed'
          owner: Web3.address.Address
      }
    | { type: 'close' }

const submitDeploymentUserOperation = async ({
    defaultCurrencyConfig,
    earn,
    installationId,
    keyStore,
    network,
    networkMap,
    networkRPCMap,
    owner,
    sessionPassword,
    taker,
    signal,
}: {
    earn: Earn
    owner: Account
    taker: NotDeployedTaker
    sessionPassword: string
    keyStore: Safe4337
    network: Network
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): ReturnType<typeof submitAndMonitorSponsoredUserOperation> => {
    switch (earn.type) {
        case 'not_configured':
            return submitAndMonitorSponsoredUserOperation({
                sessionPassword,
                account: owner,
                dApp: null,
                defaultCurrencyConfig,
                fetchSimulationByRequest: fetchSimulationByRequest,
                installationId,
                keyStore,
                network,
                networkMap,
                networkRPCMap,
                portfolio: null,
                rpcRequestsToBundle: [
                    createCoordinatorDeployTransaction({
                        owner: owner.address,
                        takerType: taker.type,
                        fromAddress: owner.address,
                    }),
                ],
                actionSource: {
                    type: 'internal',
                    transactionEventSource: 'earnHolderDeploy',
                },
                signal,
            })

        case 'configured':
            return submitAndMonitorSponsoredUserOperation({
                sessionPassword,
                account: owner,
                dApp: null,
                defaultCurrencyConfig,
                fetchSimulationByRequest: fetchSimulationByRequest,
                installationId,
                keyStore,
                network,
                networkMap,
                networkRPCMap,
                portfolio: null,
                rpcRequestsToBundle: [
                    createAccountDeployTransaction({
                        earn,
                        owner: owner.address,
                        takerType: taker.type,
                    }),
                ],
                actionSource: {
                    type: 'internal',
                    transactionEventSource: 'earnTakerDeploy',
                },
                signal,
            })

        /* istanbul ignore next */
        default:
            return notReachable(earn)
    }
}

export const fetch = async ({
    earn,
    keyStore,
    network,
    networkRPCMap,
    owner,
    sessionPassword,
    installationId,
    taker,
    networkMap,
    defaultCurrencyConfig,
    signal,
}: {
    earn: Earn
    owner: Account
    taker: NotDeployedTaker
    sessionPassword: string
    keyStore: Safe4337
    network: Network
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<void> => {
    /*
        We need this check as the addAccount call doesn't fail if taker is already deployed
        and corupts the coordinator state
    */
    const takerIsDeployed = await fetchIsContractDeployed({
        address: taker.address,
        network: EARN_NETWORK,
        networkRPCMap,
        signal,
    })

    if (takerIsDeployed) {
        return
    }

    const submittedUserOperation = await submitDeploymentUserOperation({
        earn,
        owner,
        taker,
        keyStore,
        network,
        networkRPCMap,
        networkMap,
        installationId,
        defaultCurrencyConfig,
        sessionPassword,
        signal,
    })

    switch (submittedUserOperation.state) {
        case 'completed':
            return

        case 'rejected':
        case 'failed':
            // FIXME @resetko-zeal we have proper error to throw here
            throw new ImperativeError(
                'Failed to deploy holder and taker during silent earn deployment'
            )

        default:
            return notReachable(submittedUserOperation)
    }
}

export const SubmitDeployEarnUserOperation = ({
    earn,
    owner,
    taker,
    sessionPassword,
    keyStore,
    network,
    networkRPCMap,
    defaultCurrencyConfig,
    installationId,
    networkMap,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            earn,
            owner,
            taker,
            keyStore,
            sessionPassword,
            network,
            networkRPCMap,
            defaultCurrencyConfig,
            installationId,
            networkMap,
        },
    })

    const onLiveMsgRef = useLiveRef(onMsg)
    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
            case 'error':
                break

            case 'loaded':
                onLiveMsgRef.current({
                    type: 'deploy_earn_user_operation_completed',
                    owner: owner.address,
                })
                break

            /* istanbul ignore next */
            default:
                notReachable(loadable)
        }
    }, [onLiveMsgRef, loadable, owner])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return (
                <LoadingLayout
                    title={
                        <FormattedMessage
                            id="deploy-earn-form-smart-wallet.in-progress.title"
                            defaultMessage="Preparing Earn"
                        />
                    }
                    onClose={null}
                    actionBar={null}
                />
            )

        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={
                            <FormattedMessage
                                id="deploy-earn-form-smart-wallet.in-progress.title"
                                defaultMessage="Preparing Earn"
                            />
                        }
                        onClose={null}
                        actionBar={null}
                    />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break

                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )

        /* istanbul ignore next */
        default:
            return null
    }
}
