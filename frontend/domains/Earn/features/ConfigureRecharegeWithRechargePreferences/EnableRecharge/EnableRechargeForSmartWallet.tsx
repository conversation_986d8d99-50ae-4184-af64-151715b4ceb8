import { useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'
import { SuccessLayout } from '@zeal/uikit/SuccessLayout'

import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardSafeFullyConfigured,
    ReadonlySignerSelectedOnboardedCardConfig,
    RechargePreferences,
} from '@zeal/domains/Card'
import { createDeployEarnWithRechargePreferencesTransactions } from '@zeal/domains/Card/helpers/createDeployEarnWithRechargePreferencesTransactions'
import { convertStableCoinCurrencyToFiatCurrency } from '@zeal/domains/Currency/helpers/convertStableCoinCurrencyToFiatCurrency'
import { ConfiguredEarn, Earn } from '@zeal/domains/Earn'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { fetchSimulationByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import { submitAndMonitorSponsoredUserOperation } from '@zeal/domains/UserOperation/api/submitAndMonitorSponsoredUserOperation'

import { getConfiguredEarnWithEnabledRecharge } from '../helpers/getConfiguredEarnWithEnabledRecharge'

type Msg =
    | { type: 'close' }
    | {
          type: 'on_reacharege_configured_with_user_preferences'
          earn: ConfiguredEarn
      }

type Props = {
    earn: Earn
    networkMap: NetworkMap
    keyStore: Safe4337
    accountsMap: AccountsMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    sessionPassword: string
    installationId: string
    rechargePreferences: RechargePreferences
    cardSafe: CardSafeFullyConfigured
    onMsg: (msg: Msg) => void
}

export const EnableRechargeForSmartWallet = ({
    earn,
    networkMap,
    accountsMap,
    keyStore,
    networkRPCMap,
    defaultCurrencyConfig,
    sessionPassword,
    installationId,
    rechargePreferences,
    cardSafe,
    cardConfig,
    onMsg,
}: Props) => {
    const [{ configureRechargeTransaction, configureEarnTransaction }] =
        useState(
            createDeployEarnWithRechargePreferencesTransactions({
                cardReadOnlySigner: cardConfig.readonlySignerAddress,
                accountsMap,
                cardSafe: {
                    type: 'fully_configured',
                    address: cardConfig.lastSeenSafeAddress,
                    fiatCurrency: convertStableCoinCurrencyToFiatCurrency({
                        cryptoCurrency: cardConfig.currency,
                    }),
                    cryptoCurrency: cardConfig.currency,
                },
                earn,
                rechargePreferences,
            })
        )

    const [loadable, setLoadable] = useLoadableData(
        submitAndMonitorSponsoredUserOperation,
        {
            type: 'loading',
            params: {
                sessionPassword,
                account: accountsMap[cardConfig.readonlySignerAddress],
                dApp: null,
                defaultCurrencyConfig,
                fetchSimulationByRequest: fetchSimulationByRequest,
                installationId,
                keyStore,
                network: EARN_NETWORK,
                networkMap,
                networkRPCMap,
                portfolio: null,
                rpcRequestsToBundle: [
                    configureEarnTransaction,
                    configureRechargeTransaction,
                ].filter(excludeNullValues),
                actionSource: {
                    type: 'internal',
                    transactionEventSource: 'earnDeployWithRechargePreferences',
                } as const,
            },
        }
    )

    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    title={
                        <FormattedMessage
                            id="enable-recharge-for-smart-wallet.enabling-recharge.title"
                            defaultMessage="Enabling recharge"
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )

        case 'error':
            return (
                <>
                    <LoadingLayout
                        actionBar={
                            <ActionBar
                                left={
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => onMsg({ type: 'close' })}
                                    >
                                        {({ color }) => (
                                            <BackIcon size={24} color={color} />
                                        )}
                                    </IconButton>
                                }
                            />
                        }
                        title={
                            <FormattedMessage
                                id="enable-recharge-for-smart-wallet.enabling-recharge.title"
                                defaultMessage="Enabling recharge"
                            />
                        }
                        onClose={() => onMsg({ type: 'close' })}
                    />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        case 'loaded':
            return (
                <SuccessLayout
                    title={
                        <FormattedMessage
                            id="enable-recharge-for-smart-wallet.recharge-enabled.title"
                            defaultMessage="Recharge enabled"
                        />
                    }
                    onAnimationComplete={() => {
                        onMsg({
                            type: 'on_reacharege_configured_with_user_preferences',
                            earn: getConfiguredEarnWithEnabledRecharge({
                                earn,
                                rechargePreferences,
                                cardSafe,
                            }),
                        })
                    }}
                />
            )
        default:
            return notReachable(loadable)
    }
}
