import { useState } from 'react'

import { LanguageSettings } from '@zeal/uikit/Language'
import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'

import { notReachable } from '@zeal/toolkit'
import { LoadedReloadableData } from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    FetchPortfolioRequest,
    FetchPortfolioResponse,
} from '@zeal/domains/Account/api/fetchAccounts'
import { CardConfig } from '@zeal/domains/Card'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SubmittedOfframpTransaction } from '@zeal/domains/Currency/domains/BankTransfer'
import { SubmitedBridgesMap } from '@zeal/domains/Currency/domains/Bridge'
import { SwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { ConnectionMap } from '@zeal/domains/DApp/domains/ConnectionState'
import { WalletConnectInstanceLoadable } from '@zeal/domains/DApp/domains/WalletConnect/api/fetchWalletConnectInstance'
import {
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
    TotalEarningsInDefaultCurrencyMap,
} from '@zeal/domains/Earn'
import { AppRating } from '@zeal/domains/Feedback'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Mode } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    BankTransferInfo,
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { Submited } from '@zeal/domains/TransactionRequest'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    account: Account
    accounts: AccountsMap
    keystoreMap: KeyStoreMap
    submitedBridgesMap: SubmitedBridgesMap
    submittedOffRampTransactions: SubmittedOfframpTransaction[]
    transactionRequests: Record<Web3.address.Address, Submited[]>
    portfolioMap: PortfolioMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    portfolioLoadable: LoadedReloadableData<
        FetchPortfolioResponse,
        FetchPortfolioRequest
    >
    networkMap: NetworkMap
    bankTransferInfo: BankTransferInfo
    isEthereumNetworkFeeWarningSeen: boolean
    earnTakerMetrics: EarnTakerMetrics

    networkRPCMap: NetworkRPCMap
    totalEarningsInDefaultCurrencyMap: TotalEarningsInDefaultCurrencyMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    encryptedPassword: string
    installationCampaign: string | null
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    installationId: string

    sessionPassword: string
    customCurrencyMap: CustomCurrencyMap
    walletConnectInstanceLoadable: WalletConnectInstanceLoadable
    mode: Mode

    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    connections: ConnectionMap
    notificationsConfig: NotificationsConfig
    refreshContainerState: RefreshContainerState
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    referralConfig: ReferralConfig
    languageSettings: LanguageSettings
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_meta_mask_mode_changed_pupup_refresh_page_clicked'
                  | 'reload_button_click'
                  | 'on_profile_change_confirm_click'
                  | 'on_recovery_kit_setup'
                  | 'on_custom_currency_delete_request'
                  | 'on_custom_currency_update_request'
                  | 'on_token_click'
                  | 'on_send_nft_click'
                  | 'bridge_completed'
                  | 'on_bridge_submitted_click'
                  | 'on_transaction_request_widget_click'
                  | 'receive_click'
                  | 'on_tracked_tag_click'
                  | 'on_dismiss_kyc_button_clicked'
                  | 'on_kyc_try_again_clicked'
                  | 'on_do_bank_transfer_clicked'
                  | 'transaction_request_completed'
                  | 'transaction_request_cancelled'
                  | 'transaction_request_failed'
                  | 'on_onramp_success'
                  | 'on_withdrawal_monitor_fiat_transaction_success'
                  | 'transaction_request_replaced'
                  | 'on_nba_close_click'
                  | 'on_nba_cta_click'
                  | 'on_open_fullscreen_view_click'
                  | 'on_refresh_button_clicked'
                  | 'on_zwidget_expand_request'
                  | 'on_send_clicked'
                  | 'on_swap_clicked'
                  | 'on_buy_clicked'
                  | 'on_bridge_clicked'
                  | 'on_network_item_click'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_token_hide_click'
                  | 'on_token_un_pin_click'
                  | 'on_token_un_hide_click'
                  | 'on_token_pin_click'
                  | 'on_tokens_refresh_pulled'
                  | 'on_bank_transfer_selected'
                  | 'on_portfolio_refresh_pulled'
                  | 'on_discover_more_apps_clicked'
                  | 'on_placeholder_dapp_clicked'
                  | 'on_dapp_link_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_asset_added_to_earn'
                  | 'on_earn_withdrawal_success'
                  | 'on_earn_deposit_success'
                  | 'track_wallet_clicked'
                  | 'add_wallet_clicked'
                  | 'create_clicked'
                  | 'hardware_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_earn_recharge_configured'
                  | 'on_external_earn_deposit_completed_close_click'
                  | 'on_card_marketing_card_click'
                  | 'earn_and_card_widget_view_card_clicked'
                  | 'on_earn_last_recharge_transaction_hash_loaded'
                  | 'on_cashback_loaded'
                  | 'on_card_widget_clicked'
                  | 'on_earn_taker_address_click'
                  | 'on_earnings_fetched'
                  | 'on_add_funds_click'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'cancel_submitted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_submited'
                  | 'on_earn_configured'
                  | 'on_dismiss_bridge_widget_click'
                  | 'on_address_scanned_and_add_label'
                  | 'on_address_scanned'
                  | 'on_usd_taker_metrics_loaded'
                  | 'recover_safe_wallet_clicked'
                  | 'on_get_cashback_currency_clicked'
                  | 'import_card_owner_clicked'
                  | 'on_cashback_widget_clicked'
                  | 'on_silent_gnosis_pay_login_failed'
                  | 'on_card_activity_transactions_loaded'
                  | 'on_cashback_token_click'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_card_onboarded_account_state_received'
                  | 'on_dissmiss_card_kyc_onboarding_widget_clicked'
                  | 'on_app_rating_submitted'
                  | 'on_cashback_celebration_triggered'
                  | 'on_earn_celebration_triggered'
                  | 'on_top_up_transaction_complete_close'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_swaps_io_transaction_activity_swap_started'
                  | 'on_swaps_io_transaction_activity_completed'
                  | 'on_swaps_io_transaction_activity_failed'
                  | 'on_activate_free_card_clicked'
                  | 'on_dismiss_add_to_wallet_banner_clicked'
                  | 'on_transaction_activities_loaded'
                  | 'on_see_all_transactions_clicked'
                  | 'on_swaps_io_pending_transaction_activity_completed'
                  | 'on_pending_send_transaction_activity_completed'
                  | 'on_pending_send_transaction_activity_failed'
                  | 'on_card_b_reward_dissmiss_clicked'
                  | 'card_breward_claimed'
                  | 'card_brewards_updated'
                  | 'on_pending_breward_claim_transaction_activity_completed'
                  | 'on_pending_breward_claim_transaction_activity_failed'
                  | 'on_pending_areward_claim_transaction_activity_completed'
                  | 'on_pending_areward_claim_transaction_activity_failed'
                  | 'on_dissmiss_card_kyc_onboarded_widget_clicked'
                  | 'on_pending_card_top_up_failed'
                  | 'on_card_top_up_success'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_experimental_change_clicked'
                  | 'import_card_owner_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_default_currency_selected'
                  | 'on_delete_all_dapps_confirm_click'
                  | 'on_card_disconnected'
                  | 'on_disconnect_dapps_click'
                  | 'on_lock_zeal_click'
                  | 'on_notifications_config_changed'
                  | 'on_open_fullscreen_view_click'
                  | 'on_switch_card_new_card_selected'
                  | 'on_account_label_change_submit'
                  | 'account_item_clicked'
                  | 'confirm_account_delete_click'
                  | 'on_rewards_warning_confirm_account_delete_click'
                  | 'on_account_create_request'
                  | 'on_recovery_kit_setup'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'track_wallet_clicked'
                  | 'on_add_private_key_click'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_new_virtual_card_created_successfully'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_reacharege_configured_with_user_preferences'
                  | 'on_bank_transfer_selected'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'import_keys_button_clicked'
                  | 'cancel_submitted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_earn_configured'
                  | 'on_earn_deposit_success'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_earn_recharge_configured'
                  | 'on_physical_card_activated_info_screen_closed'
                  | 'on_card_order_redirect_to_gnosis_pay_clicked'
                  | 'on_language_settings_language_selected'
                  | 'session_password_decrypted'
          }
      >

export const View = ({
    account,
    accounts,
    portfolioMap,
    portfolioLoadable,
    submittedOffRampTransactions,
    networkRPCMap,
    keystoreMap,
    totalEarningsInDefaultCurrencyMap,
    earnHistoricalTakerUserCurrencyRateMap,
    swapsIOSwapRequestsMap,
    transactionActivitiesCacheMap,
    submitedBridgesMap,
    transactionRequests,
    encryptedPassword,
    earnTakerMetrics,
    networkMap,
    celebrationConfig,
    appRating,
    bankTransferInfo,
    currencyHiddenMap,
    currencyPinMap,
    installationId,
    sessionPassword,
    customCurrencyMap,
    mode,
    walletConnectInstanceLoadable,
    feePresetMap,
    gasCurrencyPresetMap,
    isEthereumNetworkFeeWarningSeen,
    cardConfig,
    defaultCurrencyConfig,
    connections,
    installationCampaign,
    notificationsConfig,
    refreshContainerState,
    referralConfig,
    languageSettings,
    experimentalMode,
    onMsg,
}: Props) => {
    const [modalState, setModalState] = useState<ModalState>({ type: 'closed' })

    return (
        <>
            <Layout
                referralConfig={referralConfig}
                installationCampaign={installationCampaign}
                encryptedPassword={encryptedPassword}
                appRating={appRating}
                celebrationConfig={celebrationConfig}
                refreshContainerState={refreshContainerState}
                defaultCurrencyConfig={defaultCurrencyConfig}
                sessionPassword={sessionPassword}
                totalEarningsInDefaultCurrencyMap={
                    totalEarningsInDefaultCurrencyMap
                }
                earnHistoricalTakerUserCurrencyRateMap={
                    earnHistoricalTakerUserCurrencyRateMap
                }
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                earnTakerMetrics={earnTakerMetrics}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                swapsIOSwapRequestsMap={swapsIOSwapRequestsMap}
                transactionActivitiesCacheMap={transactionActivitiesCacheMap}
                walletConnectInstanceLoadable={walletConnectInstanceLoadable}
                mode={mode}
                installationId={installationId}
                currencyHiddenMap={currencyHiddenMap}
                customCurrencyMap={customCurrencyMap}
                currencyPinMap={currencyPinMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                accountsMap={accounts}
                submitedBridgesMap={submitedBridgesMap}
                transactionRequests={transactionRequests}
                submittedOffRampTransactions={submittedOffRampTransactions}
                keystoreMap={keystoreMap}
                account={account}
                portfolioLoadable={portfolioLoadable}
                portfolioMap={portfolioMap}
                bankTransferInfo={bankTransferInfo}
                cardConfig={cardConfig}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'account_filter_click':
                        case 'on_settings_clicked':
                        case 'on_balance_clicked':
                        case 'on_account_name_clicked':
                            setModalState({ type: 'settings' })
                            break
                        case 'reload_button_click':
                        case 'on_cashback_token_click':
                        case 'on_profile_change_confirm_click':
                        case 'on_custom_currency_update_request':
                        case 'on_custom_currency_delete_request':
                        case 'on_send_nft_click':
                        case 'bridge_completed':
                        case 'on_bridge_submitted_click':
                        case 'on_transaction_request_widget_click':
                        case 'on_refresh_button_clicked':
                        case 'on_portfolio_refresh_pulled':
                        case 'on_dismiss_kyc_button_clicked':
                        case 'on_kyc_try_again_clicked':
                        case 'on_do_bank_transfer_clicked':
                        case 'transaction_request_completed':
                        case 'transaction_request_failed':
                        case 'on_onramp_success':
                        case 'on_withdrawal_monitor_fiat_transaction_success':
                        case 'transaction_request_replaced':
                        case 'on_open_fullscreen_view_click':
                        case 'on_zwidget_expand_request':
                        case 'on_send_clicked':
                        case 'on_swap_clicked':
                        case 'on_buy_clicked':
                        case 'on_bridge_clicked':
                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                        case 'on_bank_transfer_selected':
                        case 'on_token_pin_click':
                        case 'on_token_un_hide_click':
                        case 'on_token_un_pin_click':
                        case 'on_token_hide_click':
                        case 'on_discover_more_apps_clicked':
                        case 'on_dapp_link_clicked':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_earn_withdrawal_success':
                        case 'on_earn_deposit_success':
                        case 'on_earn_recharge_configured':
                        case 'on_card_marketing_card_click':
                        case 'on_tokens_refresh_pulled':
                        case 'on_earn_taker_address_click':
                        case 'on_recovery_kit_setup':
                        case 'on_earnings_fetched':
                        case 'on_add_funds_click':
                        case 'on_account_create_request':
                        case 'add_wallet_clicked':
                        case 'track_wallet_clicked':
                        case 'hardware_wallet_clicked':
                        case 'on_accounts_create_success_animation_finished':
                        case 'on_add_label_to_track_only_account_during_send':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'cancel_submitted':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_submited':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_earn_configured':
                        case 'on_meta_mask_mode_changed_pupup_refresh_page_clicked':
                        case 'on_dismiss_bridge_widget_click':
                        case 'on_address_scanned_and_add_label':
                        case 'on_address_scanned':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_cashback_loaded':
                        case 'on_card_widget_clicked':
                        case 'safe_wallet_clicked':
                        case 'recover_safe_wallet_clicked':
                        case 'on_get_cashback_currency_clicked':
                        case 'import_card_owner_clicked':
                        case 'on_cashback_widget_clicked':
                        case 'on_silent_gnosis_pay_login_failed':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_card_onboarded_account_state_received':
                        case 'on_dissmiss_card_kyc_onboarding_widget_clicked':
                        case 'on_app_rating_submitted':
                        case 'on_cashback_celebration_triggered':
                        case 'on_earn_celebration_triggered':
                        case 'on_top_up_transaction_complete_close':
                        case 'on_swaps_io_swap_request_created':
                        case 'on_swaps_io_transaction_activity_swap_started':
                        case 'on_swaps_io_transaction_activity_completed':
                        case 'on_swaps_io_transaction_activity_failed':
                        case 'on_activate_free_card_clicked':
                        case 'on_dismiss_add_to_wallet_banner_clicked':
                        case 'on_transaction_activities_loaded':
                        case 'on_see_all_transactions_clicked':
                        case 'on_pending_send_transaction_activity_completed':
                        case 'on_pending_send_transaction_activity_failed':
                        case 'on_dissmiss_card_kyc_onboarded_widget_clicked':
                        case 'on_card_b_reward_dissmiss_clicked':
                        case 'card_breward_claimed':
                        case 'card_brewards_updated':
                        case 'on_pending_breward_claim_transaction_activity_completed':
                        case 'on_pending_breward_claim_transaction_activity_failed':
                        case 'on_pending_areward_claim_transaction_activity_completed':
                        case 'on_pending_areward_claim_transaction_activity_failed':
                        case 'on_pending_card_top_up_failed':
                        case 'on_card_top_up_success':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
            <Modal
                experimentalMode={experimentalMode}
                languageSettings={languageSettings}
                userAReferralConfig={referralConfig.userA} // TODO @resetko-zeal drill just referralConfig
                defaultCurrencyConfig={defaultCurrencyConfig}
                installationCampaign={installationCampaign}
                earnTakerMetrics={earnTakerMetrics}
                currencyPinMap={currencyPinMap}
                sessionPassword={sessionPassword}
                cardConfig={cardConfig}
                customCurrencyMap={customCurrencyMap}
                installationId={installationId}
                currencyHiddenMap={currencyHiddenMap}
                networkMap={networkMap}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                encryptedPassword={encryptedPassword}
                keyStoreMap={keystoreMap}
                networkRPCMap={networkRPCMap}
                state={modalState}
                accountsMap={accounts}
                portfolioMap={portfolioMap}
                selectedAccount={account}
                connections={connections}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                mode={mode}
                notificationsConfig={notificationsConfig}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_account_create_request':
                        case 'on_recovery_kit_setup':
                        case 'on_account_label_change_submit':
                        case 'track_wallet_clicked':
                        case 'on_add_private_key_click':
                        case 'hardware_wallet_clicked':
                        case 'safe_wallet_clicked':
                        case 'add_wallet_clicked':
                        case 'recover_safe_wallet_clicked':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_address_scanned_and_add_label':
                        case 'on_address_scanned':
                        case 'import_card_owner_clicked':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'on_default_currency_selected':
                        case 'on_delete_all_dapps_confirm_click':
                        case 'on_card_disconnected':
                        case 'on_disconnect_dapps_click':
                        case 'on_lock_zeal_click':
                        case 'on_notifications_config_changed':
                        case 'on_open_fullscreen_view_click':
                        case 'on_switch_card_new_card_selected':
                        case 'confirm_account_delete_click':
                        case 'on_rewards_warning_confirm_account_delete_click':
                        case 'on_accounts_create_success_animation_finished':
                        case 'on_new_virtual_card_created_successfully':
                        case 'on_card_import_on_import_keys_clicked':
                        case 'on_card_imported_success_animation_complete':
                        case 'on_onboarded_card_imported_success_animation_complete':
                        case 'on_create_smart_wallet_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_reacharege_configured_with_user_preferences':
                        case 'on_bank_transfer_selected':
                        case 'on_add_label_to_track_only_account_during_send':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'import_keys_button_clicked':
                        case 'cancel_submitted':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_request_replaced':
                        case 'transaction_submited':
                        case 'on_earn_configured':
                        case 'on_earn_deposit_success':
                        case 'on_swaps_io_swap_request_created':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_earn_recharge_configured':
                        case 'on_physical_card_activated_info_screen_closed':
                        case 'on_card_order_redirect_to_gnosis_pay_clicked':
                        case 'on_experimental_change_clicked':
                        case 'session_password_decrypted':
                            onMsg(msg)
                            break

                        case 'close':
                        case 'on_new_physical_card_created_successfully':
                            setModalState({ type: 'closed' })
                            break
                        case 'account_item_clicked':
                        case 'on_language_settings_language_selected':
                            setModalState({ type: 'closed' })
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
