import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { SAFE_4337_MODULE_ENTRYPOINT_ADDRESS } from '@zeal/domains/Address/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { Network, NetworkRPCMap } from '@zeal/domains/Network'
import { fetchRPCResponseWithRetry2 } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import {
    SubmittedUserOperationCompleted,
    SubmittedUserOperationFailed,
    SubmittedUserOperationRejected,
} from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import {
    BoostedUserOperationWithoutSignature,
    BoostedUserOperationWithSignature,
    InitialUserOperation,
    MetaTransactionData,
    OperationType,
} from '@zeal/domains/UserOperation'
import { requestCurrentEntrypointNonce2 } from '@zeal/domains/UserOperation/api/fetchCurrentEntrypointNonce'
import { fetchPimlicoGasEstimates } from '@zeal/domains/UserOperation/api/fetchGasEstimates'
import { monitorSubmittedUserOperation } from '@zeal/domains/UserOperation/api/monitorSubmittedUserOperation'
import { submitUserOperationToBundler } from '@zeal/domains/UserOperation/api/submitUserOperationToBundler'
import {
    DUMMY_PASSKEY_SIGNATURE,
    passkeySignerGasBufferConfig,
    PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA,
    SAFE_ABI,
} from '@zeal/domains/UserOperation/constants'
import { calculateUserOperationHash } from '@zeal/domains/UserOperation/helpers/calculateUserOperationHash'
import { metaTransactionDatasToUserOperationCallData } from '@zeal/domains/UserOperation/helpers/metaTransactionDatasToUserOperationCallData'
import { signUserOperationHashWithPassKey } from '@zeal/domains/UserOperation/helpers/signUserOperationHashWithPassKey'

type Props = {
    installationId: string
    keystore: Safe4337
    network: Network
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' } | { type: 'on_local_signer_added' }

const fetch = async ({
    keystore,
    network,
    networkRPCMap,
    sessionPassword,
    installationId,
    signal,
}: {
    keystore: Safe4337
    network: Network
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    installationId: string
    signal?: AbortSignal
}): Promise<
    | SubmittedUserOperationCompleted
    | SubmittedUserOperationRejected
    | SubmittedUserOperationFailed
> => {
    const safeAddress = keystore.address

    const addOwnerMetaTransactionData: MetaTransactionData = {
        to: safeAddress,
        value: '0',
        data: Web3.abi.encodeFunctionData({
            abi: SAFE_ABI,
            functionName: 'addOwnerWithThreshold',
            args: [keystore.localSignerKeyStore.address, 1n],
        }),
        operation: OperationType.Call,
    }

    const initialUserOperation: InitialUserOperation = {
        type: 'initial_user_operation',
        sender: safeAddress,
        callData: metaTransactionDatasToUserOperationCallData({
            metaTransactionDatas: [addOwnerMetaTransactionData],
        }),
        initCode: null,
        nonce: await fetchRPCResponseWithRetry2(
            requestCurrentEntrypointNonce2({
                entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
                address: safeAddress,
            }),
            { network, networkRPCMap, signal }
        ),
        entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
    }

    const verificationGasLimitBuffer =
        passkeySignerGasBufferConfig[
            keystore.safeDeplymentConfig.passkeyOwner.signerVersion
        ].verificationGasLimitBuffer

    const gasEstimates = await fetchPimlicoGasEstimates({
        initialUserOperation: {
            callData: initialUserOperation.callData,
            nonce: initialUserOperation.nonce,
            initCode: initialUserOperation.initCode,
            sender: initialUserOperation.sender,
            signature: DUMMY_PASSKEY_SIGNATURE,
            paymasterAndData: PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA,
        },
        entrypoint: initialUserOperation.entrypoint,
        network,
        verificationGasLimitBuffer,
        callGasLimitBuffer: 0n,
        actionSource: {
            type: 'internal',
            transactionEventSource: 'smartWalletRecoveryAddOwner',
        },
        signal,
    })

    const userOperationWithoutSignature: BoostedUserOperationWithoutSignature =
        {
            type: 'boosted_user_operation_without_signature',
            sender: initialUserOperation.sender,
            entrypointNonce: initialUserOperation.nonce,
            entrypoint: initialUserOperation.entrypoint,
            callData: initialUserOperation.callData,
            initCode: initialUserOperation.initCode,
            callGasLimit: gasEstimates.callGasLimit,
            verificationGasLimit: gasEstimates.verificationGasLimit,
        }

    const userOperationHash = calculateUserOperationHash({
        network,
        userOperation: userOperationWithoutSignature,
        now: Date.now(),
    })

    const signature = await signUserOperationHashWithPassKey({
        userOperationHash,
        passkey: keystore.safeDeplymentConfig.passkeyOwner,
        sessionPassword,
    })

    const userOperationWithSignature: BoostedUserOperationWithSignature = {
        ...userOperationWithoutSignature,
        signature,
        type: 'boosted_user_operation_with_signature' as const,
    }

    const submittedUserOperation = await submitUserOperationToBundler({
        network,
        networkRPCMap,
        signal,
        userOperationWithSignature,
        actionSource: {
            type: 'internal',
            transactionEventSource: 'smartWalletRecoveryAddOwner',
        },
    })

    return monitorSubmittedUserOperation({
        submittedUserOperation,
        signal,
        network,
        networkRPCMap,
        installationId,
    })
}

export const AddLocalSigner = ({
    installationId,
    network,
    networkRPCMap,
    sessionPassword,
    keystore,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            network,
            networkRPCMap,
            installationId,
            sessionPassword,
            keystore,
        },
    })

    const liveMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
                break
            case 'loaded':
                switch (loadable.data.state) {
                    case 'failed':
                    case 'rejected':
                        break
                    case 'completed':
                        liveMsg.current({ type: 'on_local_signer_added' })
                        break
                    /* istanbul ignore next */
                    default:
                        return notReachable(loadable.data)
                }
                break
            case 'error':
                const error = parseAppError(loadable.error)
                switch (error.type) {
                    case 'passkey_operation_cancelled':
                        liveMsg.current({ type: 'close' })
                        break
                    /* istanbul ignore next */
                    default:
                        break
                }
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveMsg])

    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    title={
                        <FormattedMessage
                            id="passkey-recovery.recovering.deploy-signer.loading-text"
                            defaultMessage="Verifying passkey"
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'error':
            const error = parseAppError(loadable.error)
            switch (error.type) {
                case 'passkey_operation_cancelled':
                    return null
                default:
                    return (
                        <>
                            <LoadingLayout
                                actionBar={
                                    <ActionBar
                                        left={
                                            <IconButton
                                                variant="on_light"
                                                onClick={() =>
                                                    onMsg({ type: 'close' })
                                                }
                                            >
                                                {({ color }) => (
                                                    <BackIcon
                                                        size={24}
                                                        color={color}
                                                    />
                                                )}
                                            </IconButton>
                                        }
                                    />
                                }
                                title={
                                    <FormattedMessage
                                        id="passkey-recovery.recovering.deploy-signer.loading-text"
                                        defaultMessage="Verifying passkey"
                                    />
                                }
                                onClose={() => onMsg({ type: 'close' })}
                            />
                            <AppErrorPopup
                                error={error}
                                installationId={installationId}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        case 'try_again_clicked':
                                            setLoadable({
                                                type: 'loading',
                                                params: loadable.params,
                                            })
                                            break
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        </>
                    )
            }
        case 'loaded':
            switch (loadable.data.state) {
                case 'completed':
                    return (
                        <LoadingLayout
                            actionBar={
                                <ActionBar
                                    left={
                                        <IconButton
                                            variant="on_light"
                                            onClick={() =>
                                                onMsg({ type: 'close' })
                                            }
                                        >
                                            {({ color }) => (
                                                <BackIcon
                                                    size={24}
                                                    color={color}
                                                />
                                            )}
                                        </IconButton>
                                    }
                                />
                            }
                            title={
                                <FormattedMessage
                                    id="passkey-recovery.recovering.deploy-signer.loading-text"
                                    defaultMessage="Verifying passkey"
                                />
                            }
                            onClose={() => onMsg({ type: 'close' })}
                        />
                    )
                case 'failed':
                case 'rejected':
                    const error = parseAppError(
                        new ImperativeError(
                            'Failed to add local signer as owner during Safe recovery',
                            { userOpHash: loadable.data.userOperationHash }
                        )
                    )
                    return (
                        <>
                            <LoadingLayout
                                actionBar={
                                    <ActionBar
                                        left={
                                            <IconButton
                                                variant="on_light"
                                                onClick={() =>
                                                    onMsg({ type: 'close' })
                                                }
                                            >
                                                {({ color }) => (
                                                    <BackIcon
                                                        size={24}
                                                        color={color}
                                                    />
                                                )}
                                            </IconButton>
                                        }
                                    />
                                }
                                title={
                                    <FormattedMessage
                                        id="passkey-recovery.recovering.deploy-signer.loading-text"
                                        defaultMessage="Verifying passkey"
                                    />
                                }
                                onClose={() => onMsg({ type: 'close' })}
                            />
                            <AppErrorPopup
                                error={error}
                                installationId={installationId}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        case 'try_again_clicked':
                                            setLoadable({
                                                type: 'loading',
                                                params: loadable.params,
                                            })
                                            break
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        </>
                    )
                default:
                    return notReachable(loadable.data)
            }
        default:
            return notReachable(loadable)
    }
}
