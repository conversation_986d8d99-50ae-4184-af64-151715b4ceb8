import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { Network, NetworkRPCMap } from '@zeal/domains/Network'
import { SubmittedUserOperationPending } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import {
    BoostedUserOperationWithSignature,
    UserOperationWithSignature,
} from '@zeal/domains/UserOperation'
import { submitUserOperationToBundler } from '@zeal/domains/UserOperation/api/submitUserOperationToBundler'

import { CheckUserOperationStatus } from './CheckUserOperationStatus'

import { LoadingLayout } from '../LoadingLayout'

type Props = {
    userOperationWithSignature:
        | UserOperationWithSignature
        | BoostedUserOperationWithSignature
    keyStore: Safe4337
    label: string
    network: Network
    networkRPCMap: NetworkRPCMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' } | MsgOf<typeof CheckUserOperationStatus>

const fetch = async ({
    network,
    userOperationWithSignature,
    signal,
    networkRPCMap,
}: {
    userOperationWithSignature:
        | UserOperationWithSignature
        | BoostedUserOperationWithSignature
    network: Network
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<SubmittedUserOperationPending> => {
    return submitUserOperationToBundler({
        network,
        networkRPCMap,
        userOperationWithSignature,
        actionSource: {
            type: 'internal',
            transactionEventSource: 'smartWalletCreationReferenceDeployment',
        },
        signal,
    })
}

export const SubmitUserOperation = ({
    network,
    userOperationWithSignature,
    label,
    networkRPCMap,
    keyStore,
    installationId,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            network,
            userOperationWithSignature,
            networkRPCMap,
        },
    })

    switch (loadable.type) {
        case 'loading':
            return <LoadingLayout onMsg={onMsg} />
        case 'loaded':
            return (
                <CheckUserOperationStatus
                    networkRPCMap={networkRPCMap}
                    installationId={installationId}
                    submittedUserOperation={loadable.data}
                    keyStore={keyStore}
                    label={label}
                    network={network}
                    onMsg={onMsg}
                />
            )
        case 'error':
            const error = parseAppError(loadable.error)

            return (
                <>
                    <LoadingLayout onMsg={onMsg} />
                    <AppErrorPopup
                        installationId={installationId}
                        error={error}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
