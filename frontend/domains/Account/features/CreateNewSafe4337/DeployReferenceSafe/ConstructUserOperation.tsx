import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { uuid } from '@zeal/toolkit/Crypto'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { SAFE_4337_MODULE_ENTRYPOINT_ADDRESS } from '@zeal/domains/Address/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { Passkey } from '@zeal/domains/KeyStore/domains/Passkey'
import { getKeystoreFromPrivateKey } from '@zeal/domains/KeyStore/helpers/getKeystoreFromPrivateKey'
import { getPredictedSafeAddress } from '@zeal/domains/KeyStore/helpers/getPredictedSafeAddress'
import { getSafeDeploymentInitCode } from '@zeal/domains/KeyStore/helpers/getSafeDeploymentInitCode'
import { Network } from '@zeal/domains/Network'
import {
    BoostedUserOperationWithoutSignature,
    InitialUserOperation,
    MetaTransactionData,
    OperationType,
    UserOperationHash,
    UserOperationWithoutSignature,
} from '@zeal/domains/UserOperation'
import { fetchPimlicoGasEstimates } from '@zeal/domains/UserOperation/api/fetchGasEstimates'
import {
    DUMMY_PASSKEY_SIGNATURE,
    passkeySignerGasBufferConfig,
    PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA,
    SAFE_ABI,
} from '@zeal/domains/UserOperation/constants'
import { calculateUserOperationHash } from '@zeal/domains/UserOperation/helpers/calculateUserOperationHash'
import { metaTransactionDatasToUserOperationCallData } from '@zeal/domains/UserOperation/helpers/metaTransactionDatasToUserOperationCallData'

import { LoadingLayout } from './LoadingLayout'

type Props = {
    network: Network
    sessionPassword: string
    passkey: Passkey
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'on_user_operation_constructed'
          userOperation:
              | UserOperationWithoutSignature
              | BoostedUserOperationWithoutSignature
          userOperationHash: UserOperationHash
          keyStore: Safe4337
      }

const fetch = async ({
    network,
    passkey,
    sessionPassword,
    signal,
}: {
    network: Network
    passkey: Passkey
    sessionPassword: string
    signal?: AbortSignal
}): Promise<{
    userOperation:
        | UserOperationWithoutSignature
        | BoostedUserOperationWithoutSignature
    userOperationHash: UserOperationHash
    keyStore: Safe4337
}> => {
    const pk = Web3.privateKey.generate()

    const localSignerKeyStore = await getKeystoreFromPrivateKey(
        pk,
        sessionPassword
    )

    const safeDeplymentConfig = {
        passkeyOwner: passkey,
        saltNonce: passkey.signerAddress,
        threshold: 1,
    }

    const safeAddress = getPredictedSafeAddress(safeDeplymentConfig)

    const addOwnerMetaTransactionData: MetaTransactionData = {
        to: safeAddress,
        value: '0',
        data: Web3.abi.encodeFunctionData({
            abi: SAFE_ABI,
            functionName: 'addOwnerWithThreshold',
            args: [localSignerKeyStore.address, 1n],
        }),
        operation: OperationType.Call,
    }

    const initialUserOperation: InitialUserOperation = {
        type: 'initial_user_operation',
        sender: safeAddress,
        callData: metaTransactionDatasToUserOperationCallData({
            metaTransactionDatas: [addOwnerMetaTransactionData],
        }),
        initCode: getSafeDeploymentInitCode(safeDeplymentConfig),
        nonce: 0n,
        entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
    }

    const verificationGasLimitBuffer =
        passkeySignerGasBufferConfig[
            safeDeplymentConfig.passkeyOwner.signerVersion
        ].verificationGasLimitBuffer

    const gasEstimates = await fetchPimlicoGasEstimates({
        initialUserOperation: {
            callData: initialUserOperation.callData,
            nonce: initialUserOperation.nonce,
            initCode: initialUserOperation.initCode,
            sender: initialUserOperation.sender,
            signature: DUMMY_PASSKEY_SIGNATURE,
            paymasterAndData: PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA,
        },
        entrypoint: initialUserOperation.entrypoint,
        network,
        verificationGasLimitBuffer,
        callGasLimitBuffer: 0n,
        actionSource: {
            type: 'internal',
            transactionEventSource: 'smartWalletCreationReferenceDeployment',
        },
        signal,
    })

    const userOperationWithoutSignature: BoostedUserOperationWithoutSignature =
        {
            type: 'boosted_user_operation_without_signature',
            sender: initialUserOperation.sender,
            entrypointNonce: initialUserOperation.nonce,
            entrypoint: initialUserOperation.entrypoint,
            callData: initialUserOperation.callData,
            initCode: initialUserOperation.initCode,
            callGasLimit: gasEstimates.callGasLimit,
            verificationGasLimit: gasEstimates.verificationGasLimit,
        }

    const userOperationHash = calculateUserOperationHash({
        network,
        userOperation: userOperationWithoutSignature,
        now: Date.now(),
    })

    return {
        keyStore: {
            type: 'safe_4337',
            id: uuid(),
            address: safeAddress,
            localSignerKeyStore,
            safeDeplymentConfig,
        },
        userOperation: userOperationWithoutSignature,
        userOperationHash,
    }
}

export const ConstructUserOperation = ({
    network,
    passkey,
    sessionPassword,
    installationId,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            network,
            passkey,
            sessionPassword,
        },
    })

    const onMsgLive = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
                break
            case 'loaded':
                onMsgLive.current({
                    type: 'on_user_operation_constructed',
                    userOperation: loadable.data.userOperation,
                    userOperationHash: loadable.data.userOperationHash,
                    keyStore: loadable.data.keyStore,
                })
                break
            case 'error':
                captureError(loadable.error)
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, onMsgLive])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return <LoadingLayout onMsg={onMsg} />
        case 'error':
            const error = parseAppError(loadable.error)

            return (
                <>
                    <LoadingLayout onMsg={onMsg} />
                    <AppErrorPopup
                        installationId={installationId}
                        error={error}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
