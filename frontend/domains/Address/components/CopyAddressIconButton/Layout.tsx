import { ComponentPropsWithoutRef } from 'react'

import { Copy } from '@zeal/uikit/Icon/Copy'
import { TickSquare } from '@zeal/uikit/Icon/TickSquare'
import { IconButton } from '@zeal/uikit/IconButton'

import { noop, notReachable } from '@zeal/toolkit'
import { LazyLoadableData } from '@zeal/toolkit/LoadableData/LazyLoadableData'

type Props = {
    size: number
    variant: ComponentPropsWithoutRef<typeof IconButton>['variant']
    copyLoadable: LazyLoadableData<boolean, { stringToCopy: string }>
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'on_copy_address_clicked' }

export const Layout = ({ copyLoadable, variant, size, onMsg }: Props) => {
    switch (copyLoadable.type) {
        case 'not_asked':
            return (
                <IconButton
                    variant={variant}
                    onClick={(e) => {
                        e.stopPropagation()

                        onMsg({ type: 'on_copy_address_clicked' })
                    }}
                >
                    {({ color }) => <Copy size={size} color={color} />}
                </IconButton>
            )

        case 'loading':
            return (
                <IconButton variant={variant} onClick={noop}>
                    {({ color }) => <Copy size={size} color={color} />}
                </IconButton>
            )

        case 'error':
            return null

        case 'loaded':
            return (
                <IconButton variant={variant} onClick={noop}>
                    {({ color }) => <TickSquare color={color} size={size} />}
                </IconButton>
            )

        /* istanbul ignore next */
        default:
            return notReachable(copyLoadable)
    }
}
