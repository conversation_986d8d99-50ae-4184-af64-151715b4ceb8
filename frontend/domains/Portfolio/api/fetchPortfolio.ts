import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { memoizeOne } from '@zeal/toolkit/Function/memoizeOne'
import { keys, values } from '@zeal/toolkit/Object'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    fetchBalanceOfCryptoCurrency,
    requestBalanceOfCryptoCurrency,
} from '@zeal/domains/Address/api/fetchBalanceOf'
import {
    fetchNativeBalance,
    requestNativeBalance,
} from '@zeal/domains/Address/api/fetchNativeBalance'
import { CardBalance, CardConfig } from '@zeal/domains/Card'
import { fetchBalanceOfCardOnChain } from '@zeal/domains/Card/api/fetchBalance'
import { CurrencyHiddenMap, currencyId } from '@zeal/domains/Currency'
import { fetchCryptoCurrency2 } from '@zeal/domains/Currency/api/fetchCryptoCurrency2'
import { fetchPriceChanges } from '@zeal/domains/Currency/api/fetchPriceChange'
import { MONERIUM_V1_TOKENS } from '@zeal/domains/Currency/constants'
import { fetchEarn } from '@zeal/domains/Earn/api/fetchEarn'
import { fetchDefaultCurrencyRateFromUSD } from '@zeal/domains/FXRate/api/fetchDefaultCurrencyRateToUSD'
import { fetchRates } from '@zeal/domains/FXRate/api/fetchRate'
import {
    CustomNetwork,
    Network,
    NetworkMap,
    NetworkRPCMap,
    TestNetwork,
} from '@zeal/domains/Network'
import { GNOSIS } from '@zeal/domains/Network/constants'
import { Portfolio2, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { fetchPublicRPCBatch } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { Token2 } from '@zeal/domains/Token'
import { fetchUserContractStats } from '@zeal/domains/Token/api/fetchUserContractStats'
import { checkIfScam } from '@zeal/domains/Token/helpers/checkIfScam'

import { ChainActivityResult, fetchChainActivity } from './fetchChainActivity'
import { fetchDeBankApps } from './fetchDeBankApps'
import { fetchDeBankNFTCollections } from './fetchDeBankNfts'
import { fetchDeBankTokens } from './fetchDeBankTokens'

import {
    APP_NAMES_TO_EXCLUDE,
    DEFI_PROTOCOL_ADDRESSES,
    DEFI_PROTOCOL_TOKENS,
} from '../constants'
import { updateServerPortfolioUsingDefaultCurrency } from '../helpers/updateServerPortfolioUsingDefaultCurrency'

const debankPortfolioCache = new Map<
    Web3.address.Address,
    { promise: Promise<ServerPortfolio2>; timestamp: number }
>()
const DEBANK_CACHE_TTL_MS = process.env.NODE_ENV === 'test' ? 0 : 2000

export type Request = {
    variant: DeBankPortfolioLoadingVariant
    address: Web3.address.Address
    currencyHiddenMap: CurrencyHiddenMap
    customCurrencies: CustomCurrencyMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    cardConfig: CardConfig
    signal?: AbortSignal
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
}

const fetchNetworksNativeBalances = async ({
    address,
    networks,
    networkRPCMap,
    signal,
}: {
    networks: Network[]
    address: Web3.address.Address
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<Token2[]> => {
    const nativeTokens = await Promise.all(
        networks.map(
            (network): Promise<Token2 | null> =>
                fetchNativeBalance({
                    address,
                    network: network,
                    networkRPCMap,
                    signal,
                })
                    .then((balance): Token2 | null =>
                        balance === 0n
                            ? null
                            : {
                                  balance: {
                                      amount: balance,
                                      currency: network.nativeCurrency,
                                  },
                                  rate: null,
                                  priceInDefaultCurrency: null,
                                  marketData: null,
                                  scam: false,
                              }
                    )
                    .catch(() => null)
        )
    )

    return nativeTokens.filter(excludeNullValues)
}

const fetchTestNetworksTokensBalances = async ({
    address,
    customCurrencies,
    networkMap,
    networkRPCMap,
    signal,
}: {
    address: Web3.address.Address
    networkMap: NetworkMap
    customCurrencies: CustomCurrencyMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<Token2[]> => {
    const customCurrenciesArray = await Promise.all(
        values(customCurrencies).map(
            (cryptoCurrency): Promise<Token2 | null> => {
                return fetchBalanceOfCryptoCurrency({
                    currency: cryptoCurrency,
                    address,
                    networkMap,
                    networkRPCMap,
                    signal,
                })
                    .then(
                        (balance): Token2 => ({
                            balance,
                            priceInDefaultCurrency: null,
                            rate: null,
                            marketData: null,
                            scam: false,
                        })
                    )
                    .catch(() => null)
            }
        )
    )

    return customCurrenciesArray.filter(excludeNullValues)
}

const fetchIndexerTokens = async ({
    address,
    defaultCurrencyConfig,
    networkRPCMap,
    currencyHiddenMap,
    signal,
}: {
    address: Web3.address.Address
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkRPCMap: NetworkRPCMap
    currencyHiddenMap: CurrencyHiddenMap
    signal?: AbortSignal
}): Promise<Token2[]> => {
    const contractStats = await fetchUserContractStats({ address, signal })
    const currencies = keys(contractStats).map((address) =>
        currencyId({
            network: GNOSIS.hexChainId,
            address,
        })
    )

    const knownCryptoCurrencies = await fetchCryptoCurrency2({
        currencies,
        networkRPCMap,
    })

    const defiProtocolAddresses = new Set(
        DEFI_PROTOCOL_TOKENS.map((token) => token.address)
    )

    const indexerTokens = values(knownCryptoCurrencies).map(
        (cryptoCurrency) => {
            const contractAddress = cryptoCurrency.address
            const stats = contractStats[contractAddress]
            const isDefiToken = defiProtocolAddresses.has(contractAddress)
            return {
                scam: isDefiToken || checkIfScam(stats),
                cryptoCurrency,
            }
        }
    )

    const balanceOfRequests = indexerTokens
        .filter(
            // we don't want to fetch balance for scam to save bandwidth
            (token) => {
                const userSetHiddenPropertyOnTokenManually =
                    currencyHiddenMap[token.cryptoCurrency.id] !== undefined

                const isHidden = userSetHiddenPropertyOnTokenManually
                    ? currencyHiddenMap[token.cryptoCurrency.id]
                    : token.scam

                return !isHidden
            }
        )
        .map(({ cryptoCurrency }) => {
            return requestBalanceOfCryptoCurrency({
                address,
                currency: cryptoCurrency,
                block: 'latest',
            })
        })
    const nativeBalanceRequest = requestNativeBalance({
        address,
        network: GNOSIS,
        block: 'latest',
    })

    const [native, ...erc20Balances] = await fetchPublicRPCBatch(
        [nativeBalanceRequest, ...balanceOfRequests],
        {
            network: GNOSIS,
            signal,
        }
    )

    const balances = [native, ...erc20Balances]
    const tokens: Token2[] = [
        ...[
            ...indexerTokens,
            { cryptoCurrency: GNOSIS.nativeCurrency, scam: false },
        ]
            .map((token) => {
                const balance = balances.find(
                    (money) =>
                        money.currency.address === token.cryptoCurrency.address
                ) || { amount: 0n, currency: token.cryptoCurrency }
                return {
                    balance,
                    rate: null,
                    priceInDefaultCurrency: {
                        amount: 0n,
                        currency: defaultCurrencyConfig.defaultCurrency,
                    },
                    marketData: null,
                    scam: token.scam,
                }
            })
            .filter((token) => !token.scam)
            .filter(
                (token) => !MONERIUM_V1_TOKENS.has(token.balance.currency.id)
            ),
    ]
    return tokens
}

type Params = {
    variant: DeBankPortfolioLoadingVariant
    address: Web3.address.Address
    currencyHiddenMap: CurrencyHiddenMap
    signal?: AbortSignal
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
}

export const fetchServerPortfolioWithCache = memoizeOne(
    ({
        cacheKey: _,
        signal: __,
        ...rest
    }: Params & { cacheKey: string }): Promise<ServerPortfolio2> =>
        fetchServerPortfolio2(rest),
    ({ cacheKey, address }) => {
        return `${cacheKey}${address}`
    }
)

export const fetchServerPortfolio2 = async ({
    address,
    signal,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    currencyHiddenMap,
    installationId,
    variant,
}: Params): Promise<ServerPortfolio2> => {
    const [deBankPortfolio, rateFromUsd] = await Promise.all([
        fetchCombinedPortfolio({
            variant,
            address,
            defaultCurrencyConfig,
            networkMap,
            networkRPCMap,
            signal,
            currencyHiddenMap,
        }),
        fetchDefaultCurrencyRateFromUSD({
            defaultCurrencyConfig,
            signal,
        }),
    ])

    const cryptoCurrencies = deBankPortfolio.tokens.map(
        (token) => token.balance.currency
    )

    const [rates, historyPrices] = await Promise.all([
        fetchRates({
            cryptoCurrencies,
            quote: defaultCurrencyConfig.defaultCurrency,
            networkMap,
            networkRPCMap,
            signal,
        }),
        fetchPriceChanges({
            currencies: cryptoCurrencies,
            networkMap,
            signal,
        }),
    ])

    return updateServerPortfolioUsingDefaultCurrency({
        portfolio: deBankPortfolio,
        defaultCurrencyConfig,
        rates,
        rateFromUsd: rateFromUsd,
        historyPrices,
        installationId,
    })
}

const fetchDebankPortfolioWithCache = async ({
    signal,
    address,
    variant,
}: {
    signal?: AbortSignal
    address: Web3.address.Address
    variant: DeBankPortfolioLoadingVariant
}): Promise<ServerPortfolio2> => {
    const lastFetchTime = debankPortfolioCache.get(address)
    const now = Date.now()

    if (lastFetchTime && now - lastFetchTime.timestamp < DEBANK_CACHE_TTL_MS) {
        return lastFetchTime.promise
    }

    const promise = fetchDebankPortfolio({ variant, signal, address })

    debankPortfolioCache.set(address, { promise, timestamp: now })

    promise.catch(() => {
        const currentCached = debankPortfolioCache.get(address)
        if (currentCached && currentCached.promise === promise) {
            debankPortfolioCache.delete(address)
        }
    })

    return promise
}

const shouldLoadDebankData = ({
    chainActivity,
    indexerTokens,
}: {
    chainActivity: ChainActivityResult
    indexerTokens: Token2[]
}) => {
    switch (chainActivity.type) {
        case 'no_activity':
            return false
        case 'gnosis_only':
            return indexerTokens.some((token) =>
                DEFI_PROTOCOL_ADDRESSES.has(token.balance.currency.address)
            )
        case 'mixed_activity':
            return true

        default:
            notReachable(chainActivity)
    }
}

const fetchCombinedPortfolio = async ({
    address,
    signal,
    currencyHiddenMap,
    defaultCurrencyConfig,
    networkRPCMap,
    variant,
}: {
    address: Web3.address.Address
    signal?: AbortSignal
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    currencyHiddenMap: CurrencyHiddenMap
    variant: DeBankPortfolioLoadingVariant
}): Promise<ServerPortfolio2> => {
    const [chainActivity, indexerPortfolio] = await Promise.all([
        fetchChainActivity({ address, signal }),
        fetchIndexerTokens({
            address,
            signal,
            networkRPCMap,
            defaultCurrencyConfig,
            currencyHiddenMap,
        }),
    ])
    const deBankPortfolio = shouldLoadDebankData({
        chainActivity,
        indexerTokens: indexerPortfolio,
    })
        ? await fetchDebankPortfolioWithCache({ variant, address, signal })
        : {
              currencies: {},
              tokens: [],
              apps: [],
              nftCollections: [],
          }

    const tokens = [
        ...deBankPortfolio.tokens.filter(
            (token) =>
                GNOSIS.hexChainId !== token.balance.currency.networkHexChainId
        ),
        ...indexerPortfolio.filter(
            (token) =>
                GNOSIS.hexChainId === token.balance.currency.networkHexChainId
        ),
    ]

    return {
        ...deBankPortfolio,
        tokens,
    }
}
export type DeBankPortfolioLoadingVariant = 'only_tokens' | 'full_portfolio'
const fetchDebankPortfolio = async ({
    signal,
    address,
    variant,
}: {
    variant: DeBankPortfolioLoadingVariant
    signal?: AbortSignal
    address: Web3.address.Address
}): Promise<ServerPortfolio2> => {
    switch (variant) {
        case 'full_portfolio': {
            const [tokens, apps, nftCollections] = await Promise.all([
                fetchDeBankTokens({ address, signal }),
                fetchDeBankApps({ address, signal }).then((apps) =>
                    apps.filter((app) => !APP_NAMES_TO_EXCLUDE.has(app.name))
                ),
                fetchDeBankNFTCollections({ address, signal }),
            ])

            return {
                apps,
                nftCollections,
                tokens,
            }
        }

        case 'only_tokens': {
            const [tokens] = await Promise.all([
                fetchDeBankTokens({ address, signal }),
            ])

            return {
                apps: [],
                nftCollections: [],
                tokens,
            }
        }

        /* istanbul ignore next */
        default:
            return notReachable(variant)
    }
}

export const fetchPortfolio2 = async ({
    address,
    customCurrencies,
    signal,
    networkMap,
    networkRPCMap,
    cardConfig,
    defaultCurrencyConfig,
    installationId,
    currencyHiddenMap,
    variant,
}: Request): Promise<Portfolio2> => {
    const networksToFetchNativeCurrency = values(networkMap).filter(
        (net): net is CustomNetwork | TestNetwork => {
            switch (net.type) {
                case 'predefined':
                    return false
                case 'custom':
                case 'testnet':
                    return true
                /* istanbul ignore next */
                default:
                    return notReachable(net)
            }
        }
    )

    const [
        earn,
        cardBalance,
        serverPortfolio,
        testNetworksNativeBalances,
        testNetworksTokensBalances,
    ] = await Promise.all([
        fetchEarn({
            earnOwnerAddress: address,
            networkRPCMap,
            signal,
            defaultCurrencyConfig,
            networkMap,
        }),
        fetchCardBalance({
            cardConfig,
            networkRPCMap,
            address,
            defaultCurrencyConfig,
            networkMap,
            signal,
        }),
        fetchServerPortfolio2({
            variant,
            address,
            defaultCurrencyConfig,
            networkMap,
            networkRPCMap,
            installationId,
            currencyHiddenMap,
            signal,
        }),
        fetchNetworksNativeBalances({
            address,
            networks: networksToFetchNativeCurrency,
            networkRPCMap,
            signal,
        }),
        fetchTestNetworksTokensBalances({
            address,
            customCurrencies,
            networkMap,
            networkRPCMap,
            signal,
        }),
    ])

    return {
        ...serverPortfolio,
        tokens: [
            ...serverPortfolio.tokens,
            ...testNetworksNativeBalances,
            ...testNetworksTokensBalances,
        ],
        earn,
        cardBalance,
    }
}

export const fetchPortfolioWithCache = memoizeOne(
    ({
        cacheKey: _,
        signal: __,
        ...rest
    }: Request & { cacheKey: string }): Promise<Portfolio2> =>
        fetchPortfolio2(rest),
    ({ cacheKey, address }) => {
        return `${cacheKey}${address}`
    }
)

const fetchCardBalance = async ({
    cardConfig,
    networkRPCMap,
    address,
    networkMap,
    defaultCurrencyConfig,
    signal,
}: {
    address: Web3.address.Address
    cardConfig: CardConfig
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<CardBalance | null> => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
        case 'card_readonly_signer_address_is_selected':
            return null
        case 'card_readonly_signer_address_is_selected_fully_onboarded':
            if (address !== cardConfig.readonlySignerAddress) {
                return null
            }
            return fetchBalanceOfCardOnChain({
                cardCryptoCurrency: cardConfig.currency,
                cardAddress: cardConfig.lastSeenSafeAddress,
                networkRPCMap,
                networkMap,
                signal,
                defaultCurrencyConfig,
            })
        /* istanbul ignore next */
        default:
            return notReachable(cardConfig)
    }
}
