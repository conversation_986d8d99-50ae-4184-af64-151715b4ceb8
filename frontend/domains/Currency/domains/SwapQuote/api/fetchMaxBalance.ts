import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'

import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { fetchFeeAdjustedBalance } from '@zeal/domains/Currency/api/fetchFeeAdjustedBalance'
import { KeyStore } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import {
    DEFAULT_NETWORK_HEX_IDS_TO_SPONSOR,
    findNetworkByHexChainId,
} from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'

import { fetchSwapQuote } from './fetchSwapQuote'

import { SwapQuoteRequest } from '../SwapQuote'

export const fetchMaxBalance = async ({
    portfolio,
    swapQuoteRequest,
    gasCurrencyPresetMap,
    keyStore,
    signal,
}: {
    swapQuoteRequest: SwapQuoteRequest
    portfolio: ServerPortfolio2
    keyStore: KeyStore
    gasCurrencyPresetMap: GasCurrencyPresetMap
    signal?: AbortSignal
}): Promise<CryptoMoney> => {
    const quote = await fetchSwapQuote({ ...swapQuoteRequest, signal })
    const route = quote.routes[0]

    if (!route) {
        return getBalanceByCryptoCurrency2({
            currency: swapQuoteRequest.fromCurrency,
            serverPortfolio: portfolio,
        })
    }

    const network = findNetworkByHexChainId(
        swapQuoteRequest.fromCurrency.networkHexChainId,
        swapQuoteRequest.networkMap
    )

    return fetchFeeAdjustedBalance({
        fromAddress: swapQuoteRequest.fromAccount.address,
        defaultCurrencyConfig: swapQuoteRequest.defaultCurrencyConfig,
        networkMap: swapQuoteRequest.networkMap,
        networkRPCMap: swapQuoteRequest.networkRPCMap,
        fromCurrency: swapQuoteRequest.fromCurrency,
        gasCurrencyPresetMap,
        keyStore,
        network,
        networksToSponsor: DEFAULT_NETWORK_HEX_IDS_TO_SPONSOR,
        serverPortfolio: portfolio,
        requests: [route.approvalTransaction, route.swapTransaction].filter(
            excludeNullValues
        ),
        actionSource: {
            type: 'internal',
            transactionEventSource: 'swapGasEstimation',
        },
        signal,
    })
}
