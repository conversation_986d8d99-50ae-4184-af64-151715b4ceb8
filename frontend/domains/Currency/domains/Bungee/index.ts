import { BungeeRawRequestType, BungeeRawWitness } from '@zeal/api/bungeeApi'

import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

import { CryptoCurrency } from '@zeal/domains/Currency'
import { CryptoMoney } from '@zeal/domains/Money'
import {
    EthSendTransaction,
    EthSignTypedDataV4,
} from '@zeal/domains/RPCRequest'

export type BungeeIntentQuoteRequest = {
    fromAddress: Web3.address.Address
    fromAmount: CryptoMoney

    toCurrency: CryptoCurrency
    toAddress: Web3.address.Address
}

export type BungeeIntentQuote = BungeeNativeIntentQuote | BungeeERC20IntentQuote

export type BungeeNativeIntentQuote = {
    type: 'bungee_native_token_intent_quote'
    quoteId: string
    requestHash: Hexadecimal

    outputAmount: CryptoMoney
    transaction: EthSendTransaction
}

export type BungeeERC20IntentQuote = {
    type: 'bungee_erc20_intent_quote'
    quoteId: string
    requestHash: Hexadecimal
    signTypedData: EthSignTypedDataV4
    witness: BungeeRawWitness
    quoteRequestType: BungeeRawRequestType

    outputAmount: CryptoMoney

    permitApprovalTransaction: EthSendTransaction | null
}
