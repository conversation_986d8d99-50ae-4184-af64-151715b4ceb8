import {
    BungeeRawRequestType,
    BungeeRawWitness,
    get,
} from '@zeal/api/bungeeApi'

import { maxUint256 } from '@zeal/toolkit/BigInt'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import {
    bigint,
    nullableOf,
    object,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { PERMIT_2_CONTRACT_ADDRESS } from '@zeal/domains/Address/constants'
import { CryptoCurrency } from '@zeal/domains/Currency'
import { requestAllowance } from '@zeal/domains/Currency/api/fetchAllowance'
import {
    GNOSIS_EURE,
    GNOSIS_EURE_V2,
    GNOSIS_GBPE,
    GNOSIS_GBPE_V2,
} from '@zeal/domains/Currency/constants'
import {
    BungeeIntentQuote,
    BungeeIntentQuoteRequest,
    BungeeNativeIntentQuote,
} from '@zeal/domains/Currency/domains/Bungee'
import { BUNGEE_NATIVE_TOKEN_ADDRESS } from '@zeal/domains/Currency/domains/Bungee/constants'
import { createApprovalTransaction } from '@zeal/domains/Currency/helpers/createApprovalTransaction'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import {
    EthSendTransaction,
    EthSignTypedDataV4,
} from '@zeal/domains/RPCRequest'
import { fetchRPCResponseWithRetry2 } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'

type ERC20RawQuote = {
    quoteId: string
    requestHash: Hexadecimal.Hexadecimal
    outputAmount: CryptoMoney
    signTypedData: EthSignTypedDataV4
    witness: BungeeRawWitness
    quoteRequestType: BungeeRawRequestType
}

const getOutputTokenAddress = (
    outputCurrency: CryptoCurrency,
    networkMap: NetworkMap
): Web3.address.Address => {
    const network = findNetworkByHexChainId(
        outputCurrency.networkHexChainId,
        networkMap
    )

    switch (outputCurrency.id) {
        case network.nativeCurrency.id:
            return BUNGEE_NATIVE_TOKEN_ADDRESS
        case GNOSIS_EURE_V2.id:
            return GNOSIS_EURE.address
        case GNOSIS_GBPE_V2.id:
            return GNOSIS_GBPE.address
        default:
            return outputCurrency.address
    }
}

export const fetchBungeeIntentQuote = async ({
    request,
    networkMap,
    networkRPCMap,
    signal,
}: {
    request: BungeeIntentQuoteRequest
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<BungeeIntentQuote | null> => {
    const fromNetwork = findNetworkByHexChainId(
        request.fromAmount.currency.networkHexChainId,
        networkMap
    )

    const toNetwork = findNetworkByHexChainId(
        request.toCurrency.networkHexChainId,
        networkMap
    )

    if (fromNetwork.nativeCurrency.id === request.fromAmount.currency.id) {
        return get(
            '/quote',
            {
                query: {
                    userAddress: request.fromAddress,
                    originChainId: Number(fromNetwork.hexChainId).toString(10),
                    inputToken: BUNGEE_NATIVE_TOKEN_ADDRESS,
                    inputAmount: request.fromAmount.amount.toString(),
                    receiverAddress: request.toAddress,
                    destinationChainId: Number(toNetwork.hexChainId).toString(
                        10
                    ),
                    outputToken: getOutputTokenAddress(
                        request.toCurrency,
                        networkMap
                    ),
                },
            },
            signal
        ).then((response) =>
            parseNativeQuote(response, request).getSuccessResultOrThrow(
                'Failed to parse Bungee native intent quote'
            )
        )
    }

    const [allowance, quote] = await Promise.all([
        fetchRPCResponseWithRetry2(
            requestAllowance({
                currency: request.fromAmount.currency,
                owner: request.fromAddress,
                spender: PERMIT_2_CONTRACT_ADDRESS,
            }),
            { network: fromNetwork, networkRPCMap, signal }
        ),
        get(
            '/quote',
            {
                query: {
                    userAddress: request.fromAddress,
                    originChainId: Number(fromNetwork.hexChainId).toString(10),
                    inputToken: request.fromAmount.currency.address,
                    inputAmount: request.fromAmount.amount.toString(),
                    receiverAddress: request.toAddress,
                    destinationChainId: Number(toNetwork.hexChainId).toString(
                        10
                    ),
                    outputToken: getOutputTokenAddress(
                        request.toCurrency,
                        networkMap
                    ),
                },
            },
            signal
        ).then((response) =>
            parseERC20Quote(response, request).getSuccessResultOrThrow(
                'Failed to parse Bungee ERC20 quote'
            )
        ),
    ])

    const permitApprovalTransaction =
        allowance.amount < request.fromAmount.amount
            ? createApprovalTransaction({
                  owner: request.fromAddress,
                  spender: PERMIT_2_CONTRACT_ADDRESS,
                  amount: {
                      amount: maxUint256, // FIXME :: @Nicvaniek double check if we want to do this
                      currency: request.fromAmount.currency,
                  },
              })
            : null

    return quote
        ? {
              ...quote,
              type: 'bungee_erc20_intent_quote',
              permitApprovalTransaction,
          }
        : null
}

const parseERC20Quote = (
    response: unknown,
    request: BungeeIntentQuoteRequest
): Result<unknown, ERC20RawQuote | null> =>
    object(response).andThen((obj) =>
        nullableOf(obj.autoRoute, (routeInput) =>
            object(routeInput).andThen((autoRoute) =>
                shape({
                    quoteId: string(autoRoute.quoteId),
                    requestHash: Hexadecimal.parse(autoRoute.requestHash),
                    witness: object(autoRoute.signTypedData).andThen((v4) =>
                        object(v4.values).andThen((values) =>
                            object(values.witness).map(
                                (witness) => witness as BungeeRawWitness
                            )
                        )
                    ),
                    quoteRequestType: string(autoRoute.requestType).map(
                        (type) => type as BungeeRawRequestType
                    ),
                    signTypedData: object(autoRoute.signTypedData)
                        .map((d) => ({
                            // SEEMS LIKE ORDER MATERS
                            types: d.types,
                            primaryType: 'PermitWitnessTransferFrom',
                            message: d.values,
                            domain: d.domain,
                        }))
                        .map((typedData) => JSON.stringify(typedData))
                        .map(
                            (strTypedData): EthSignTypedDataV4 => ({
                                id: generateRandomNumber(),
                                jsonrpc: '2.0',
                                method: 'eth_signTypedData_v4',
                                params: [request.fromAddress, strTypedData],
                            })
                        ),
                    outputAmount: object(autoRoute.output).andThen((output) =>
                        bigint(output.amount).map(
                            (amount): CryptoMoney => ({
                                amount,
                                currency: request.toCurrency,
                            })
                        )
                    ),
                })
            )
        )
    )

const parseNativeQuote = (
    response: unknown,
    request: BungeeIntentQuoteRequest
): Result<unknown, BungeeNativeIntentQuote | null> =>
    object(response).andThen((obj) =>
        nullableOf(obj.autoRoute, (routeInput) =>
            object(routeInput).andThen((autoRoute) =>
                shape({
                    type: success('bungee_native_token_intent_quote' as const),
                    quoteId: string(autoRoute.quoteId),
                    requestHash: Hexadecimal.parse(autoRoute.requestHash),
                    transaction: object(autoRoute.txData).andThen((tx) =>
                        shape({
                            to: Web3.address.parse(tx.to),
                            data: Hexadecimal.parse(tx.data),
                            value: bigint(tx.value),
                        }).map(
                            ({ to, data, value }): EthSendTransaction => ({
                                id: generateRandomNumber(),
                                jsonrpc: '2.0',
                                method: 'eth_sendTransaction',
                                params: [
                                    {
                                        from: request.fromAddress,
                                        to,
                                        data,
                                        value: Hexadecimal.fromBigInt(value),
                                    },
                                ],
                            })
                        )
                    ),
                    outputAmount: object(autoRoute.output).andThen((output) =>
                        bigint(output.amount).map(
                            (amount): CryptoMoney => ({
                                amount,
                                currency: request.toCurrency,
                            })
                        )
                    ),
                })
            )
        )
    )
