import {
    BungeeRawRequestType,
    BungeeRawWitness,
    post,
} from '@zeal/api/bungeeApi'

import { Hexadecimal, parse } from '@zeal/toolkit/Hexadecimal'
import { object } from '@zeal/toolkit/Result'

export const submitBungeeIntentQuote = async ({
    quoteId,
    quoteWiness,
    userSignature,
    quoteRequestType,
    signal,
}: {
    quoteId: string
    quoteWiness: BungeeRawWitness
    quoteRequestType: BungeeRawRequestType
    userSignature: Hexadecimal
    signal?: AbortSignal
}): Promise<Hexadecimal> => {
    const response = await post(
        '/submit',
        {
            body: {
                request: quoteWiness,
                userSignature,
                requestType: quoteRequestType,
                quoteId,
            },
            query: undefined,
        },
        signal
    )

    return object(response)
        .andThen((obj) => parse(obj.requestHash))
        .getSuccessResultOrThrow(
            'Cannot parse requestHash from response from <PERSON>ung<PERSON>'
        )
}
