import { get } from '@zeal/api/bungeeApi'

import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import {
    array,
    arrayOfLength,
    match,
    number,
    object,
    oneOf,
    Result,
    shape,
    ValidObject,
} from '@zeal/toolkit/Result'

enum BungeeRequestStatus {
    PENDING = 0,
    ASSIGNED = 1,
    EXTRACTED = 2,
    FULFILLED = 3,
    SETTLED = 4,
    EXPIRED = 5,
    CANCELLED = 6,
    REFUNDED = 7,
}

export type BungeeIntentQuoteStatus =
    | {
          type: 'in_progress'
      }
    | {
          type: 'completed'
          sourceTxHash: Hexadecimal.Hexadecimal
          destinationTxHash: Hexadecimal.Hexadecimal
          completedAtMs: number
      }
    | { type: 'expired' }
    | { type: 'cancelled' }
    | { type: 'refunded'; refundTxHash: Hexadecimal.Hexadecimal }

export const fetchBungeeIntentQuoteStatus = ({
    requestHash,
    signal,
}: {
    requestHash: Hexadecimal.Hexadecimal
    signal?: AbortSignal
}): Promise<BungeeIntentQuoteStatus> =>
    get('/status', { query: { requestHash } }, signal).then((response) =>
        parseQuoteStatus(response).getSuccessResultOrThrow(
            'Failed to parse bungee intent quote status'
        )
    )

const parseQuoteStatus = (
    input: unknown
): Result<unknown, BungeeIntentQuoteStatus> =>
    array(input)
        .andThen((arr) => arrayOfLength(1, arr))
        .andThen(([result]) => object(result))
        .andThen((obj) =>
            oneOf(obj, [
                parseCompletedQuote(obj),
                parseRefundedQuote(obj),
                parseInProgressQuote(obj),

                match(obj.bungeeStatusCode, BungeeRequestStatus.EXPIRED).map(
                    () => ({ type: 'expired' as const })
                ),
                match(obj.bungeeStatusCode, BungeeRequestStatus.CANCELLED).map(
                    () => ({ type: 'cancelled' as const })
                ),
            ])
        )

const parseInProgressQuote = (
    obj: ValidObject
): Result<unknown, Extract<BungeeIntentQuoteStatus, { type: 'in_progress' }>> =>
    oneOf(obj, [
        match(obj.bungeeStatusCode, BungeeRequestStatus.PENDING),
        match(obj.bungeeStatusCode, BungeeRequestStatus.ASSIGNED),
        match(obj.bungeeStatusCode, BungeeRequestStatus.EXTRACTED),
    ]).map(() => ({ type: 'in_progress' }))

export const parseRefundedQuote = (
    obj: ValidObject
): Result<unknown, Extract<BungeeIntentQuoteStatus, { type: 'refunded' }>> =>
    shape({
        bungeeStatusCode: match(
            obj.bungeeStatusCode,
            BungeeRequestStatus.REFUNDED
        ),
        refundTxHash: object(obj.refund).andThen((refundObj) =>
            Hexadecimal.parse(refundObj.txHash)
        ),
    }).map(({ refundTxHash }) => ({
        type: 'refunded',
        refundTxHash,
    }))

export const parseCompletedQuote = (
    obj: ValidObject
): Result<unknown, Extract<BungeeIntentQuoteStatus, { type: 'completed' }>> =>
    shape({
        bungeeStatusCode: oneOf(obj.bungeeStatusCode, [
            match(obj.bungeeStatusCode, BungeeRequestStatus.FULFILLED),
            match(obj.bungeeStatusCode, BungeeRequestStatus.SETTLED),
        ]),
        sourceTxHash: object(obj.originData).andThen((originObj) =>
            Hexadecimal.parse(originObj.txHash)
        ),
        destinationData: object(obj.destinationData).andThen((destinationObj) =>
            shape({
                destinationTxHash: Hexadecimal.parse(destinationObj.txHash),
                completedAtMs: number(destinationObj.timestamp).map(
                    (timeInSeconds) => timeInSeconds * 1000
                ),
            })
        ),
    }).map(
        ({
            sourceTxHash,
            destinationData: { destinationTxHash, completedAtMs },
        }) => ({
            type: 'completed',
            sourceTxHash,
            destinationTxHash,
            completedAtMs,
        })
    )
