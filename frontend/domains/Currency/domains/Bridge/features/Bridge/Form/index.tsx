import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useLazyLoadableData } from '@zeal/toolkit/LoadableData/LazyLoadableData'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { keys } from '@zeal/toolkit/Object'
import { useLiveRef } from '@zeal/toolkit/React'

import { Account } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyId,
    CurrencyPinMap,
    GasCurrencyPresetMap,
    KnownCurrencies,
} from '@zeal/domains/Currency'
import {
    CurrenciesMatrix,
    CurrenciesMatrixItem,
} from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import { fetchFeeAdjustedBalance } from '@zeal/domains/Currency/api/fetchFeeAdjustedBalance'
import {
    BridgeRequest,
    BridgeRouteRequest,
} from '@zeal/domains/Currency/domains/Bridge'
import { fetchBridgeRoutes } from '@zeal/domains/Currency/domains/Bridge/api/fetchBridgeRoutes'
import { BRIDGE_SPONSOR_NETWORKS } from '@zeal/domains/Currency/domains/Bridge/constants'
import { DEFAULT_SWAP_SLIPPAGE_PERCENT } from '@zeal/domains/Currency/domains/SwapQuote/constants'
import { getCryptoCurrencyOrThrow } from '@zeal/domains/Currency/helpers/getCryptoCurrency'
import { searchCryptoCurrencies } from '@zeal/domains/Currency/helpers/searchCryptoCurrencies'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStore, KeyStoreMap, Safe4337 } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import {
    Network,
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
} from '@zeal/domains/Network'
import {
    findNetworkByHexChainId,
    GNOSIS,
    OPTIMISM,
} from '@zeal/domains/Network/constants'
import { Portfolio2, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { fetchGasAbstractionTransactionFeesForMetaTransactions } from '@zeal/domains/UserOperation/api/fetchGasAbstractionTransactionFeesForMetaTransactions'
import { ethSendTransactionToMetaTransactionData } from '@zeal/domains/UserOperation/helpers/ethSendTransactionToMetaTransactionData'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

const DEFAULT_FROM_CURRENCY = OPTIMISM.nativeCurrency
const DEFAULT_TO_CURRENCY = GNOSIS.nativeCurrency

const DEFAULT_FROM_NETWORK: PredefinedNetwork = OPTIMISM
const DEFAULT_TO_NETWORK: PredefinedNetwork = GNOSIS

type Props = {
    account: Account
    installationId: string
    portfolio: Portfolio2
    keystoreMap: KeyStoreMap
    fromCurrencyId: CurrencyId | null
    currenciesMatrix: CurrenciesMatrix
    swapSlippagePercent: number | null
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    gasCurrencyPresetMap: GasCurrencyPresetMap
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | Extract<MsgOf<typeof Layout>, { type: 'on_bridge_continue_clicked' }>
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_set_slippage_percent'
                  | 'on_rpc_change_confirmed'
                  | 'on_select_rpc_click'
          }
      >

const selectCurrencyInNetwork = ({
    currencyIds,
    knownCurrencies,
    network,
}: {
    currencyIds: CurrencyId[]
    network: Network
    knownCurrencies: KnownCurrencies
}): CurrencyId =>
    currencyIds.find((id) => {
        const currency = knownCurrencies[id]

        if (!currency) {
            throw new ImperativeError(
                'Failed to find currency in dictionary while defaulting curency for network'
            )
        }

        switch (currency.type) {
            case 'FiatCurrency':
                return false
            case 'CryptoCurrency':
                return (
                    currency.networkHexChainId === network.hexChainId &&
                    currency.address === network.nativeCurrency.address
                )

            /* istanbul ignore next */
            default:
                return notReachable(currency)
        }
    }) || currencyIds[0]

const getFromCurrencyIds = ({
    currenciesMatrix: { currencies },
}: {
    currenciesMatrix: CurrenciesMatrix
}): Set<CurrencyId> =>
    new Set(
        keys(currencies).flatMap((fromNetworkHexId) =>
            keys(currencies[fromNetworkHexId] || {}).flatMap(
                (toNetworkHexId) => {
                    const matrixItem: CurrenciesMatrixItem | null =
                        currencies[fromNetworkHexId]?.[toNetworkHexId] || null
                    return matrixItem ? matrixItem.from : []
                }
            )
        )
    )

const getToCurrencyIds = ({
    currenciesMatrix,
    fromCurrency,
}: {
    currenciesMatrix: CurrenciesMatrix
    fromCurrency: CryptoCurrency
}): Set<CurrencyId> => {
    const { currencies } = currenciesMatrix
    const fromNetworkHexId = fromCurrency.networkHexChainId

    const toNetworks = keys(
        currencies[fromCurrency.networkHexChainId] || {}
    ).filter((hexId) => hexId !== fromCurrency.networkHexChainId)

    return new Set(
        toNetworks.flatMap((toNetworkHexId) => {
            const matrixItem: CurrenciesMatrixItem | null =
                currencies[fromNetworkHexId]?.[toNetworkHexId] || null
            return matrixItem ? matrixItem.to : []
        })
    )
}

const getInitialFromCurrency = ({
    currenciesMatrix,
    fromCurrencyId,
    networkMap,
    serverPortfolio,
    currencyHiddenMap,
    currencyPinMap,
}: {
    fromCurrencyId: CurrencyId | null
    currenciesMatrix: CurrenciesMatrix
    networkMap: NetworkMap
    serverPortfolio: ServerPortfolio2
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
}): CryptoCurrency => {
    const { knownCurrencies } = currenciesMatrix
    const fromCurrencies = getFromCurrencyIds({ currenciesMatrix })

    const selectedFromCurrency =
        fromCurrencyId &&
        knownCurrencies[fromCurrencyId] &&
        fromCurrencies.has(fromCurrencyId)
            ? getCryptoCurrencyOrThrow({
                  cryptoCurrencyId: fromCurrencyId,
                  knownCurrencies,
              })
            : null

    if (selectedFromCurrency) {
        return selectedFromCurrency
    }

    const sortedPortfolioCurrencies = searchCryptoCurrencies({
        currencies: serverPortfolio.tokens.map(
            (token) => token.balance.currency
        ),
        searchTerm: '',
        portfolio: serverPortfolio,
        currencyPinMap,
        currencyHiddenMap,
        networkMap,
    })

    switch (sortedPortfolioCurrencies.type) {
        case 'grouped_results': {
            const firstCurrency =
                sortedPortfolioCurrencies.portfolioCurrencies[0] || null

            return firstCurrency && fromCurrencies.has(firstCurrency.id)
                ? firstCurrency
                : DEFAULT_FROM_CURRENCY
        }

        case 'no_currencies_found':
            return DEFAULT_FROM_CURRENCY

        default:
            return notReachable(sortedPortfolioCurrencies)
    }
}

const getInitialToCurrency = ({
    currenciesMatrix,
    fromCurrency,
    networkMap,
    serverPortfolio,
    currencyHiddenMap,
    currencyPinMap,
}: {
    fromCurrency: CryptoCurrency
    currenciesMatrix: CurrenciesMatrix
    networkMap: NetworkMap
    serverPortfolio: ServerPortfolio2
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
}): CryptoCurrency => {
    const toCurrencies: Set<CurrencyId> = getToCurrencyIds({
        currenciesMatrix,
        fromCurrency,
    })

    const portfolioCurrenciesToSelectFrom = serverPortfolio.tokens
        .map((token) => token.balance.currency)
        .filter((currency) => toCurrencies.has(currency.id))

    const sortedPortfolioCurrencies = searchCryptoCurrencies({
        currencies: portfolioCurrenciesToSelectFrom,
        searchTerm: '',
        portfolio: serverPortfolio,
        currencyPinMap,
        currencyHiddenMap,
        networkMap,
    })

    switch (sortedPortfolioCurrencies.type) {
        case 'grouped_results': {
            const firstCurrency =
                sortedPortfolioCurrencies.portfolioCurrencies[0] || null

            return firstCurrency && toCurrencies.has(firstCurrency.id)
                ? firstCurrency
                : DEFAULT_TO_CURRENCY
        }

        case 'no_currencies_found':
            return DEFAULT_TO_CURRENCY

        default:
            return notReachable(sortedPortfolioCurrencies)
    }
}

const initForm = ({
    currenciesMatrix,
    fromCurrencyId,
    fromAccount,
    swapSlippagePercent,
    networkMap,
    serverPortfolio,
    networkRPCMap,
    defaultCurrencyConfig,
    currencyHiddenMap,
    currencyPinMap,
}: {
    fromAccount: Account
    fromCurrencyId: CurrencyId | null
    currenciesMatrix: CurrenciesMatrix
    swapSlippagePercent: number | null
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    serverPortfolio: ServerPortfolio2
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
}): BridgeRouteRequest => {
    const { knownCurrencies } = currenciesMatrix

    const fromCurrency = getInitialFromCurrency({
        currenciesMatrix,
        fromCurrencyId,
        networkMap,
        serverPortfolio,
        currencyPinMap,
        currencyHiddenMap,
    })

    const toCurrency = getInitialToCurrency({
        currenciesMatrix,
        fromCurrency: fromCurrency,
        networkMap,
        serverPortfolio,
        currencyPinMap,
        currencyHiddenMap,
    })

    return {
        fromCurrency,
        toCurrency,
        slippagePercent: swapSlippagePercent || DEFAULT_SWAP_SLIPPAGE_PERCENT,

        networkRPCMap,
        bridgeRouteName: null,
        fromAmount: null,
        refuel: false,
        knownCurrencies,
        fromAccount,
        networkMap,
        defaultCurrencyConfig,
    }
}

const setFromNetwork = ({
    newFromNetwork,
    currenciesMatrix,
    form,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
}: {
    newFromNetwork: Network
    currenciesMatrix: CurrenciesMatrix
    form: BridgeRouteRequest
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
}):
    | { type: 'routes_found'; request: BridgeRouteRequest }
    | { type: 'routes_not_found' } => {
    const { knownCurrencies } = currenciesMatrix
    const currentToNetworkHexId = form.toCurrency.networkHexChainId

    // If we can keep existing network - we keep it. If we can't we set it to Ethereum if new fromNetwork is not Ethereum or Poligon otherwise
    const currentToNetworkMatrixItem =
        currenciesMatrix.currencies[newFromNetwork.hexChainId]?.[
            currentToNetworkHexId
        ] || null

    const newToNetworkHexId = currentToNetworkMatrixItem
        ? currentToNetworkHexId
        : newFromNetwork.hexChainId === DEFAULT_FROM_NETWORK.hexChainId
          ? DEFAULT_TO_NETWORK.hexChainId
          : DEFAULT_FROM_NETWORK.hexChainId

    const newToNetworkMatrixItem =
        currenciesMatrix.currencies[newFromNetwork.hexChainId]?.[
            newToNetworkHexId
        ] || null

    if (
        !newToNetworkMatrixItem ||
        newToNetworkMatrixItem.from.length === 0 ||
        newToNetworkMatrixItem.to.length === 0
    ) {
        return { type: 'routes_not_found' }
    }

    const { from, to } = newToNetworkMatrixItem

    const fromCurrencyId = selectCurrencyInNetwork({
        currencyIds: from,
        network: newFromNetwork,
        knownCurrencies,
    })

    const toCurrencyId =
        newToNetworkHexId === currentToNetworkHexId
            ? form.toCurrency.id
            : selectCurrencyInNetwork({
                  currencyIds: to,
                  network: findNetworkByHexChainId(
                      newToNetworkHexId,
                      networkMap
                  ),
                  knownCurrencies,
              })

    return {
        type: 'routes_found',
        request: {
            networkRPCMap,
            bridgeRouteName: null,
            fromAmount: null,
            refuel: false,
            slippagePercent: form.slippagePercent,
            fromAccount: form.fromAccount,
            knownCurrencies: form.knownCurrencies,
            fromCurrency: getCryptoCurrencyOrThrow({
                cryptoCurrencyId: fromCurrencyId,
                knownCurrencies,
            }),
            toCurrency: getCryptoCurrencyOrThrow({
                cryptoCurrencyId: toCurrencyId,
                knownCurrencies,
            }),
            networkMap,
            defaultCurrencyConfig,
        },
    }
}

const setToNetwork = ({
    newToNetwork,
    currenciesMatrix,
    form,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
}: {
    newToNetwork: Network
    currenciesMatrix: CurrenciesMatrix
    form: BridgeRouteRequest
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
}):
    | { type: 'routes_found'; request: BridgeRouteRequest }
    | { type: 'routes_not_found' } => {
    const { knownCurrencies } = currenciesMatrix

    const to =
        currenciesMatrix.currencies[form.fromCurrency.networkHexChainId]?.[
            newToNetwork.hexChainId
        ]?.to || []

    if (to.length === 0) {
        return { type: 'routes_not_found' }
    }
    const toCurrencyId = selectCurrencyInNetwork({
        currencyIds: to,
        network: newToNetwork,
        knownCurrencies,
    })

    const toCurrency = getCryptoCurrencyOrThrow({
        cryptoCurrencyId: toCurrencyId,
        knownCurrencies,
    })

    return {
        type: 'routes_found',
        request: {
            networkRPCMap,
            bridgeRouteName: null,
            fromAmount: form.fromAmount,
            refuel: false,
            slippagePercent: form.slippagePercent,
            fromAccount: form.fromAccount,
            knownCurrencies: form.knownCurrencies,
            fromCurrency: form.fromCurrency,
            toCurrency,
            networkMap,
            defaultCurrencyConfig,
        },
    }
}

const fetchMaxBalance = async ({
    serverPortfolio,
    bridgeRouteRequest,
    gasCurrencyPresetMap,
    keyStore,
    signal,
}: {
    bridgeRouteRequest: BridgeRouteRequest
    serverPortfolio: ServerPortfolio2
    keyStore: KeyStore
    gasCurrencyPresetMap: GasCurrencyPresetMap
    signal?: AbortSignal
}): Promise<CryptoMoney> => {
    const quote = await fetchBridgeRoutes({ ...bridgeRouteRequest, signal })

    switch (quote.type) {
        case 'Failure':
            return getBalanceByCryptoCurrency2({
                currency: bridgeRouteRequest.fromCurrency,
                serverPortfolio,
            })

        case 'Success':
            const network = findNetworkByHexChainId(
                bridgeRouteRequest.fromCurrency.networkHexChainId,
                bridgeRouteRequest.networkMap
            )

            const route = quote.data[0].route

            return fetchFeeAdjustedBalance({
                fromAddress: bridgeRouteRequest.fromAccount.address,
                defaultCurrencyConfig: bridgeRouteRequest.defaultCurrencyConfig,
                networkMap: bridgeRouteRequest.networkMap,
                networkRPCMap: bridgeRouteRequest.networkRPCMap,
                fromCurrency: bridgeRouteRequest.fromCurrency,
                gasCurrencyPresetMap,
                keyStore,
                network,
                networksToSponsor: BRIDGE_SPONSOR_NETWORKS,
                serverPortfolio,
                requests: [
                    route.approvalTransaction,
                    route.sourceTransaction,
                ].filter(excludeNullValues),
                actionSource: {
                    type: 'internal',
                    transactionEventSource: 'bridgeGasEstimation',
                },
                signal,
            })

        default:
            return notReachable(quote)
    }
}

const fetchBridgeRouteGasFee = async ({
    bridgeRequest,
    keyStore,
    networkRPCMap,
    serverPortfolio,
    defaultCurrencyConfig,
    networkMap,
    signal,
}: {
    bridgeRequest: BridgeRequest
    keyStore: Safe4337
    networkRPCMap: NetworkRPCMap
    serverPortfolio: ServerPortfolio2 | null
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<BridgeRequest> => {
    return fetchGasAbstractionTransactionFeesForMetaTransactions({
        metaTransactionDatas: [
            bridgeRequest.route.approvalTransaction,
            bridgeRequest.route.sourceTransaction,
        ]
            .filter(excludeNullValues)
            .map(ethSendTransactionToMetaTransactionData),
        network: findNetworkByHexChainId(
            bridgeRequest.route.from.currency.networkHexChainId,
            networkMap
        ),
        keyStore,
        sponsored: BRIDGE_SPONSOR_NETWORKS.includes(
            bridgeRequest.route.from.currency.networkHexChainId
        ),
        networkRPCMap,
        serverPortfolio,
        defaultCurrencyConfig,
        callGasLimitBuffer: 0n,
        networkMap,
        actionSource: {
            type: 'internal',
            transactionEventSource: 'bridgeGasEstimation',
        },
        signal,
    }).then(() => bridgeRequest)
}
export const Form = ({
    account,
    portfolio,
    fromCurrencyId,
    currenciesMatrix,
    keystoreMap,
    swapSlippagePercent,
    networkMap,
    networkRPCMap,
    currencyHiddenMap,
    currencyPinMap,
    installationId,
    defaultCurrencyConfig,
    gasCurrencyPresetMap,
    onMsg,
}: Props) => {
    const [pollable, setPollable] = usePollableData(
        fetchBridgeRoutes,
        () => ({
            type: 'loading',
            params: initForm({
                networkRPCMap,
                fromCurrencyId,
                currenciesMatrix,
                swapSlippagePercent,
                fromAccount: account,
                networkMap,
                serverPortfolio: portfolio,
                defaultCurrencyConfig,
                currencyHiddenMap,
                currencyPinMap,
            }),
        }),
        {
            pollIntervalMilliseconds: 60_000,
        }
    )

    const fromCurrencyBalance = getBalanceByCryptoCurrency2({
        currency: pollable.params.fromCurrency,
        serverPortfolio: portfolio,
    })

    const [gasFeeLoadable, setGasFeeLoadable] = useLazyLoadableData(
        fetchBridgeRouteGasFee,
        {
            type: 'not_asked',
        }
    )

    const onLiveMsg = useLiveRef(onMsg)
    useEffect(() => {
        switch (gasFeeLoadable.type) {
            case 'not_asked':
            case 'loading':
            case 'error':
                break
            case 'loaded':
                onLiveMsg.current({
                    type: 'on_bridge_continue_clicked',
                    route: gasFeeLoadable.data,
                })
                break

            /* istanbul ignore next */
            default:
                notReachable(gasFeeLoadable)
        }
    }, [gasFeeLoadable, onLiveMsg])

    const [maxBalanceLoadable, setMaxBalanceLoadable] = useLoadableData(
        fetchMaxBalance,
        {
            type: 'loading',
            params: {
                bridgeRouteRequest: {
                    ...pollable.params,
                    fromAmount: toFixedWithFraction(
                        fromCurrencyBalance.amount,
                        fromCurrencyBalance.currency.fraction
                    ),
                },
                serverPortfolio: portfolio,
                gasCurrencyPresetMap,
                keyStore: getKeyStore({
                    address: account.address,
                    keyStoreMap: keystoreMap,
                }),
            },
        }
    )

    useEffect(() => {
        const fromBalance = getBalanceByCryptoCurrency2({
            currency: pollable.params.fromCurrency,
            serverPortfolio: portfolio,
        })

        setMaxBalanceLoadable((old) => ({
            ...old,
            type: 'loading',
            params: {
                ...old.params,
                bridgeRouteRequest: {
                    fromAmount: toFixedWithFraction(
                        fromBalance.amount,
                        fromBalance.currency.fraction
                    ),
                    defaultCurrencyConfig:
                        pollable.params.defaultCurrencyConfig,
                    fromAccount: pollable.params.fromAccount,
                    networkMap: pollable.params.networkMap,
                    networkRPCMap: pollable.params.networkRPCMap,
                    slippagePercent: pollable.params.slippagePercent,
                    bridgeRouteName: pollable.params.bridgeRouteName,
                    toCurrency: pollable.params.toCurrency,
                    fromCurrency: pollable.params.fromCurrency,
                    knownCurrencies: pollable.params.knownCurrencies,
                    refuel: pollable.params.refuel,
                },
                serverPortfolio: portfolio,
            },
        }))
    }, [
        pollable.params.bridgeRouteName,
        pollable.params.defaultCurrencyConfig,
        pollable.params.fromAccount,
        pollable.params.fromCurrency,
        pollable.params.knownCurrencies,
        pollable.params.networkMap,
        pollable.params.networkRPCMap,
        pollable.params.refuel,
        pollable.params.slippagePercent,
        pollable.params.toCurrency,
        portfolio,
        setMaxBalanceLoadable,
    ])

    useEffect(() => {
        switch (maxBalanceLoadable.type) {
            case 'loaded':
            case 'loading':
                break
            case 'error':
                captureError(maxBalanceLoadable.error)
                break

            /* istanbul ignore next */
            default:
                notReachable(maxBalanceLoadable)
        }
    }, [maxBalanceLoadable])

    useEffect(() => {
        switch (pollable.type) {
            case 'loaded':
            case 'reloading':
            case 'loading':
                break
            case 'subsequent_failed':
            case 'error':
                captureError(pollable.error)
                break

            /* istanbul ignore next */
            default:
                notReachable(pollable)
        }
    }, [pollable])

    const [modalState, setModalState] = useState<ModalState>({ type: 'closed' })

    const keyStore = getKeyStore({
        address: pollable.params.fromAccount.address,
        keyStoreMap: keystoreMap,
    })

    return (
        <>
            <Layout
                installationId={installationId}
                gasFeeLoadable={gasFeeLoadable}
                maxBalanceLoadable={maxBalanceLoadable}
                networkMap={networkMap}
                currenciesMatrix={currenciesMatrix}
                pollable={pollable}
                serverPortfolio={portfolio}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            onMsg(msg)
                            break
                        case 'on_from_network_click':
                            setModalState({ type: 'select_from_network' })
                            break
                        case 'on_to_network_click':
                            setModalState({ type: 'select_to_network' })
                            break
                        case 'on_from_currency_click':
                            setModalState({
                                type: 'select_from_network_and_currency',
                            })
                            break
                        case 'on_to_currency_click':
                            setModalState({
                                type: 'select_to_network_and_currency',
                            })
                            break
                        case 'on_refuel_add_click':
                            setGasFeeLoadable({ type: 'not_asked' })
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    refuel: true,
                                },
                            })
                            break
                        case 'on_refuel_remove_click':
                            setGasFeeLoadable({ type: 'not_asked' })
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    refuel: false,
                                },
                            })
                            break
                        case 'on_route_click':
                            setModalState({ type: 'select_route' })
                            break
                        case 'on_amount_change':
                            setGasFeeLoadable({ type: 'not_asked' })
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    fromAmount: msg.amount,
                                },
                            })
                            break

                        case 'on_try_again_clicked':
                            setGasFeeLoadable({ type: 'not_asked' })
                            setPollable({
                                type: 'loading',
                                params: pollable.params,
                            })
                            break

                        case 'on_bridge_continue_clicked': {
                            switch (keyStore.type) {
                                case 'safe_4337':
                                    setGasFeeLoadable({
                                        type: 'loading',
                                        params: {
                                            bridgeRequest: msg.route,
                                            keyStore,
                                            networkMap,
                                            networkRPCMap,
                                            serverPortfolio: portfolio,
                                            defaultCurrencyConfig,
                                        },
                                    })
                                    break
                                case 'private_key_store':
                                case 'ledger':
                                case 'secret_phrase_key':
                                case 'trezor':
                                case 'track_only':
                                    onMsg(msg)
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(keyStore)
                            }
                            break
                        }

                        case 'on_slippage_clicked':
                            setModalState({ type: 'set_slippage' })
                            break

                        case 'on_fields_switch_clicked':
                            const { fromCurrency, toCurrency } = pollable.params
                            const request = msg.bridgeRequest

                            const newFromAmount: string | null = request
                                ? toFixedWithFraction(
                                      request.route.to.amount,
                                      toCurrency.fraction
                                  )
                                : null

                            setGasFeeLoadable({ type: 'not_asked' })
                            setPollable({
                                type: 'loading',
                                params: {
                                    networkRPCMap,
                                    fromCurrency: toCurrency,
                                    fromAmount: newFromAmount,
                                    toCurrency: fromCurrency,

                                    fromAccount: pollable.params.fromAccount,
                                    bridgeRouteName: null,
                                    knownCurrencies:
                                        pollable.params.knownCurrencies,
                                    networkMap: pollable.params.networkMap,
                                    refuel: pollable.params.refuel,
                                    slippagePercent:
                                        pollable.params.slippagePercent,
                                    defaultCurrencyConfig,
                                },
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                defaultCurrencyConfig={defaultCurrencyConfig}
                installationId={installationId}
                currencyHiddenMap={currencyHiddenMap}
                currencyPinMap={currencyPinMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                account={account}
                keystoreMap={keystoreMap}
                portfolio={portfolio}
                currenciesMatrix={currenciesMatrix}
                pollable={pollable}
                state={modalState}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_routes_not_found_cancel_clicked':
                            setModalState({ type: 'closed' })
                            break
                        case 'on_from_network_selected': {
                            const params = setFromNetwork({
                                networkRPCMap,
                                currenciesMatrix,
                                form: pollable.params,
                                newFromNetwork: msg.network,
                                networkMap,
                                defaultCurrencyConfig,
                            })
                            switch (params.type) {
                                case 'routes_found':
                                    setModalState({ type: 'closed' })
                                    setGasFeeLoadable({ type: 'not_asked' })
                                    setPollable({
                                        type: 'loading',
                                        params: params.request,
                                    })
                                    break
                                case 'routes_not_found': {
                                    const network = findNetworkByHexChainId(
                                        pollable.params.toCurrency
                                            .networkHexChainId,
                                        networkMap
                                    )
                                    setModalState({
                                        type: 'routes_not_found',
                                        fromNetworkName: msg.network.name,
                                        toNetworkName: network.name,
                                    })
                                    break
                                }

                                default:
                                    notReachable(params)
                            }

                            break
                        }

                        case 'on_to_network_selected': {
                            const params = setToNetwork({
                                networkRPCMap,
                                currenciesMatrix,
                                form: pollable.params,
                                newToNetwork: msg.network,
                                networkMap,
                                defaultCurrencyConfig,
                            })
                            switch (params.type) {
                                case 'routes_found':
                                    setModalState({ type: 'closed' })
                                    setGasFeeLoadable({ type: 'not_asked' })
                                    setPollable({
                                        type: 'loading',
                                        params: params.request,
                                    })
                                    break
                                case 'routes_not_found': {
                                    const network = findNetworkByHexChainId(
                                        pollable.params.fromCurrency
                                            .networkHexChainId,
                                        networkMap
                                    )
                                    setModalState({
                                        type: 'routes_not_found',
                                        fromNetworkName: network.name,
                                        toNetworkName: msg.network.name,
                                    })
                                    break
                                }

                                default:
                                    notReachable(params)
                            }

                            break
                        }

                        case 'on_from_currency_selected':
                            setModalState({ type: 'closed' })
                            setGasFeeLoadable({ type: 'not_asked' })
                            setPollable({
                                type: 'loading',
                                params: {
                                    networkRPCMap,
                                    knownCurrencies:
                                        pollable.params.knownCurrencies,
                                    slippagePercent:
                                        pollable.params.slippagePercent,
                                    fromCurrency: getCryptoCurrencyOrThrow({
                                        cryptoCurrencyId: msg.currencyId,
                                        knownCurrencies:
                                            currenciesMatrix.knownCurrencies,
                                    }),
                                    toCurrency: pollable.params.toCurrency,
                                    bridgeRouteName: null,
                                    refuel: pollable.params.refuel,
                                    fromAmount: null,
                                    fromAccount: account,
                                    networkMap,
                                    defaultCurrencyConfig,
                                },
                            })
                            break
                        case 'on_to_currency_selected':
                            setModalState({ type: 'closed' })
                            setGasFeeLoadable({ type: 'not_asked' })
                            setPollable({
                                type: 'loading',
                                params: {
                                    networkRPCMap,
                                    networkMap,
                                    knownCurrencies:
                                        pollable.params.knownCurrencies,
                                    slippagePercent:
                                        pollable.params.slippagePercent,
                                    fromCurrency: pollable.params.fromCurrency,
                                    toCurrency: getCryptoCurrencyOrThrow({
                                        cryptoCurrencyId: msg.currencyId,
                                        knownCurrencies:
                                            currenciesMatrix.knownCurrencies,
                                    }),
                                    bridgeRouteName: null,
                                    refuel: pollable.params.refuel,
                                    fromAmount: pollable.params.fromAmount,
                                    fromAccount: account,
                                    defaultCurrencyConfig,
                                },
                            })
                            break
                        case 'on_route_selected':
                            setModalState({ type: 'closed' })
                            setGasFeeLoadable({ type: 'not_asked' })
                            setPollable({
                                ...pollable,
                                params: {
                                    ...pollable.params,
                                    bridgeRouteName: msg.route.route.name,
                                },
                            })
                            break
                        case 'on_set_slippage_percent':
                            setGasFeeLoadable({ type: 'not_asked' })
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    slippagePercent: msg.slippagePercent,
                                },
                            })
                            setModalState({ type: 'closed' })
                            onMsg(msg)
                            break
                        case 'on_rpc_change_confirmed':
                        case 'on_select_rpc_click':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
