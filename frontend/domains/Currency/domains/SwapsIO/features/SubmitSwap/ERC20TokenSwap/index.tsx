import { useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import {
    SwapsIOERC20SwapQuote,
    SwapsIOSwapRequest,
} from '@zeal/domains/Currency/domains/SwapsIO'
import { FAKE_SWAPS_IO_DAPP } from '@zeal/domains/DApp/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    EthSendTransaction,
    EthSignTypedDataV4,
} from '@zeal/domains/RPCRequest'
import {
    OrderBuySignMessage,
    OrderCardTopupSignMessage,
    OrderEarnDepositBridge,
} from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { fetchSimulationByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import { fetchTransactionResultByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchTransactionResult'
import { SwapsioEventSource } from '@zeal/domains/UserEvents'

import { CreateSwapRequest } from './CreateSwapRequest'
import { SendSwap } from './SendSwap'
import { SignSwap } from './SignSwap'

import { Monitor } from '../Monitor'

type Props = {
    quote: SwapsIOERC20SwapQuote
    simulation:
        | OrderCardTopupSignMessage
        | OrderEarnDepositBridge
        | OrderBuySignMessage

    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    sessionPassword: string
    senderPortfolio: ServerPortfolio2
    defaultCurrencyConfig: DefaultCurrencyConfig
    source: SwapsioEventSource
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
          }
      >
    | Extract<MsgOf<typeof CreateSwapRequest>, { type: 'close' }>
    | Extract<
          MsgOf<typeof SignSwap>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
          }
      >
    | Extract<MsgOf<typeof SendSwap>, { type: 'close' }>
    | MsgOf<typeof Monitor>
    | {
          type: 'on_swaps_io_swap_request_created'
          request: SwapsIOSwapRequest
      }

type State =
    | {
          type: 'approve'
          transaction: EthSendTransaction
      }
    | {
          type: 'create_swap_request'
      }
    | {
          type: 'sign_swap'
          request: SwapsIOSwapRequest
          msgToSign: EthSignTypedDataV4
      }
    | {
          type: 'send_swap'
          request: SwapsIOSwapRequest
          msgToSign: EthSignTypedDataV4
          signature: Hexadecimal
      }
    | {
          type: 'monitor'
          request: SwapsIOSwapRequest
      }

const SWAPS_IO_APPROVE_ACTION_SOURCE: ActionSource2 = {
    type: 'internal',
    transactionEventSource: 'kinetexApprove',
    dAppSiteInfo: FAKE_SWAPS_IO_DAPP,
}

const calculateState = (quote: SwapsIOERC20SwapQuote): State => {
    if (quote.approvalTransaction) {
        return {
            type: 'approve',
            transaction: quote.approvalTransaction,
        }
    }
    return { type: 'create_swap_request' }
}

export const Erc20TokenSwap = ({
    quote,
    keyStoreMap,
    networkMap,
    senderPortfolio,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    networkRPCMap,
    onMsg,
    sessionPassword,
    simulation,
    installationId,
    defaultCurrencyConfig,
    source,
}: Props) => {
    const [state, setState] = useState<State>(calculateState(quote))
    const network = findNetworkByHexChainId(
        quote.from.currency.networkHexChainId,
        networkMap
    )

    switch (state.type) {
        case 'approve':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    sendTransactionRequests={[state.transaction]}
                    account={quote.sender}
                    network={network}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    portfolio={senderPortfolio}
                    accounts={accountsMap}
                    keystores={keyStoreMap}
                    networkMap={networkMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    fetchSimulationByRequest={fetchSimulationByRequest}
                    fetchTransactionResultByRequest={
                        fetchTransactionResultByRequest
                    }
                    state={{ type: 'maximised' }}
                    actionSource={SWAPS_IO_APPROVE_ACTION_SOURCE}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'on_safe_transaction_failure_accepted':
                                onMsg({ type: 'close' })
                                break

                            case 'cancel_submitted':
                            case 'transaction_submited':
                            case 'transaction_request_replaced':
                            case 'on_user_operation_bundled':
                                noop()
                                break
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_completed_transaction_close_click':
                            case 'on_completed_safe_transaction_close_click':
                                setState({
                                    type: 'create_swap_request',
                                })
                                break

                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in swaps IO erc20 swap $${msg.type}`
                                    )
                                )
                                onMsg({ type: 'close' })
                                break

                            case 'on_predefined_fee_preset_selected':
                            case 'import_keys_button_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'create_swap_request':
            return (
                <CreateSwapRequest
                    installationId={installationId}
                    networkMap={networkMap}
                    quote={quote}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_swap_requet_created':
                                setState({
                                    type: 'sign_swap',
                                    request: msg.request,
                                    msgToSign: msg.msgToSign,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'sign_swap':
            return (
                <SignSwap
                    source={source}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    quote={quote}
                    request={state.msgToSign}
                    sessionPassword={sessionPassword}
                    simulation={simulation}
                    network={network}
                    networkMap={networkMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'import_keys_button_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'close':
                                onMsg(msg)
                                break
                            case 'message_signed':
                                setState({
                                    type: 'send_swap',
                                    request: state.request,
                                    signature: msg.signature as Hexadecimal, // FIXME :: @mike can we change signMessage to return Hexadecimal?
                                    msgToSign: state.msgToSign,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                    networkRPCMap={networkRPCMap}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    senderPortfolio={senderPortfolio}
                />
            )
        case 'send_swap':
            return (
                <SendSwap
                    networkMap={networkMap}
                    source={source}
                    simulation={simulation}
                    sender={quote.sender}
                    installationId={installationId}
                    request={state.request}
                    signature={state.signature}
                    msgToSign={state.msgToSign}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_swap_submitted':
                                onMsg({
                                    type: 'on_swaps_io_swap_request_created',
                                    request: state.request,
                                })
                                setState({
                                    type: 'monitor',
                                    request: msg.request,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'monitor':
            return (
                <Monitor
                    installationId={installationId}
                    source={source}
                    simulation={simulation}
                    sender={quote.sender}
                    networkMap={networkMap}
                    request={state.request}
                    onMsg={onMsg}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
