import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'

import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import {
    fetchFeeAdjustedBalance,
    fetchFeeAdjustedBalanceForGas,
} from '@zeal/domains/Currency/api/fetchFeeAdjustedBalance'
import { SWAPS_IO_NATIVE_SWAP_GAS_AMOUNT } from '@zeal/domains/Currency/domains/SwapsIO/constants'
import { KeyStore } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import {
    DEFAULT_NETWORK_HEX_IDS_TO_SPONSOR,
    findNetworkByHexChainId,
} from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'

import { fetchQuote, SwapsIOQuoteRequest } from './fetchQuote'

export type MaxBalanceRequest = {
    swapQuoteRequest: SwapsIOQuoteRequest
    serverPortfolio: ServerPortfolio2
    keyStore: KeyStore
    gasCurrencyPresetMap: GasCurrencyPresetMap
    signal?: AbortSignal
}

export const fetchMaxBalance = async ({
    serverPortfolio,
    swapQuoteRequest,
    gasCurrencyPresetMap,
    keyStore,
    signal,
}: MaxBalanceRequest): Promise<CryptoMoney> => {
    const swapsIOQuote = await fetchQuote({ ...swapQuoteRequest, signal })

    switch (swapsIOQuote.type) {
        case 'Failure':
            return getBalanceByCryptoCurrency2({
                currency: swapQuoteRequest.fromCurrency,
                serverPortfolio,
            })

        case 'Success': {
            const network = findNetworkByHexChainId(
                swapQuoteRequest.fromCurrency.networkHexChainId,
                swapQuoteRequest.networkMap
            )

            switch (swapsIOQuote.data.type) {
                case 'swaps_io_quote_native_swap':
                    return fetchFeeAdjustedBalanceForGas({
                        gasEstimate: SWAPS_IO_NATIVE_SWAP_GAS_AMOUNT,
                        fromAddress: swapQuoteRequest.sender.address,
                        defaultCurrencyConfig:
                            swapQuoteRequest.defaultCurrencyConfig,
                        networkMap: swapQuoteRequest.networkMap,
                        networkRPCMap: swapQuoteRequest.networkRPCMap,
                        fromCurrency: swapQuoteRequest.fromCurrency,
                        gasCurrencyPresetMap,
                        keyStore,
                        network,
                        networksToSponsor: DEFAULT_NETWORK_HEX_IDS_TO_SPONSOR,
                        serverPortfolio,
                        actionSource: {
                            type: 'internal',
                            transactionEventSource: 'swapsIOGasEstimation',
                        },
                        signal,
                    })
                case 'swaps_io_quote_erc20_swap':
                    return fetchFeeAdjustedBalance({
                        fromAddress: swapQuoteRequest.sender.address,
                        defaultCurrencyConfig:
                            swapQuoteRequest.defaultCurrencyConfig,
                        networkMap: swapQuoteRequest.networkMap,
                        networkRPCMap: swapQuoteRequest.networkRPCMap,
                        fromCurrency: swapQuoteRequest.fromCurrency,
                        gasCurrencyPresetMap,
                        keyStore,
                        network,
                        networksToSponsor: DEFAULT_NETWORK_HEX_IDS_TO_SPONSOR,
                        serverPortfolio,
                        requests: [
                            swapsIOQuote.data.approvalTransaction,
                        ].filter(excludeNullValues),
                        actionSource: {
                            type: 'internal',
                            transactionEventSource: 'swapsIOGasEstimation',
                        },
                        signal,
                    })
                default:
                    return notReachable(swapsIOQuote.data)
            }
        }

        /* istanbul ignore next */
        default:
            return notReachable(swapsIOQuote)
    }
}
