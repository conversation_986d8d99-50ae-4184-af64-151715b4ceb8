import { get } from '@zeal/api/swapsIOApi'

import { notReachable } from '@zeal/toolkit'
import { parseFromSeconds } from '@zeal/toolkit/Date'
import { ImperativeError } from '@zeal/toolkit/Error'
import {
    array,
    groupByType,
    nullableOf,
    object,
    shape,
    string,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { CurrencyId } from '@zeal/domains/Currency'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { CurrentNetwork } from '@zeal/domains/Network'

import { swapsIOCurrencyId } from '../helpers/swapsIOCurrencyId'
import { parseSwapRequest, RawSwapRequest } from '../parsers/parseSwapRequest'

export type SwapsIOContinuationToken =
    | {
          type: 'cached_swaps'
          startTime: Date
          endTime: Date
          rawSwapRequests: RawSwapRequest[]
      }
    | {
          type: 'no_more_swaps'
          startTime: Date
          rawSwapRequests: RawSwapRequest[]
      }

const SWAP_REQUESTS_PER_PAGE = 50

export const fetchUserSwaps = async ({
    address,
    startTime,
    endTime,
    currentNetwork,
    swapsIOContinuationToken,
}: {
    startTime: Date
    endTime: Date
    address: Web3.address.Address
    currentNetwork: CurrentNetwork
    swapsIOContinuationToken: SwapsIOContinuationToken | null
}): Promise<{
    rawSwapRequests: RawSwapRequest[]
    currencies: CurrencyId[]
    swapsIOContinuationToken: SwapsIOContinuationToken
}> => {
    if (swapsIOContinuationToken) {
        switch (swapsIOContinuationToken?.type) {
            case undefined:
                break
            case 'cached_swaps':
                if (
                    swapsIOContinuationToken.startTime.getTime() >=
                        startTime.getTime() &&
                    swapsIOContinuationToken.endTime.getTime() <=
                        endTime.getTime()
                ) {
                    const filteredSwaps =
                        swapsIOContinuationToken.rawSwapRequests.filter(
                            (swap) =>
                                swap.created_at.getTime() <=
                                    startTime.getTime() &&
                                swap.created_at.getTime() >= endTime.getTime()
                        )
                    return {
                        rawSwapRequests: filteredSwaps,
                        currencies: getSwapCurrencies({
                            rawSwapRequests: filteredSwaps,
                        }),
                        swapsIOContinuationToken,
                    }
                }
                break
            case 'no_more_swaps':
                if (
                    swapsIOContinuationToken.startTime.getTime() >=
                    startTime.getTime()
                ) {
                    const filteredSwaps =
                        swapsIOContinuationToken.rawSwapRequests.filter(
                            (swap) =>
                                swap.created_at.getTime() <=
                                    startTime.getTime() &&
                                swap.created_at.getTime() >= endTime.getTime()
                        )
                    return {
                        rawSwapRequests: filteredSwaps,
                        currencies: getSwapCurrencies({
                            rawSwapRequests: filteredSwaps,
                        }),
                        swapsIOContinuationToken,
                    }
                }
                break
            /* istanbul ignore next */
            default:
                return notReachable(swapsIOContinuationToken)
        }
    }

    const response = await get(
        `/users/${address}/swaps`,
        {
            query: {
                limit: SWAP_REQUESTS_PER_PAGE,
                cursor: String(Math.round(startTime.getTime() / 1000)),
            },
        },
        undefined
    )

    const { swapsArray, cursor } = object(response)
        .andThen((obj) =>
            shape({
                swapsArray: array(obj.swaps),
                cursor: nullableOf(obj.cursor, (cursor) =>
                    string(cursor).andThen((cursorStr) =>
                        parseFromSeconds(Number.parseInt(cursorStr))
                    )
                ),
            })
        )
        .getSuccessResultOrThrow('Failed to parse swaps response')

    const [unparsedSwaps, swaps] = groupByType(swapsArray.map(parseSwapRequest))

    if (unparsedSwaps.length > 0) {
        captureError(
            new ImperativeError('Unparsed swaps raw requests found', {
                unparsedSwaps,
            })
        )
    }

    const filteredSwaps = swaps.filter((swap) => {
        const isInTimeRange = swap.created_at.getTime() >= endTime.getTime()

        switch (currentNetwork.type) {
            case 'all_networks':
                return isInTimeRange
            case 'specific_network':
                return (
                    isInTimeRange &&
                    (swap.from_chain_id === currentNetwork.network.hexChainId ||
                        swap.to_chain_id === currentNetwork.network.hexChainId)
                )
            /* istanbul ignore next */
            default:
                return notReachable(currentNetwork)
        }
    })

    const currencies = getSwapCurrencies({ rawSwapRequests: filteredSwaps })

    const nextSwapsIOContinuationToken: SwapsIOContinuationToken = cursor
        ? {
              type: 'cached_swaps',
              startTime,
              endTime: cursor,
              rawSwapRequests: swaps,
          }
        : {
              type: 'no_more_swaps',
              startTime,
              rawSwapRequests: swaps,
          }

    if (cursor && cursor.getTime() > endTime.getTime()) {
        const {
            rawSwapRequests: nextRawSwapRequests,
            currencies: nextCurrencies,
            swapsIOContinuationToken,
        } = await fetchUserSwaps({
            address,
            startTime: cursor,
            endTime,
            currentNetwork,
            swapsIOContinuationToken: nextSwapsIOContinuationToken,
        })

        return {
            rawSwapRequests: [...filteredSwaps, ...nextRawSwapRequests],
            currencies: Array.from(new Set([...currencies, ...nextCurrencies])),
            swapsIOContinuationToken,
        }
    }

    return {
        rawSwapRequests: filteredSwaps,
        currencies,
        swapsIOContinuationToken: nextSwapsIOContinuationToken,
    }
}

const getSwapCurrencies = ({
    rawSwapRequests,
}: {
    rawSwapRequests: RawSwapRequest[]
}): CurrencyId[] => {
    return Array.from(
        rawSwapRequests.reduce(
            (
                acc,
                {
                    from_token_address,
                    from_chain_id,
                    to_token_address,
                    to_chain_id,
                }
            ) => {
                return acc
                    .add(
                        swapsIOCurrencyId({
                            address: from_token_address,
                            networkId: from_chain_id,
                        })
                    )
                    .add(
                        swapsIOCurrencyId({
                            address: to_token_address,
                            networkId: to_chain_id,
                        })
                    )
            },
            new Set<CurrencyId>()
        )
    )
}
