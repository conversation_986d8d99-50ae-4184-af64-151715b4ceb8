import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { fetchFeeAdjustedBalance } from '@zeal/domains/Currency/api/fetchFeeAdjustedBalance'
import {
    OffRampAccount,
    UnblockUser,
} from '@zeal/domains/Currency/domains/BankTransfer'
import { OffRampFeeParams } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchTransactionFee'
import { fetchUnblockOffRampFee } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockOffRampFee'
import { BankTransferCurrencies } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStore, KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import {
    DEFAULT_NETWORK_HEX_IDS_TO_SPONSOR,
    findNetworkByHexChainId,
} from '@zeal/domains/Network/constants'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { createTransferEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/createERC20EthSendTransaction'
import {
    BankTransferUnblockUserCreated,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    currencies: BankTransferCurrencies
    initialCurrency: CryptoCurrency | null
    unblockLoginInfo: UnblockLoginInfo
    bankTransferInfo: BankTransferUnblockUserCreated
    userPreferredCryptoCurrency: CryptoCurrency
    offRampAccounts: OffRampAccount[]
    ownerPortfolio: ServerPortfolio2
    networkMap: NetworkMap
    owner: Account
    network: Network
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    unblockUser: UnblockUser
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof Layout>,
          { type: 'on_deposit_tab_click' | 'on_submit_form_click' }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'unblock_offramp_account_created'
                  | 'on_off_ramp_account_become_default'
                  | 'on_unblock_login_success'
                  | 'on_logged_in'
                  | 'bank_transfer_owner_successfully_changed'
                  | 'on_4337_gas_currency_selected'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_deposit_receiver_changed_successfully'
                  | 'on_withdrawal_bank_account_successfully_set_as_default_from_form'
                  | 'on_withdrawal_bank_account_created'
                  | 'on_withdrawal_bank_account_successfully_set_as_default'
                  | 'on_withdrawal_bank_account_successfully_deleted'
          }
      >

const fetchMaxBalance = async ({
    portfolio,
    gasCurrencyPresetMap,
    keyStore,
    cryptoCurrency,
    from,
    signal,
    networkMap,
    defaultCurrencyConfig,
    networkRPCMap,
}: {
    from: Account
    cryptoCurrency: CryptoCurrency
    portfolio: ServerPortfolio2
    keyStore: KeyStore
    gasCurrencyPresetMap: GasCurrencyPresetMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<CryptoMoney> => {
    const balance = getBalanceByCryptoCurrency2({
        currency: cryptoCurrency,
        serverPortfolio: portfolio,
    })

    const network = findNetworkByHexChainId(
        cryptoCurrency.networkHexChainId,
        networkMap
    )

    const trx = createTransferEthSendTransaction({
        amount: balance,
        from: from.address,
        network,
        to: from.address, // Sending to self should be same gas
    })

    return fetchFeeAdjustedBalance({
        fromAddress: from.address,
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        fromCurrency: cryptoCurrency,
        gasCurrencyPresetMap,
        keyStore,
        network,
        networksToSponsor: DEFAULT_NETWORK_HEX_IDS_TO_SPONSOR,
        serverPortfolio: portfolio,
        requests: [trx],
        actionSource: {
            type: 'internal',
            transactionEventSource: 'unblockOfframpGasEstimation',
        },
        signal,
    })
}

const calculateInitialForm = ({
    userPreferredCryptoCurrency,
    initialCurrency,
    offRampAccounts,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
}: {
    userPreferredCryptoCurrency: CryptoCurrency
    initialCurrency: CryptoCurrency | null
    offRampAccounts: OffRampAccount[]
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
}): OffRampFeeParams => {
    if (offRampAccounts.length === 0) {
        throw new ImperativeError('No offramp accounts for withdrawal')
    }

    const defaultAccount = offRampAccounts.find(
        (account) => account.mainBeneficiary
    )

    if (!defaultAccount) {
        throw new ImperativeError('No default offramp account for withdrawal')
    }

    return {
        type: 'cryptoToFiat',
        inputCurrency: initialCurrency || userPreferredCryptoCurrency,
        outputCurrency: defaultAccount.currency,
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        amount: null,
    }
}

export const Form = ({
    currencies,
    unblockLoginInfo,
    bankTransferInfo,
    offRampAccounts,
    initialCurrency,
    owner,
    network,
    ownerPortfolio,
    keyStoreMap,
    unblockUser,
    userPreferredCryptoCurrency,
    networkMap,
    currencyPinMap,
    currencyHiddenMap,
    installationId,
    sessionPassword,
    accountsMap,
    portfolioMap,
    networkRPCMap,
    feePresetMap,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({
        type: 'closed',
    })

    const [pollable, setPollable] = usePollableData(
        fetchUnblockOffRampFee,
        {
            type: 'loading',
            params: calculateInitialForm({
                offRampAccounts,
                userPreferredCryptoCurrency,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                initialCurrency,
            }),
        },
        {
            stopIf: () => false,
            pollIntervalMilliseconds: 30000,
        }
    )
    const keyStore = getKeyStore({
        keyStoreMap,
        address: owner.address,
    })

    const [maxBalanceLoadable, setMaxBalanceLoadable] = useLoadableData(
        fetchMaxBalance,
        {
            type: 'loading',
            params: {
                cryptoCurrency: pollable.params.inputCurrency,
                from: owner,
                defaultCurrencyConfig,
                gasCurrencyPresetMap,
                installationId,
                keyStore,
                networkMap,
                networkRPCMap,
                portfolio: ownerPortfolio,
            },
        }
    )

    useEffect(() => {
        setMaxBalanceLoadable((old) => ({
            ...old,
            type: 'loading',
            params: {
                ...old.params,
                cryptoCurrency: pollable.params.inputCurrency,
                portfolio: ownerPortfolio,
            },
        }))
    }, [pollable.params.inputCurrency, ownerPortfolio, setMaxBalanceLoadable])

    useEffect(() => {
        switch (maxBalanceLoadable.type) {
            case 'error':
                captureError(maxBalanceLoadable.error, {
                    extra: {
                        context:
                            'maxButton balance correction on unblock withdraw',
                    },
                })
                break

            case 'loaded':
            case 'loading':
                break

            /* istanbul ignore next */
            default:
                notReachable(maxBalanceLoadable)
        }
    }, [maxBalanceLoadable])

    return (
        <>
            <Layout
                installationId={installationId}
                maxBalanceLoadable={maxBalanceLoadable}
                networkMap={networkMap}
                unblockUser={unblockUser}
                pollable={pollable}
                ownerPortfolio={ownerPortfolio}
                offRampAccounts={offRampAccounts}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            onMsg(msg)
                            break
                        case 'on_amount_change':
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    amount: msg.amount,
                                },
                            })
                            break
                        case 'on_deposit_tab_click':
                            onMsg(msg)
                            break
                        case 'on_fiat_currency_selector_click':
                        case 'on_withdrawal_receiver_click':
                            setModal({ type: 'change_withdrawal_receiver' })
                            break
                        case 'on_crypto_currency_selector_click':
                        case 'on_network_selector_click':
                            setModal({ type: 'crypto_currency_selector' })
                            break
                        case 'on_provider_info_click':
                            setModal({ type: 'bank_transfer_provider_info' })
                            break
                        case 'on_submit_form_click':
                            onMsg(msg)
                            break
                        case 'on_settings_icon_click':
                            setModal({ type: 'settings' })
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                network={network}
                defaultCurrencyConfig={defaultCurrencyConfig}
                installationId={installationId}
                portfolioMap={portfolioMap}
                sessionPassword={sessionPassword}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                networkRPCMap={networkRPCMap}
                unblockUser={unblockUser}
                currencies={currencies}
                ownerPortfolio={ownerPortfolio}
                currencyPinMap={currencyPinMap}
                currencyHiddenMap={currencyHiddenMap}
                networkMap={networkMap}
                pollable={pollable}
                unblockLoginInfo={unblockLoginInfo}
                bankTransferInfo={bankTransferInfo}
                owner={owner}
                keyStoreMap={keyStoreMap}
                offRampAccounts={offRampAccounts}
                state={modal}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break

                        case 'on_withdrawal_bank_account_successfully_set_as_default_from_form':
                        case 'on_withdrawal_bank_account_created':
                            setModal({ type: 'closed' })
                            setPollable((pollable) => ({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    outputCurrency: msg.bankAccount.currency,
                                },
                            }))
                            onMsg(msg)
                            break
                        case 'on_withdrawal_bank_account_successfully_set_as_default':
                            setPollable((pollable) => ({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    outputCurrency: msg.bankAccount.currency,
                                },
                            }))
                            onMsg(msg)
                            break

                        case 'on_crypto_currency_selected':
                            setModal({ type: 'closed' })
                            setPollable((pollable) => ({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    inputCurrency: msg.currency,
                                },
                            }))
                            break
                        case 'on_logged_in':
                        case 'bank_transfer_owner_successfully_changed':
                        case 'on_4337_gas_currency_selected':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_deposit_receiver_changed_successfully':
                        case 'on_withdrawal_bank_account_successfully_deleted':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
