import { get } from '@zeal/api/socketApi'

import { delay } from '@zeal/toolkit/Function'
import {
    array,
    bigint,
    boolean,
    combine,
    number,
    object,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'

import {
    CurrencyId,
    ShortCryptoCurrency,
    ShortKnownCryptoCurrencies,
} from '@zeal/domains/Currency'
import {
    fetchStaticCurrencies,
    ToMatrix,
} from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import { fetchSocketFromTokens } from '@zeal/domains/Currency/api/fetchSocketFromTokens'
import { fetchSocketToTokens } from '@zeal/domains/Currency/api/fetchSocketToTokens'
import { NetworkHexId } from '@zeal/domains/Network'
import { PREDEFINED_NETWORKS } from '@zeal/domains/Network/constants'

import { GNOSIS_EURE_V2, GNOSIS_GBPE_V2 } from '../constants'

const BadCurrencyFromRPC = new Set<CurrencyId>([
    'Ethereum|******************************************',
    'Ethereum|******************************************', // bad symbol decoding (viem issue?)
    'Optimism|******************************************', // EOA
    'BSC|******************************************', // EOA
    // https://explorer.mantle.xyz/address/******************************************?tab=read_contract
    'Mantle|******************************************', // Contract but don't support correct methods
    // Fraud
    'BSC|******************************************', // Fraud BNB
    'BSC|******************************************', // Fraud BNB
    'Optimism|******************************************', // Fraud Tether USD
    'Avalanche|******************************************', // Fraud Tether USD
    'Arbitrum|******************************************', // Fraud Tether USD
    'Polygon|******************************************', // Fraud Tether USD
    'Polygon|******************************************', // Fraud Tether USD
    'BSC|******************************************', // Fraud Tether USD
    'BSC|******************************************', // Fraud Tether USD
    'BSC|******************************************', // Fraud Tether USD
    'BSC|******************************************', // Fraud Tether USD
    'BSC|******************************************', // Fraud Tether USD
    'BSC|******************************************', // Fraud Tether USD
    'BSC|******************************************', // Fraud Tether USD
    'BSC|******************************************', // Fraud Tether USD
    'BSC|******************************************', // Fraud Tether USD
    'BSC|******************************************', // Fraud Tether USD
    'BSC|******************************************', // Fraud Tether USD
    'BSC|******************************************', // Fraud Tether USD
    'Arbitrum|******************************************', // Fraud USD
    'Arbitrum|******************************************', // Fraud USD
    'Mantle|******************************************', // Fraud USDC
    'Aurora|******************************************', // Fraud USDE
    'Polygon|******************************************', // Fraud USDC
    'Mantle|******************************************', // Fraud USDC
    'Polygon|******************************************', // Fake Matic
    'Polygon|******************************************',
    'Arbitrum|******************************************', // Fake Usd Coin
    'Arbitrum|******************************************', // Fake Usd Coin
    'Polygon|******************************************',
    'Ethereum|******************************************',
    'Optimism|******************************************',
])

const RETRY_DELAY = 30000
const RETRY_COUNT = 5

const fetchSocketFromTokensWithDelayAndRetry = async ({
    fromChainId,
    isShortList,
    retriesRemaining,
    singleTxOnly,
    toChainId,
}: Parameters<typeof fetchSocketFromTokens>[0] & {
    retriesRemaining: number
}): ReturnType<typeof fetchSocketFromTokens> => {
    try {
        return await fetchSocketFromTokens({
            fromChainId,
            toChainId,
            singleTxOnly,
            isShortList,
        })
    } catch (error) {
        if (retriesRemaining > 0) {
            await delay(RETRY_DELAY)
            return await fetchSocketFromTokensWithDelayAndRetry({
                fromChainId,
                isShortList,
                retriesRemaining: retriesRemaining - 1,
                singleTxOnly,
                toChainId,
            })
        }

        throw error
    }
}

const fetchSocketToTokensWithDelayAndRetry = async ({
    fromChainId,
    isShortList,
    retriesRemaining,
    singleTxOnly,
    toChainId,
}: Parameters<typeof fetchSocketToTokens>[0] & {
    retriesRemaining: number
}): ReturnType<typeof fetchSocketToTokens> => {
    try {
        return await fetchSocketToTokens({
            fromChainId,
            toChainId,
            singleTxOnly,
            isShortList,
        })
    } catch (error) {
        if (retriesRemaining > 0) {
            await delay(RETRY_DELAY)
            return await fetchSocketToTokensWithDelayAndRetry({
                fromChainId,
                isShortList,
                retriesRemaining: retriesRemaining - 1,
                singleTxOnly,
                toChainId,
            })
        }

        throw error
    }
}

export const buildCurrencyMatrix = async ({
    updateRoutesWithNewCurrenciesFromSocket,
    signal,
}: {
    updateRoutesWithNewCurrenciesFromSocket: boolean
    signal?: AbortSignal
}): Promise<{
    knownCurrencies: ShortKnownCryptoCurrencies
    currencies: Record<NetworkHexId, ToMatrix>
}> => {
    const sorting = (a: CurrencyId, b: CurrencyId) => {
        return (
            (staticShortCurrencies[a]?.marketCapRank ||
                Number.MAX_SAFE_INTEGER) -
            (staticShortCurrencies[b]?.marketCapRank || Number.MAX_SAFE_INTEGER)
        )
    }
    const staticShortCurrencies = await fetchStaticCurrencies()
    const res = await get('/supported/chains', {}, signal)
    const data = parseSupportedChains(res).getSuccessResultOrThrow(
        '[Socket API] cannot parse /supported/chains'
    )

    const filtered = data.filter((fromNetworkDTO) => {
        return PREDEFINED_NETWORKS.find(
            (predefinedNetwork) =>
                fromNetworkDTO.chainId === predefinedNetwork.hexChainId
        )
    })

    const matrix = filtered.reduce(
        (memo, fromNetworkDTO) => {
            const multiply = filtered.map((toNetworkDTO) => {
                return { from: fromNetworkDTO, to: toNetworkDTO }
            })
            return [...memo, ...multiply]
        },
        [] as { from: SocketChainDetailDto; to: SocketChainDetailDto }[]
    )

    const socketCurrencies: ShortKnownCryptoCurrencies = {}
    const currencies: Record<NetworkHexId, ToMatrix> = {}

    // TODO @resetko-zeal remove this once sockets support GBPE_V2
    socketCurrencies[GNOSIS_GBPE_V2.id] = GNOSIS_GBPE_V2
    socketCurrencies[GNOSIS_EURE_V2.id] = GNOSIS_EURE_V2

    for await (const { from, to } of matrix) {
        if (!currencies[from.chainId]) {
            currencies[from.chainId] = {}
        }
        const fromSocketCurrencies: (ShortCryptoCurrency & {
            currencyId: CurrencyId
        })[] = await fetchSocketFromTokensWithDelayAndRetry({
            fromChainId: from.chainId,
            toChainId: to.chainId,
            singleTxOnly: true,
            isShortList: false,
            retriesRemaining: RETRY_COUNT,
        })
        const filterdFromSocketCurrencies: Set<CurrencyId> = new Set()
        fromSocketCurrencies.forEach((currency) => {
            if (!BadCurrencyFromRPC.has(currency.currencyId)) {
                if (!staticShortCurrencies[currency.currencyId]) {
                    socketCurrencies[currency.currencyId] = {
                        symbol: currency.symbol,
                        fraction: currency.fraction,
                        marketCapRank: currency.marketCapRank,
                        name: currency.name,
                    }
                }
                if (
                    staticShortCurrencies[currency.currencyId] ||
                    updateRoutesWithNewCurrenciesFromSocket
                ) {
                    filterdFromSocketCurrencies.add(currency.currencyId)
                }
            }
        })

        if (from.chainId !== to.chainId) {
            const toSocketCurrencies: (ShortCryptoCurrency & {
                currencyId: CurrencyId
            })[] = await fetchSocketToTokensWithDelayAndRetry({
                fromChainId: from.chainId,
                toChainId: to.chainId,
                singleTxOnly: true,
                isShortList: false,
                retriesRemaining: RETRY_COUNT,
            })
            const filterdToSocketCurrencies: Set<CurrencyId> = new Set()
            toSocketCurrencies.forEach((currency) => {
                if (!BadCurrencyFromRPC.has(currency.currencyId)) {
                    if (!staticShortCurrencies[currency.currencyId]) {
                        socketCurrencies[currency.currencyId] = {
                            symbol: currency.symbol,
                            fraction: currency.fraction,
                            marketCapRank: currency.marketCapRank,
                            name: currency.name,
                        }
                    }
                    if (
                        staticShortCurrencies[currency.currencyId] ||
                        updateRoutesWithNewCurrenciesFromSocket
                    ) {
                        filterdToSocketCurrencies.add(currency.currencyId)
                    }
                }
            })
            const fromSorted = [...filterdFromSocketCurrencies].toSorted(
                sorting
            )
            const toSorted = [...filterdToSocketCurrencies].toSorted(sorting)
            currencies[from.chainId][to.chainId] = {
                from: fromSorted,
                to: toSorted,
                canRefuel:
                    from.refuel.sendingEnabled && to.refuel.receivingEnabled,
            }
        } else {
            const fromSorted = [...filterdFromSocketCurrencies].toSorted(
                sorting
            )
            // if we have a same chainId from and to we don't need fetch currency list again
            currencies[from.chainId][to.chainId] = {
                from: fromSorted,
                to: fromSorted,
                canRefuel:
                    from.refuel.sendingEnabled && to.refuel.receivingEnabled,
            }
        }
    }

    return {
        knownCurrencies: {
            ...staticShortCurrencies,
            ...socketCurrencies,
        },
        currencies,
    }
}

type SocketChainDetailDto = {
    chainId: NetworkHexId
    name: string
    isL1: boolean
    sendingEnabled: boolean
    receivingEnabled: boolean
    icon: string
    refuel: RefuelDto
    currency: SocketCurrencyDto
    rpcs: string[]
    explorers: string[]
}

type RefuelDto = {
    sendingEnabled: boolean
    receivingEnabled: boolean
}

type SocketCurrencyDto = {
    icon: string
    name: string
    symbol: string
    decimals: number
    minNativeCurrencyForGas: string
}

const parseSupportedChains = (
    input: unknown
): Result<unknown, SocketChainDetailDto[]> =>
    array(input).andThen((arr) =>
        combine(
            arr.map((item) =>
                object(item).andThen((dto) =>
                    shape({
                        chainId: bigint(dto.chainId).andThen((int) =>
                            success(`0x${int.toString(16)}` as NetworkHexId)
                        ),
                        name: string(dto.name),
                        isL1: boolean(dto.isL1),
                        icon: string(dto.icon),
                        sendingEnabled: boolean(dto.sendingEnabled),
                        receivingEnabled: boolean(dto.receivingEnabled),
                        refuel: object(dto.refuel).andThen((dto) =>
                            shape({
                                sendingEnabled: boolean(dto.sendingEnabled),
                                receivingEnabled: boolean(dto.receivingEnabled),
                            })
                        ),
                        currency: object(dto.currency).andThen((dto) =>
                            shape({
                                icon: string(dto.icon),
                                name: string(dto.name),
                                symbol: string(dto.symbol),
                                decimals: number(dto.decimals),
                                minNativeCurrencyForGas: string(
                                    dto.minNativeCurrencyForGas
                                ),
                            })
                        ),
                        rpcs: array(dto.rpcs).andThen((rpcs) =>
                            combine(rpcs.map(string))
                        ),
                        explorers: array(dto.explorers).andThen((explorers) =>
                            combine(explorers.map(string))
                        ),
                    })
                )
            )
        )
    )
