import { keys } from '@zeal/toolkit/Object'

import { CurrencyId, KnownCryptoCurrencies } from '@zeal/domains/Currency'
import { getShortKnownCryptoCurrenciesFromKnownCurrencies } from '@zeal/domains/Currency/helpers/getShortKnownCryptoCurrenciesfromKnownCryptoCurrencies'
import { NetworkRPCMap } from '@zeal/domains/Network'

import { fetchCryptoCurrencyFromRPC } from './fetchCryptoCurrencyFromRPC'
import {
    fetchStaticCurrencies,
    writeCurrenciesCache,
} from './fetchCurrenciesMatrix'

type Params = {
    currencies: CurrencyId[]
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}
export const fetchCryptoCurrency2 = async ({
    currencies,
    networkRPCMap,
    signal,
}: Params): Promise<KnownCryptoCurrencies> => {
    const currenciesForFetch: CurrencyId[] = []
    const knownCurrencies: KnownCryptoCurrencies = {}
    const staticCurrencies = await fetchStaticCurrencies()

    currencies.forEach((element: CurrencyId) => {
        if (staticCurrencies[element]) {
            knownCurrencies[element] = staticCurrencies[element]
        } else {
            currenciesForFetch.push(element)
        }
    })

    const currenciesFromRPC: KnownCryptoCurrencies =
        await fetchCryptoCurrencyFromRPC({
            currencies: currenciesForFetch,
            networkRPCMap,
            signal,
        })
    if (keys(currenciesFromRPC).length > 0) {
        await writeCurrenciesCache(
            getShortKnownCryptoCurrenciesFromKnownCurrencies(currenciesFromRPC)
        )
    }

    return { ...knownCurrencies, ...currenciesFromRPC }
}
