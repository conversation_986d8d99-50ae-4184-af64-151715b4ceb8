import memoize from 'lodash/memoize'

import { notReachable } from '@zeal/toolkit'

import { getCryptoCurrencyIcon } from './getCryptoCurrencyIcon'

import {
    CryptoCurrency,
    KnownCryptoCurrencies,
    KnownCurrencies,
    ShortCryptoCurrency,
    ShortKnownCryptoCurrencies,
    unsafeGetCurrencyInfo,
} from '..'

export const getShortKnownCryptoCurrenciesFromKnownCryptoCurrencies = (
    knownCurrencies: KnownCryptoCurrencies
): ShortKnownCryptoCurrencies => {
    return Object.entries(knownCurrencies).reduce((acc, [key, value]) => {
        acc[key] = {
            symbol: value.symbol,
            fraction: value.fraction,
            marketCapRank: value.marketCapRank,
            name: value.name,
        }
        return acc
    }, {} as ShortKnownCryptoCurrencies)
}

export const getShortKnownCryptoCurrenciesFromKnownCurrencies = (
    knownCurrencies: KnownCurrencies
): ShortKnownCryptoCurrencies => {
    return Object.entries(knownCurrencies)
        .filter(([_, value]) => {
            switch (value.type) {
                case 'FiatCurrency':
                    return false
                case 'CryptoCurrency':
                    return true
                default:
                    return notReachable(value)
            }
        })
        .reduce((acc, [key, value]) => {
            const currency = value as CryptoCurrency
            acc[key] = {
                symbol: currency.symbol,
                fraction: currency.fraction,
                marketCapRank: currency.marketCapRank,
                name: currency.name,
            }
            return acc
        }, {} as ShortKnownCryptoCurrencies)
}

const shortCryptoCurrencyToCryptoCurrency = memoize(
    (key: string, short: ShortCryptoCurrency): CryptoCurrency => {
        const { network, address } = unsafeGetCurrencyInfo(key)

        return {
            symbol: short.symbol,
            code: short.symbol,
            fraction: short.fraction,
            rateFraction: short.fraction,
            marketCapRank: short.marketCapRank,
            name: short.name,
            address,
            networkHexChainId: network.hexChainId,
            id: key,
            type: 'CryptoCurrency',
            icon: getCryptoCurrencyIcon(network.name, address),
        }
    },
    (key) => {
        return key
    }
)

export const getKnownCryptoCurrenciesFromShortKnownCryptoCurrencies = (
    shortCurrencies: ShortKnownCryptoCurrencies
): KnownCryptoCurrencies => {
    return Object.entries(shortCurrencies).reduce((acc, [key, value]) => {
        acc[key] = shortCryptoCurrencyToCryptoCurrency(key, value)
        return acc
    }, {} as KnownCryptoCurrencies)
}
