import { notReachable } from '@zeal/toolkit'

import {
    EOATransactionFailedError,
    UserOperationFailedError,
} from '@zeal/domains/Error'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStore } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { Network } from '@zeal/domains/Network'
import { SubmitedTransactionFailed } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction'
import {
    SubmittedUserOperationFailed,
    SubmittedUserOperationRejected,
} from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import { fetchTransactionFailureReasonsByHash } from '@zeal/domains/Transactions/api/fetchTransactionFailureReasons'

export const captureTransactionFailureError = async ({
    transaction,
    network,
    keyStore,
    actionSource,
}: {
    transaction: SubmitedTransactionFailed
    actionSource: ActionSource2
    keyStore: KeyStore // TODO :: @Nicvaniek this should be EOA
    network: Network
}) => {
    const reasons = await (() => {
        switch (network.type) {
            case 'predefined':
            case 'testnet':
                return fetchTransactionFailureReasonsByHash({
                    hash: transaction.hash,
                    network,
                })
            case 'custom':
                return []

            default:
                return notReachable(network)
        }
    })()

    captureError(
        new EOATransactionFailedError({
            actionSource,
            keyStoreType: keyStore.type,
            trxHash: transaction.hash,
            networkHexId: network.hexChainId,
            reasons,
        })
    )
}

export const captureUserOperationFailureError = async ({
    userOperation,
    network,
    actionSource,
}: {
    userOperation: SubmittedUserOperationFailed | SubmittedUserOperationRejected
    actionSource: ActionSource2
    network: Network
}) => {
    const bundleTrxHash = (() => {
        switch (userOperation.state) {
            case 'rejected':
                return null
            case 'failed':
                return userOperation.bundleTransactionHash
            default:
                return notReachable(userOperation)
        }
    })()

    const reasons = await (() => {
        switch (network.type) {
            case 'predefined':
            case 'testnet':
                return bundleTrxHash
                    ? fetchTransactionFailureReasonsByHash({
                          hash: bundleTrxHash,
                          network,
                      })
                    : []
            case 'custom':
                return []

            default:
                return notReachable(network)
        }
    })()

    captureError(
        new UserOperationFailedError({
            actionSource,
            keyStoreType: 'safe_4337',
            bundleTrxHash,
            userOperationHash: userOperation.userOperationHash,
            networkHexId: network.hexChainId,
            reasons,
        })
    )
}
