import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkHexId } from '@zeal/domains/Network'

export type BundlerErrorType =
    | 'bundler_error_aa10_sender_already_constructed'
    | 'bundler_error_aa13_init_code_failed_or_out_of_gas'
    | 'bundler_error_aa21_didnt_pay_prefund'
    | 'bundler_error_aa22_expired_or_not_due'
    | 'bundler_error_aa23_reverted_or_oog'
    | 'bundler_error_aa24_signature_error'
    | 'bundler_error_aa25_invalid_account_nonce'
    | 'bundler_error_aa31_paymaster_deposit_too_low'
    | 'bundler_error_aa33_reverted_or_out_of_gas'
    | 'bundler_error_aa34_signature_error'
    | 'bundler_error_aa40_over_verification_gas_limit'
    | 'bundler_error_aa41_too_little_verification_gas'
    | 'bundler_error_aa51_prefund_below_gas_cost'
    | 'bundler_error_aa93_invalid_paymaster_and_data'
    | 'bundler_error_aa95_out_of_gas'
    | 'bundler_error_cannot_execute_request'
    | 'bundler_error_max_priority_fee_per_gas_is_lower_than_expected'
    | 'bundler_error_unknown'
    | 'bundler_error_user_operation_reverted_during_execution_phase'
    | 'bundler_error_user_operation_reverted_during_execution_phase_address_not_whitelisted'
    | 'bundler_error_user_operation_reverted_during_execution_phase_already_minted'
    | 'bundler_error_user_operation_reverted_during_execution_phase_return_amount_not_enough'
    | 'bundler_error_user_operation_reverted_during_execution_phase_transfer_amount_exceeds_balance'

export class BundlerResponseError extends Error {
    name = 'BundlerResponseError' as const
    isBunderResponseError = true as const
    networkHexId: NetworkHexId
    type: BundlerErrorType
    response: unknown
    request: unknown
    actionSource: ActionSource2
    revertReasons?: string[]
    provider: 'biconomy' | 'pimlico'

    constructor({
        networkHexId,
        response,
        request,
        type,
        actionSource,
        revertReasons,
        provider,
    }: {
        networkHexId: NetworkHexId
        provider: 'biconomy' | 'pimlico'
        response: unknown
        request: unknown
        type: BundlerErrorType
        actionSource: ActionSource2
        revertReasons?: string[]
    }) {
        super()
        this.networkHexId = networkHexId
        this.response = response
        this.request = request
        this.type = type
        this.actionSource = actionSource
        this.revertReasons = revertReasons
        this.provider = provider
    }
}
