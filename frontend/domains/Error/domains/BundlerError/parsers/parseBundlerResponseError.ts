import {
    failure,
    match,
    matchRegExp,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'

import {
    BundlerErrorType,
    BundlerResponseError,
} from '@zeal/domains/Error/domains/BundlerError'
import { getReplacedStack } from '@zeal/domains/Error/helpers/joinStacks'
import { parseHttpError } from '@zeal/domains/Error/parsers/parseHttpError'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkHexId } from '@zeal/domains/Network'
import { parse as parseNetworkHexId } from '@zeal/domains/Network/helpers/parse'
import { PimlicoBundlerRequest } from '@zeal/domains/UserOperation/api/fetchBundlerResponse'

const _yes_I_added_parser: Record<BundlerResponseError['type'], true> = {
    bundler_error_aa10_sender_already_constructed: true,
    bundler_error_aa13_init_code_failed_or_out_of_gas: true,
    bundler_error_aa21_didnt_pay_prefund: true,
    bundler_error_aa22_expired_or_not_due: true,
    bundler_error_aa23_reverted_or_oog: true,
    bundler_error_aa24_signature_error: true,
    bundler_error_aa25_invalid_account_nonce: true,
    bundler_error_aa31_paymaster_deposit_too_low: true,
    bundler_error_aa33_reverted_or_out_of_gas: true,
    bundler_error_aa34_signature_error: true,
    bundler_error_aa40_over_verification_gas_limit: true,
    bundler_error_aa41_too_little_verification_gas: true,
    bundler_error_aa51_prefund_below_gas_cost: true,
    bundler_error_aa93_invalid_paymaster_and_data: true,
    bundler_error_aa95_out_of_gas: true,
    bundler_error_cannot_execute_request: true,
    bundler_error_max_priority_fee_per_gas_is_lower_than_expected: true,
    bundler_error_unknown: true,
    bundler_error_user_operation_reverted_during_execution_phase: true,

    // not parsed from http error
    bundler_error_user_operation_reverted_during_execution_phase_address_not_whitelisted:
        true,
    bundler_error_user_operation_reverted_during_execution_phase_already_minted:
        true,
    bundler_error_user_operation_reverted_during_execution_phase_return_amount_not_enough:
        true,
    bundler_error_user_operation_reverted_during_execution_phase_transfer_amount_exceeds_balance:
        true,
}

const BUNDLER_URL_REGEX = /\/proxy\/bundler\/(0x.+)\//i

export const parseBundlerResponseError = (
    input: unknown
): Result<unknown, BundlerResponseError> =>
    input instanceof BundlerResponseError && input.isBunderResponseError
        ? success(input)
        : failure({
              type: 'not_bundler_response_error',
              input,
          })

const parseBundlerErrorType = (
    msg: string
): Result<unknown, BundlerErrorType> =>
    oneOf(msg, [
        oneOf(msg, [
            matchRegExp(msg, /AA21/i).map(
                () => 'bundler_error_aa21_didnt_pay_prefund' as const
            ),
            matchRegExp(msg, /AA33/i).map(
                () => 'bundler_error_aa33_reverted_or_out_of_gas' as const
            ),
            matchRegExp(msg, /AA24/i).map(
                () => 'bundler_error_aa24_signature_error' as const
            ),
            matchRegExp(msg, /AA10/i).map(
                () => 'bundler_error_aa10_sender_already_constructed' as const
            ),
            matchRegExp(msg, /AA13/i).map(
                () =>
                    'bundler_error_aa13_init_code_failed_or_out_of_gas' as const
            ),
            matchRegExp(msg, /AA93/i).map(
                () => 'bundler_error_aa93_invalid_paymaster_and_data' as const
            ),
            matchRegExp(msg, /AA95/i).map(
                () => 'bundler_error_aa95_out_of_gas' as const
            ),
            matchRegExp(msg, /AA31/i).map(
                () => 'bundler_error_aa31_paymaster_deposit_too_low' as const
            ),
            matchRegExp(msg, /AA41/i).map(
                () => 'bundler_error_aa41_too_little_verification_gas' as const
            ),
            matchRegExp(msg, /AA40/i).map(
                () => 'bundler_error_aa40_over_verification_gas_limit' as const
            ),
        ]),
        oneOf(msg, [
            matchRegExp(msg, /AA22/i).map(
                () => 'bundler_error_aa22_expired_or_not_due' as const
            ),
            matchRegExp(msg, /AA51/i).map(
                () => 'bundler_error_aa51_prefund_below_gas_cost' as const
            ),
            matchRegExp(msg, /AA34/i).map(
                () => 'bundler_error_aa34_signature_error' as const
            ),
            matchRegExp(msg, /AA25/i).map(
                () => 'bundler_error_aa25_invalid_account_nonce' as const
            ),
            matchRegExp(msg, /AA23/i).map(
                () => 'bundler_error_aa23_reverted_or_oog' as const
            ),
            matchRegExp(msg, /we can't execute this request/i).map(
                () => 'bundler_error_cannot_execute_request' as const
            ),
            matchRegExp(
                msg,
                /UserOperation reverted during execution phase/i
            ).map(
                () =>
                    'bundler_error_user_operation_reverted_during_execution_phase' as const
            ),
            matchRegExp(
                msg,
                /is lower than expected maxPriorityFeePerGas/i
            ).map(
                () =>
                    'bundler_error_max_priority_fee_per_gas_is_lower_than_expected' as const
            ),
        ]),
    ])

export const parseBiconomyBundlerResponseErrorFromHttpError = ({
    input,
    actionSource,
}: {
    actionSource: ActionSource2
    input: unknown
}): Result<unknown, BundlerResponseError> =>
    parseHttpError(input).andThen((httpError) =>
        shape({
            url: matchRegExp(httpError.urlPathname, BUNDLER_URL_REGEX),
            status: match(httpError.status, 400),
            networkHexId: string(
                httpError.urlPathname.match(BUNDLER_URL_REGEX)?.[1]
            ).andThen(parseNetworkHexId),
            error: oneOf(httpError.responseBody, [
                object(httpError.responseBody)
                    .andThen((errObj) => object(errObj.error))
                    .andThen((error) => string(error.message))
                    .andThen(parseBundlerErrorType),
                success('bundler_error_unknown' as const),
            ]),
        }).map(({ error, networkHexId }) => {
            const newError = new BundlerResponseError({
                response: httpError.responseBody,
                request: httpError.requestBody,
                networkHexId,
                type: error,
                actionSource,
                provider: 'biconomy',
            })

            newError.stack = getReplacedStack(newError, httpError)

            return newError
        })
    )

export const parsePimlicoBundlerErrorResponse = ({
    request,
    response,
    networkHexId,
    actionSource,
}: {
    response: unknown
    actionSource: ActionSource2
    networkHexId: NetworkHexId
    request: PimlicoBundlerRequest
}): Result<unknown, BundlerResponseError> =>
    oneOf(response, [
        object(response)
            .andThen((obj) => string(obj.message))
            .andThen(parseBundlerErrorType),
        success('bundler_error_unknown' as const),
    ]).map(
        (type) =>
            new BundlerResponseError({
                type,
                actionSource,
                provider: 'pimlico',
                response,
                request,
                networkHexId,
            })
    )
