import { useEffect } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { uuid } from '@zeal/toolkit/Crypto'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { useLazyLoadableData } from '@zeal/toolkit/LoadableData/LazyLoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { SAFE_4337_MODULE_ENTRYPOINT_ADDRESS } from '@zeal/domains/Address/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { AppAssociationCheckFailed } from '@zeal/domains/Error/domains/Passkey/components/AppAssociationCheckFailed'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { Passkey } from '@zeal/domains/KeyStore/domains/Passkey'
import { getKeystoreFromPrivateKey } from '@zeal/domains/KeyStore/helpers/getKeystoreFromPrivateKey'
import { getPredictedSafeAddress } from '@zeal/domains/KeyStore/helpers/getPredictedSafeAddress'
import { getSafeDeploymentInitCode } from '@zeal/domains/KeyStore/helpers/getSafeDeploymentInitCode'
import { Network, NetworkRPCMap } from '@zeal/domains/Network'
import { SubmittedUserOperationPending } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'
import {
    BoostedUserOperationWithoutSignature,
    InitialUserOperation,
    MetaTransactionData,
    OperationType,
} from '@zeal/domains/UserOperation'
import { fetchPimlicoGasEstimates } from '@zeal/domains/UserOperation/api/fetchGasEstimates'
import { submitUserOperationToBundler } from '@zeal/domains/UserOperation/api/submitUserOperationToBundler'
import {
    DUMMY_PASSKEY_SIGNATURE,
    passkeySignerGasBufferConfig,
    PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA,
    SAFE_ABI,
} from '@zeal/domains/UserOperation/constants'
import { calculateUserOperationHash } from '@zeal/domains/UserOperation/helpers/calculateUserOperationHash'
import { metaTransactionDatasToUserOperationCallData } from '@zeal/domains/UserOperation/helpers/metaTransactionDatasToUserOperationCallData'
import { signUserOperationHashWithPassKey } from '@zeal/domains/UserOperation/helpers/signUserOperationHashWithPassKey'

import { Layout } from './Layout'

type Props = {
    installationId: string
    networkRPCMap: NetworkRPCMap
    passkey: Passkey
    label: string
    network: Network
    sessionPassword: string
    onMsg: (msg: Msg) => void
}

type Msg = {
    type: 'on_deployment_submitted'
    keyStore: Safe4337
    account: Account
    submittedUserOperation: SubmittedUserOperationPending
}

const fetch = async ({
    network,
    label,
    passkey,
    sessionPassword,
    networkRPCMap,
    signal,
}: {
    label: string
    network: Network
    passkey: Passkey
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<{
    account: Account
    keyStore: Safe4337
    submittedUserOperation: SubmittedUserOperationPending
}> => {
    const pk = Web3.privateKey.generate()

    const localSignerKeyStore = await getKeystoreFromPrivateKey(
        pk,
        sessionPassword
    )

    const safeDeplymentConfig = {
        passkeyOwner: passkey,
        saltNonce: passkey.signerAddress,
        threshold: 1,
    }

    const safeAddress = getPredictedSafeAddress(safeDeplymentConfig)
    const entrypoint = SAFE_4337_MODULE_ENTRYPOINT_ADDRESS

    const addOwnerMetaTransactionData: MetaTransactionData = {
        to: safeAddress,
        value: '0',
        data: Web3.abi.encodeFunctionData({
            abi: SAFE_ABI,
            functionName: 'addOwnerWithThreshold',
            args: [localSignerKeyStore.address, 1n],
        }),
        operation: OperationType.Call,
    }

    const initialUserOperation: InitialUserOperation = {
        type: 'initial_user_operation',
        sender: safeAddress,
        callData: metaTransactionDatasToUserOperationCallData({
            metaTransactionDatas: [addOwnerMetaTransactionData],
        }),
        initCode: getSafeDeploymentInitCode(safeDeplymentConfig),
        nonce: 0n,
        entrypoint,
    }

    const verificationGasLimitBuffer =
        passkeySignerGasBufferConfig[
            safeDeplymentConfig.passkeyOwner.signerVersion
        ].verificationGasLimitBuffer

    const gasEstimates = await fetchPimlicoGasEstimates({
        initialUserOperation: {
            callData: initialUserOperation.callData,
            nonce: initialUserOperation.nonce,
            initCode: (initialUserOperation.initCode || '0x') as Hexadecimal,
            sender: initialUserOperation.sender,
            signature: DUMMY_PASSKEY_SIGNATURE,
            paymasterAndData: PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA,
        },
        entrypoint: initialUserOperation.entrypoint,
        network,
        verificationGasLimitBuffer,
        callGasLimitBuffer: 0n,
        actionSource: {
            type: 'internal',
            transactionEventSource: 'smartWalletCreationReferenceDeployment',
        },
        signal,
    })

    const userOperationWithoutSignature: BoostedUserOperationWithoutSignature =
        {
            type: 'boosted_user_operation_without_signature',
            sender: initialUserOperation.sender,
            entrypointNonce: initialUserOperation.nonce,
            entrypoint: initialUserOperation.entrypoint,
            callData: initialUserOperation.callData as Hexadecimal,
            initCode: initialUserOperation.initCode as Hexadecimal,
            callGasLimit: gasEstimates.callGasLimit,
            verificationGasLimit: gasEstimates.verificationGasLimit,
        }

    const userOperationHash = calculateUserOperationHash({
        network,
        userOperation: userOperationWithoutSignature,
        now: Date.now(),
    })

    const signature = await signUserOperationHashWithPassKey({
        userOperationHash,
        passkey,
        sessionPassword,
    })

    const submittedUserOperation = await submitUserOperationToBundler({
        network,
        signal,
        networkRPCMap,
        userOperationWithSignature: {
            ...userOperationWithoutSignature,
            signature,
            type: 'boosted_user_operation_with_signature',
        },
        actionSource: {
            type: 'internal',
            transactionEventSource: 'smartWalletCreationReferenceDeployment',
        },
    })

    return {
        account: {
            address: safeAddress,
            label,
            avatarSrc: null,
        },
        keyStore: {
            type: 'safe_4337',
            id: uuid(),
            address: safeAddress,
            localSignerKeyStore,
            safeDeplymentConfig,
        },
        submittedUserOperation,
    }
}

export const SignAndSubmitDeployment = ({
    onMsg,
    installationId,
    sessionPassword,
    networkRPCMap,
    network,
    label,
    passkey,
}: Props) => {
    const [loadable, setLoadable] = useLazyLoadableData(fetch, {
        type: 'not_asked',
    })

    const onMsgLive = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'not_asked':
            case 'loading':
                break
            case 'loaded':
                postUserEvent({
                    type: 'DeploySmartWalletEvent',
                    installationId,
                    provider: 'biconomy',
                })
                onMsgLive.current({
                    type: 'on_deployment_submitted',
                    submittedUserOperation:
                        loadable.data.submittedUserOperation,
                    keyStore: loadable.data.keyStore,
                    account: loadable.data.account,
                })
                break
            case 'error':
                const error = parseAppError(loadable.error)
                switch (error.type) {
                    case 'passkey_operation_cancelled':
                        setLoadable({ type: 'not_asked' })
                        break
                    /* istanbul ignore next */
                    default:
                        break
                }
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, setLoadable, onMsgLive, installationId])

    switch (loadable.type) {
        case 'not_asked':
            return (
                <Layout
                    isLoading={false}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_verify_passkey_clicked':
                                postUserEvent({
                                    type: 'VerifyPasskeyClickedEvent',
                                    installationId,
                                })
                                setLoadable({
                                    type: 'loading',
                                    params: {
                                        label,
                                        network,
                                        passkey,
                                        sessionPassword,
                                        networkRPCMap,
                                    },
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg.type)
                        }
                    }}
                />
            )
        case 'loading':
        case 'loaded':
            return (
                <Layout
                    isLoading
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_verify_passkey_clicked':
                                noop()
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg.type)
                        }
                    }}
                />
            )
        case 'error':
            const error = parseAppError(loadable.error)

            switch (error.type) {
                case 'passkey_operation_cancelled':
                    return null
                case 'passkey_app_not_associated_with_domain':
                case 'passkey_android_cannot_validate_incoming_request':
                    return (
                        <>
                            <Layout isLoading onMsg={noop} />
                            <AppAssociationCheckFailed
                                error={error}
                                variant="passkey_signing"
                                network={network}
                                installationId={installationId}
                                location="wallet_creation"
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            setLoadable({ type: 'not_asked' })
                                            break
                                        case 'on_try_again_clicked':
                                            setLoadable({
                                                type: 'loading',
                                                params: loadable.params,
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        </>
                    )
                /* istanbul ignore next */
                default:
                    return (
                        <>
                            <Layout isLoading onMsg={noop} />
                            <AppErrorPopup
                                installationId={installationId}
                                error={error}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            setLoadable({ type: 'not_asked' })
                                            break

                                        case 'try_again_clicked':
                                            setLoadable({
                                                type: 'loading',
                                                params: loadable.params,
                                            })
                                            break

                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        </>
                    )
            }
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
