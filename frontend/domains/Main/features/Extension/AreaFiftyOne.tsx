import { useEffect } from 'react'

import { getApps } from '@zeal/react-native-packages'

import { LanguageSettings } from '@zeal/uikit/Language'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import * as storage from '@zeal/toolkit/Storage'
import * as Web3 from '@zeal/toolkit/Web3'

import { CardConfig } from '@zeal/domains/Card'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { CurrencyHiddenMap, CurrencyPinMap } from '@zeal/domains/Currency'
import { ConnectionMap } from '@zeal/domains/DApp/domains/ConnectionState'
import { fetchWalletConnectInstance } from '@zeal/domains/DApp/domains/WalletConnect/api/fetchWalletConnectInstance'
import { WalletConnectListener } from '@zeal/domains/DApp/domains/WalletConnect/features/WalletConnectListener'
import {
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
} from '@zeal/domains/Earn'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { AppRating } from '@zeal/domains/Feedback'
import { InitialActiveTab, Mode } from '@zeal/domains/Main'
import { AppDeepLinkChecker } from '@zeal/domains/Main/features/AppDeepLinkChecker'
import { AppUpdateChecker } from '@zeal/domains/Main/features/AppUpdateChecker'
import { NetworkMap } from '@zeal/domains/Network'
import { subscribeForNotifications } from '@zeal/domains/Notification/api/subscribeForNotifications'
import { subscribeForTokenUpdates } from '@zeal/domains/Notification/api/subscribeForTokenUpdates'
import {
    BankTransferInfo,
    BrowserTabState,
    CelebrationConfig,
    CustomCurrencyMap,
    NotificationsConfig,
    Storage,
} from '@zeal/domains/Storage'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { PortfolioLoader } from './PortfolioLoader'

type Props = {
    mode: Mode
    initialActiveTab: InitialActiveTab
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    storage: Storage
    sessionPassword: string
    referralConfig: ReferralConfig
    selectedAddress: Web3.address.Address
    customCurrencies: CustomCurrencyMap
    browserTabState: BrowserTabState

    installationId: string
    installationCampaign: string | null

    connections: ConnectionMap
    isEthereumNetworkFeeWarningSeen: boolean
    networkMap: NetworkMap

    currencyHiddenMap: CurrencyHiddenMap
    earnTakerMetrics: EarnTakerMetrics
    currencyPinMap: CurrencyPinMap
    cardConfig: CardConfig
    appBrowserProviderScript: string | null
    celebrationConfig: CelebrationConfig
    appRating: AppRating

    bankTransferInfo: BankTransferInfo
    notificationsConfig: NotificationsConfig
    languageSettings: LanguageSettings
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof PortfolioLoader> | MsgOf<typeof WalletConnectListener>

const fetchAppsAndReportOnce = async ({
    installationId,
}: {
    installationId: string
}) => {
    const STORAGE_KEY = 'apps_fetched_1' // TODO @resetko-zeal we can keep one key, but put as value list of apps we check, that way we don't need to update it
    const value = await storage.local.get(STORAGE_KEY)

    if (value === STORAGE_KEY) {
        return
    }

    try {
        const apps = await getApps()

        postUserEvent({
            type: 'AppsFetchedEvent',
            apps,
            installationId,
            location: 'home',
        })
    } catch (error) {
        captureError(error)
    } finally {
        await storage.local.set(STORAGE_KEY, STORAGE_KEY)
    }
}

// Naming is so hard so we can't really find a name for this global experimental area, so it's gonna be Area51
export const AreaFiftyOne = ({
    connections,
    initialActiveTab,
    currencyHiddenMap,
    currencyPinMap,
    referralConfig,
    customCurrencies,
    installationId,
    installationCampaign,
    mode,
    browserTabState,
    networkMap,
    earnHistoricalTakerUserCurrencyRateMap,
    earnTakerMetrics,
    selectedAddress,
    sessionPassword,
    storage,
    celebrationConfig,
    appRating,
    cardConfig,
    isEthereumNetworkFeeWarningSeen,
    appBrowserProviderScript,
    bankTransferInfo,
    notificationsConfig,
    languageSettings,
    experimentalMode,

    onMsg,
}: Props) => {
    const [walletConnectInstanceLoadable] = useLoadableData(
        fetchWalletConnectInstance,
        {
            type: 'loading',
            params: undefined,
        }
    )

    const [notificationsLoadable, setNotificationsLoadable] = useLoadableData(
        subscribeForNotifications,
        {
            type: 'loading',
            params: {
                bankTransferInfo,
                cardConfig,
                notificationsConfig,
                installationId,
            },
        }
    )

    useEffect(() => {
        const listener = () => {
            setNotificationsLoadable({
                type: 'loading',
                params: {
                    bankTransferInfo,
                    cardConfig,
                    notificationsConfig,
                    installationId,
                },
            })
        }

        const unsubscribe = subscribeForTokenUpdates({ listener })

        return unsubscribe
    }, [
        setNotificationsLoadable,
        storage.accounts,
        cardConfig,
        bankTransferInfo,
        notificationsConfig,
        installationId,
    ])

    useEffect(() => {
        switch (notificationsLoadable.type) {
            case 'loading':
            case 'loaded':
                break
            case 'error':
                captureError(notificationsLoadable.error)
                break
            /* istanbul ignore next */
            default:
                return notReachable(notificationsLoadable)
        }
    }, [notificationsLoadable])

    useEffect(() => {
        switch (ZealPlatform.OS) {
            case 'web':
                break

            case 'ios':
            case 'android':
                fetchAppsAndReportOnce({ installationId }).catch(captureError)
                break

            default:
                notReachable(ZealPlatform)
        }
    }, [installationId])

    return (
        <>
            <AppUpdateChecker installationId={installationId} />

            <AppDeepLinkChecker
                cardConfig={cardConfig}
                defaultCurrencyConfig={storage.defaultCurrencyConfig}
                sessionPassword={sessionPassword}
                installationId={installationId}
                networkRPCMap={storage.networkRPCMap}
                networkMap={networkMap}
                accountsMap={storage.accounts}
                keyStoreMap={storage.keystoreMap}
            />

            <WalletConnectListener
                key={storage.defaultCurrencyConfig.defaultCurrency.id}
                defaultCurrencyConfig={storage.defaultCurrencyConfig}
                selectedAccount={storage.accounts[selectedAddress]}
                customCurrencyMap={storage.customCurrencies}
                currencyHiddenMap={storage.currencyHiddenMap}
                cardConfig={cardConfig}
                accountsMap={storage.accounts}
                feePresetMap={storage.feePresetMap}
                gasCurrencyPresetMap={storage.gasCurrencyPresetMap}
                installationId={installationId}
                keyStoreMap={storage.keystoreMap}
                networkMap={networkMap}
                networkRPCMap={storage.networkRPCMap}
                portfolioMap={storage.portfolios}
                sessionPassword={sessionPassword}
                walletConnectInstanceLoadable={walletConnectInstanceLoadable}
                onMsg={onMsg}
            />

            <PortfolioLoader
                experimentalMode={experimentalMode}
                languageSettings={languageSettings}
                referralConfig={referralConfig}
                appRating={appRating}
                celebrationConfig={celebrationConfig}
                defaultCurrencyConfig={storage.defaultCurrencyConfig}
                browserTabState={browserTabState}
                initialActiveTab={initialActiveTab}
                earnHistoricalTakerUserCurrencyRateMap={
                    earnHistoricalTakerUserCurrencyRateMap
                }
                earnTakerMetrics={earnTakerMetrics}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                cardConfig={cardConfig}
                walletConnectInstanceLoadable={walletConnectInstanceLoadable}
                connections={connections}
                currencyHiddenMap={currencyHiddenMap}
                currencyPinMap={currencyPinMap}
                customCurrencies={customCurrencies}
                installationId={installationId}
                installationCampaign={installationCampaign}
                mode={mode}
                networkMap={networkMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_earn_last_recharge_transaction_hash_loaded':
                        case 'on_custom_currency_delete_request':
                        case 'on_custom_currency_update_request':
                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                        case 'on_swap_clicked':
                        case 'on_bridge_clicked':
                        case 'on_send_clicked':
                        case 'on_bank_transfer_selected':
                        case 'on_token_pin_click':
                        case 'on_token_un_pin_click':
                        case 'on_token_hide_click':
                        case 'on_token_un_hide_click':
                        case 'on_profile_change_confirm_click':
                        case 'on_send_nft_click':
                        case 'on_dismiss_kyc_button_clicked':
                        case 'on_kyc_try_again_clicked':
                        case 'bridge_completed':
                        case 'on_recovery_kit_setup':
                        case 'transaction_request_completed':
                        case 'transaction_request_failed':
                        case 'on_withdrawal_monitor_fiat_transaction_success':
                        case 'transaction_request_replaced':
                        case 'on_open_fullscreen_view_click':
                        case 'on_zwidget_expand_request':
                        case 'on_account_label_change_submit':
                        case 'confirm_account_delete_click':
                        case 'on_rewards_warning_confirm_account_delete_click':
                        case 'on_add_private_key_click':
                        case 'on_account_create_request':
                        case 'track_wallet_clicked':
                        case 'add_wallet_clicked':
                        case 'hardware_wallet_clicked':
                        case 'safe_wallet_clicked':
                        case 'recover_safe_wallet_clicked':
                        case 'account_item_clicked':
                        case 'on_disconnect_dapps_click':
                        case 'on_delete_all_dapps_confirm_click':
                        case 'on_lock_zeal_click':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'transaction_cancel_failure_accepted':
                        case 'cancel_submitted':
                        case 'transaction_failure_accepted':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_submited':
                        case 'on_card_transactions_fetch_success':
                        case 'on_card_onboarded_account_state_received':
                        case 'on_card_imported_success_animation_complete':
                        case 'on_onboarded_card_imported_success_animation_complete':
                        case 'on_browser_url_change':
                        case 'on_browser_closed':
                        case 'portfolio_loaded':
                        case 'on_earn_deposit_success':
                        case 'on_get_cashback_currency_clicked':
                        case 'on_cashback_loaded':
                        case 'import_card_owner_clicked':
                        case 'on_earn_updated':
                        case 'on_card_disconnected':
                        case 'on_switch_card_new_card_selected':
                        case 'on_earnings_fetched':
                        case 'on_historical_taker_user_currency_rate_fetched':
                        case 'on_default_currency_selected':
                        case 'on_card_import_on_import_keys_clicked':
                        case 'on_create_smart_wallet_clicked':
                        case 'on_address_scanned':
                        case 'on_monerium_deposit_success_go_to_wallet_clicked':
                        case 'on_add_funds_click':
                        case 'on_accounts_create_success_animation_finished':
                        case 'on_add_label_to_track_only_account_during_send':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_switch_bank_transfer_provider_clicked':
                        case 'on_buy_clicked':
                        case 'on_meta_mask_mode_changed_pupup_refresh_page_clicked':
                        case 'on_dismiss_bridge_widget_click':
                        case 'on_address_scanned_and_add_label':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_gnosis_pay_account_created':
                        case 'on_do_bank_transfer_clicked':
                        case 'on_dissmiss_card_kyc_onboarding_widget_clicked':
                        case 'on_app_rating_submitted':
                        case 'on_cashback_celebration_triggered':
                        case 'on_earn_celebration_triggered':
                        case 'on_swaps_io_swap_requests_fetched':
                        case 'on_swaps_io_swap_request_created':
                        case 'on_swaps_io_transaction_activity_swap_started':
                        case 'on_swaps_io_transaction_activity_completed':
                        case 'on_swaps_io_transaction_activity_failed':
                        case 'on_new_virtual_card_created_successfully':
                        case 'on_dismiss_add_to_wallet_banner_clicked':
                        case 'on_transaction_activities_loaded':
                        case 'on_pending_send_transaction_activity_completed':
                        case 'on_pending_send_transaction_activity_failed':
                        case 'on_virtual_card_order_created_animation_completed':
                        case 'on_dissmiss_card_kyc_onboarded_widget_clicked':
                        case 'on_card_b_reward_dissmiss_clicked':
                        case 'card_breward_claimed':
                        case 'card_brewards_updated':
                        case 'on_pending_breward_claim_transaction_activity_completed':
                        case 'on_pending_breward_claim_transaction_activity_failed':
                        case 'on_pending_areward_claim_transaction_activity_completed':
                        case 'on_pending_areward_claim_transaction_activity_failed':
                        case 'on_a_reward_claimed_successfully':
                        case 'on_a_rewards_configured':
                        case 'on_physical_card_activated_info_screen_closed':
                        case 'on_physical_card_activated_info_screen_closed_wallet_not_funded':
                        case 'on_card_onboarded_account_state_card_owner_signer_or_keystore_not_found':
                        case 'on_language_settings_language_selected':
                        case 'on_pending_card_top_up_failed':
                        case 'on_card_top_up_success':
                        case 'on_experimental_change_clicked':
                        case 'session_password_decrypted':
                            onMsg(msg)
                            break

                        case 'on_notifications_config_changed':
                            onMsg(msg)
                            setNotificationsLoadable({
                                type: 'loading',
                                params: {
                                    bankTransferInfo,
                                    cardConfig,
                                    installationId,
                                    notificationsConfig:
                                        msg.notificationsConfig,
                                },
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
                selectedAddress={selectedAddress}
                sessionPassword={sessionPassword}
                storage={storage}
                appBrowserProviderScript={appBrowserProviderScript}
            />
        </>
    )
}
