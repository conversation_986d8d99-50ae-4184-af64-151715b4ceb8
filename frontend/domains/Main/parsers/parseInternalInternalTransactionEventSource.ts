import { failure, Result, string, success } from '@zeal/toolkit/Result'

import { InternalTransactionEventSource } from '..'

const InternalTransactionEventSourceMap: Record<
    InternalTransactionEventSource,
    true
> = {
    bridge: true,
    bridgeApprove: true,
    bridgeGasEstimation: true,
    cardAddCash: true,
    cardCashBackDeposit: true,
    earnAddTakerToHolder: true,
    earnDeployWithRechargePreferences: true,
    earnDeposit: true,
    earnDisableRecharge: true,
    earnEnableRecharge: true,
    earnHolderDeploy: true,
    earnTakerDeploy: true,
    earnWithdrawal: true,
    kinetexApprove: true,
    kinetexNativeSwap: true,
    offramp: true,
    send: true,
    sendGasEstimation: true,
    smartWalletCreationReferenceDeployment: true,
    smartWalletRecoveryAddOwner: true,
    swap: true,
    swapApprove: true,
    swapGasEstimation: true,
    swapsIOGasEstimation: true,
    topupCardEarnApprove: true,
    topupCardEarnSign: true,
    topupCardEarnWithdraw: true,
    topupCardEarnWithdrawWithOptionalApproval: true,
    topupCardFromEarn: true,
    topupCardSend: true,
    topupCardSwapApproval: true,
    topupCardSwapApprovalOrDeployment: true,
    topupCardSwapNativeSend: true,
    topupCardSwapSign: true,
    topupSend: true,
    transactionRequestWidget: true,
    unblockOfframpGasEstimation: true,
}

export const parseInternalInternalTransactionEventSource = (
    input: unknown
): Result<unknown, InternalTransactionEventSource> =>
    string(input).andThen((str) =>
        InternalTransactionEventSourceMap[str as InternalTransactionEventSource]
            ? success(str as InternalTransactionEventSource)
            : failure(`${str} is not a valid InternalTransactionEventSource`)
    )
