import { match, object, Result, shape } from '@zeal/toolkit/Result'

import { InternalTransactionActionSource } from '@zeal/domains/Main'
import { parseInternalInternalTransactionEventSource } from '@zeal/domains/Main/parsers/parseInternalInternalTransactionEventSource'

export const parseInternalTransactionActionSource = (
    input: unknown
): Result<unknown, InternalTransactionActionSource> =>
    object(input).andThen((actionObj) =>
        shape({
            type: match(actionObj.type, 'internal' as const),
            transactionEventSource: parseInternalInternalTransactionEventSource(
                actionObj.transactionEventSource
            ),
        })
    )
