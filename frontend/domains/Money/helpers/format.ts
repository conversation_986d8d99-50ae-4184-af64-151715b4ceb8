import { LanguageRule } from '@zeal/uikit/Language'

import { notReachable } from '@zeal/toolkit'
import {
    toFixedWithFraction,
    unsafe_toNumberWithFraction,
} from '@zeal/toolkit/BigInt'

import { Money2 } from '..'

export type Variant =
    | 'just_2_decimals'
    | 'min_2_decimals_max_2_significant'
    | 'min_2_decimals_max_4_significant'
    | 'max_6_significant'
    | 'no_decimals'
    | 'short'

const THRESHOLDS = [
    { limit: 1_000_000_000, suffix: 'B' },
    { limit: 1_000_000, suffix: 'M' },
    { limit: 1_000, suffix: 'K' },
]

const formatIntegerPart = (input: string, rule: LanguageRule): string => {
    let result = ''
    for (let i = input.length - 1; i >= 0; i--) {
        result = input[i] + result
        if (i > 0 && (input.length - i) % 3 === 0) {
            result = rule.digitGroupingSeparator + result
        }
    }
    return result
}

const maxSignificant = (digits: string, maxSignificant: number) => {
    const leadingZeroes = digits.match(/^0+/)?.[0] || ''
    const significant = digits.replace(/^0+/, '')
    const truncatedSignificant = significant.slice(0, maxSignificant)
    const truncatedSignificantNoTrailingZeroes = truncatedSignificant.replace(
        /0+$/,
        ''
    )

    return `${leadingZeroes}${truncatedSignificantNoTrailingZeroes}`
}

/**
 * @deprication
 *
 * It is recommended to use this `useFormat` hook directly.
 * The `format` function is not meant to be used on its own as it is internal to the hook.
 */
export const format = ({
    money,
    variant,
    rule,
}: {
    money: Money2
    variant: Variant
    rule: LanguageRule
}) => {
    const str = toFixedWithFraction(money.amount, money.currency.fraction)

    if (str === '') {
        return str
    }

    const splitted = str.split('.')

    const integerPart = splitted[0]
    const decimalPart = splitted[1] || null

    switch (variant) {
        case 'no_decimals':
            return formatIntegerPart(integerPart, rule)

        case 'min_2_decimals_max_4_significant': {
            const formattedIntegerPart = formatIntegerPart(integerPart, rule)

            if (!decimalPart) {
                return formattedIntegerPart
            }

            const formattedDecimalPart = maxSignificant(decimalPart, 4).padEnd(
                2,
                '0'
            )

            return `${formattedIntegerPart}${rule.decimalSeparator}${formattedDecimalPart}`
        }

        case 'max_6_significant': {
            const formattedIntegerPart = formatIntegerPart(integerPart, rule)

            if (!decimalPart) {
                return formattedIntegerPart
            }

            const formattedDecimalPart = maxSignificant(decimalPart, 6)

            return `${formattedIntegerPart}${rule.decimalSeparator}${formattedDecimalPart}`
        }

        case 'min_2_decimals_max_2_significant': {
            const formattedIntegerPart = formatIntegerPart(integerPart, rule)

            if (!decimalPart) {
                return formattedIntegerPart
            }

            const formattedDecimalPart = maxSignificant(decimalPart, 2).padEnd(
                2,
                '0'
            )

            return `${formattedIntegerPart}${rule.decimalSeparator}${formattedDecimalPart}`
        }

        case 'just_2_decimals': {
            const formattedIntegerPart = formatIntegerPart(integerPart, rule)
            const formattedDecimalPart = decimalPart
                ? decimalPart.slice(0, 2).padEnd(2, '0')
                : '00'

            return `${formattedIntegerPart}${rule.decimalSeparator}${formattedDecimalPart}`
        }

        case 'short': {
            const value = unsafe_toNumberWithFraction(
                money.amount,
                money.currency.fraction
            )

            const threshold = THRESHOLDS.find(
                (threshold) => value >= threshold.limit
            )

            if (!threshold) {
                const formattedIntegerPart = formatIntegerPart(
                    integerPart,
                    rule
                )
                const formattedDecimalPart = decimalPart
                    ? decimalPart.slice(0, 2).padEnd(2, '0')
                    : '00'

                return `${formattedIntegerPart}${rule.decimalSeparator}${formattedDecimalPart}`
            }

            const formattedValue = (value / threshold.limit)
                .toFixed(1)
                .replace(/\.0$/, '')

            return `${formattedValue}${threshold.suffix}`
        }

        default:
            return notReachable(variant)
    }
}
