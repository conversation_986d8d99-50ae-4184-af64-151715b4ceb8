import { DEFAULT_LANGUAGE, getLanguageRule } from '@zeal/uikit/Language'
import { useLanguage } from '@zeal/uikit/Language/LanguageContext'

import { CryptoMoney, FiatMoney, Money2 } from '..'
import { format, Variant } from '../helpers/format'
import {
    formattedCryptoMoneyCompact,
    formattedFiatMoneyCompact,
} from '../helpers/formattedMoneyCompact'
import {
    formattedCryptoMoneyOrCash,
    formattedCryptoMoneyPrecise,
    formattedFiatMoneyPrecise,
    formattedMoneyPrecise,
    formattedMoneyPreciseParts,
} from '../helpers/formattedMoneyPrecise'
import { formattedFiatMoneyShort } from '../helpers/formattedMoneyShort'

export type Sign = '+' | '-' | null

export const useMoneyFormat = (): {
    format: (_: { money: Money2; variant: Variant }) => string
    formatForInputs: (_: { money: Money2 }) => string
    formattedMoneyPrecise: (_: {
        money: Money2
        withSymbol: boolean
        sign: Sign
    }) => string

    formattedMoneyPreciseParts: (_: { money: Money2; sign: Sign }) => {
        sign: '+' | '-' | null
        formattedIntPart: string
        formattedDecimalsWithSeparator: string | null
    }
    formattedFiatMoneyPrecise: (_: {
        money: FiatMoney
        withSymbol: boolean
        sign: Sign
    }) => string
    formattedCryptoMoneyPrecise: (_: {
        money: CryptoMoney
        withSymbol: boolean
        sign: Sign
    }) => string
    formattedCryptoMoneyOrCash: (_: {
        money: CryptoMoney
        withSymbol: boolean
        sign: Sign
    }) => string
    formattedCryptoMoneyCompact: (_: { money: CryptoMoney }) => string
    formattedFiatMoneyCompact: (_: { money: FiatMoney }) => string
    formattedFiatMoneyShort: (_: { money: FiatMoney }) => string
} => {
    const { currentSelectedLanguage } = useLanguage()
    const rule = getLanguageRule(currentSelectedLanguage)

    return {
        format: (props) => format({ ...props, rule }),
        formatForInputs: (props) =>
            format({
                ...props,
                rule: getLanguageRule(DEFAULT_LANGUAGE),
                variant: 'max_6_significant',
            }),
        formattedMoneyPrecise: (props) =>
            formattedMoneyPrecise({ ...props, rule }),
        formattedMoneyPreciseParts: (props) =>
            formattedMoneyPreciseParts({
                ...props,
                rule,
            }),
        formattedFiatMoneyPrecise: (props) =>
            formattedFiatMoneyPrecise({ ...props, rule }),
        formattedCryptoMoneyPrecise: (props) =>
            formattedCryptoMoneyPrecise({ ...props, rule }),
        formattedCryptoMoneyOrCash: (props) =>
            formattedCryptoMoneyOrCash({ ...props, rule }),
        formattedCryptoMoneyCompact: (props) =>
            formattedCryptoMoneyCompact({ ...props, rule }),
        formattedFiatMoneyCompact: (props) =>
            formattedFiatMoneyCompact({ ...props, rule }),
        formattedFiatMoneyShort: (props) =>
            formattedFiatMoneyShort({ ...props, rule }),
    }
}
