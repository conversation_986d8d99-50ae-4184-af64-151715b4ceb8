import { notReachable } from '@zeal/toolkit'
import { max } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hex from '@zeal/toolkit/Hexadecimal'
import { fromBigInt } from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import * as Web3 from '@zeal/toolkit/Web3'

import { SAFE_4337_MODULE_ENTRYPOINT_ADDRESS } from '@zeal/domains/Address/constants'
import {
    CryptoCurrency,
    CurrencyId,
    DefaultCurrency,
    GasCurrencyPresetMap,
    KnownCryptoCurrencies,
} from '@zeal/domains/Currency'
import { fetchAllowance } from '@zeal/domains/Currency/api/fetchAllowance'
import {
    PAYMASTER_ADDRESS,
    PAYMASTER_MAP,
} from '@zeal/domains/Currency/constants'
import { createApprovalTransaction } from '@zeal/domains/Currency/helpers/createApprovalTransaction'
import { FXRate2 } from '@zeal/domains/FXRate'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { SafeInstance } from '@zeal/domains/KeyStore/helpers/fetchSafe4337Instance'
import { CryptoMoney } from '@zeal/domains/Money'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { createTransferEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/createERC20EthSendTransaction'
import {
    BoostedUserOperationWithoutSignature,
    BoostedUserOperationWithSignature,
    ERC20GasAbstractionTransactionFee,
    FeeAndGasEstimates,
    NativeGasAbstractionTransactionFee,
    NonSponsoredGasAbstractionTransactionFee,
    NonSponsoredUserOperationFee,
    UserOperationHash,
    UserOperationWithoutSignature,
} from '@zeal/domains/UserOperation'
import { calculateIfGasAbstractionCurrencyApprovalNeeded } from '@zeal/domains/UserOperation/api/fetchGasAbstractionTransactionFees'
import {
    MAX_FEE_PER_GAS_BUFFER_MULTIPLIER,
    MINIMUM_CALL_GAS_LIMIT,
    PRECISION,
} from '@zeal/domains/UserOperation/api/fetchGasEstimates'
import { parsePaymasterAndData } from '@zeal/domains/UserOperation/api/fetchPaymasterAndData'
import { fetchPaymasterResponse } from '@zeal/domains/UserOperation/api/fetchPaymasterResponse'
import {
    APPROVAL_CALL_GAS_LIMIT_BUFFER,
    DUMMY_EOA_SIGNATURE,
    DUMMY_PASSKEY_SIGNATURE,
    TOKEN_PAYMASTER_VERIFICATION_GAS_LIMIT_BUFFER,
} from '@zeal/domains/UserOperation/constants'
import { calculateUserOperationHash } from '@zeal/domains/UserOperation/helpers/calculateUserOperationHash'
import { createAddOwnerMetaTransaction } from '@zeal/domains/UserOperation/helpers/createAddOwnerMetaTransaction'
import { ethSendTransactionToMetaTransactionData } from '@zeal/domains/UserOperation/helpers/ethSendTransactionToMetaTransactionData'
import { getDummySignature } from '@zeal/domains/UserOperation/helpers/getDummySignature'
import { getSignatureVGLBuffer } from '@zeal/domains/UserOperation/helpers/getSignatureVGLBuffer'
import { metaTransactionDatasToUserOperationCallData } from '@zeal/domains/UserOperation/helpers/metaTransactionDatasToUserOperationCallData'
import { signUserOperationHashWithLocalSigner } from '@zeal/domains/UserOperation/helpers/signUserOperationHashWithLocalSigner'
import { signUserOperationHashWithPassKey } from '@zeal/domains/UserOperation/helpers/signUserOperationHashWithPassKey'

import { Form, SafeTransactionRequest } from '../fetch'

/**
 * @deprecated don't need that if send from domain is used
 */
export type InitialUserOperation = {
    sender: Web3.address.Address
    nonce: bigint
    initCode: Hex.Hexadecimal
    callData: Hex.Hexadecimal
    signature: Hex.Hexadecimal
    paymasterAndData: Hex.Hexadecimal
}

const ensureMinGasAndPrice = (
    fee: FeeAndGasEstimates,
    safeInstance: SafeInstance,
    keystore: Safe4337
): FeeAndGasEstimates => {
    const signatureGasLimitBuffer = getSignatureVGLBuffer({
        safeInstance,
        keystore,
    })

    return {
        gasEstimate: {
            ...fee.gasEstimate,
            callGasLimit: max(
                fee.gasEstimate.callGasLimit,
                MINIMUM_CALL_GAS_LIMIT
            ),
            verificationGasLimit:
                fee.gasEstimate.verificationGasLimit + signatureGasLimitBuffer,
        },
        gasPrice: {
            maxFeePerGas:
                (fee.gasPrice.maxFeePerGas *
                    BigInt(MAX_FEE_PER_GAS_BUFFER_MULTIPLIER * PRECISION)) /
                BigInt(PRECISION),
            maxPriorityFeePerGas: fee.gasPrice.maxPriorityFeePerGas,
        },
    }
}

const addPaymasterBuffer = (fee: FeeAndGasEstimates): FeeAndGasEstimates => {
    const { gasEstimate } = fee
    return {
        ...fee,
        gasEstimate: {
            ...gasEstimate,
            verificationGasLimit:
                gasEstimate.verificationGasLimit +
                TOKEN_PAYMASTER_VERIFICATION_GAS_LIMIT_BUFFER,
        },
    }
}

const addAprovalBuffer = (fee: FeeAndGasEstimates): FeeAndGasEstimates => {
    const { gasEstimate } = fee
    return {
        ...fee,
        gasEstimate: {
            ...gasEstimate,
            callGasLimit:
                gasEstimate.callGasLimit + APPROVAL_CALL_GAS_LIMIT_BUFFER,
        },
    }
}

export const createInitialUserOperation = ({
    form,
    safeInstance,
    keystore,
    network,
    nonce,
}: {
    safeInstance: SafeInstance
    form: Form
    nonce: bigint
    network: Network
    keystore: Safe4337
}): InitialUserOperation => {
    const cryptoCurrency = form.cryptoCurrency
    const sender = form.fromWallet.address
    // this one is for estimation only, we do 1n amount to avoid 0 transaction that is lower on gas as it doesnt write to storage
    const inputAmount = 1n

    const ethSendTransaction = createTransferEthSendTransaction({
        network,
        amount: { amount: inputAmount, currency: cryptoCurrency },
        from: form.fromWallet.address,
        to: form.toAddress,
    })

    const ethSendMetaTransaction =
        ethSendTransactionToMetaTransactionData(ethSendTransaction)

    const addOwner = createAddOwnerMetaTransaction({
        safeAddress: safeInstance.safeAddress,
        owner: keystore.localSignerKeyStore.address,
        treshold: 1n,
    })

    const metaTransactions = (() => {
        switch (safeInstance.type) {
            case 'deployed':
                return safeInstance.owners.includes(
                    keystore.localSignerKeyStore.address
                )
                    ? [ethSendMetaTransaction]
                    : [addOwner, ethSendMetaTransaction]
            case 'not_deployed':
                return [addOwner, ethSendMetaTransaction]
            default:
                return notReachable(safeInstance)
        }
    })()

    const callData = metaTransactionDatasToUserOperationCallData({
        metaTransactionDatas: metaTransactions,
    })

    const initCode = (() => {
        switch (safeInstance.type) {
            case 'not_deployed':
                return safeInstance.deploymentInitCode
            case 'deployed':
                return '0x'
            default:
                return notReachable(safeInstance)
        }
    })()

    return {
        initCode,
        callData,
        signature: getDummySignature({ safeInstance, keyStore: keystore }),
        nonce,
        sender,
        paymasterAndData: '0x',
    }
}

const calculateAmount = (
    fee: FeeAndGasEstimates,
    currency: CryptoCurrency
): CryptoMoney => {
    return {
        amount:
            (fee.gasEstimate.callGasLimit +
                fee.gasEstimate.verificationGasLimit +
                fee.gasEstimate.preVerificationGas) *
            fee.gasPrice.maxFeePerGas,
        currency,
    }
}

export const getFeeAndMaxBalance = ({
    estimate,
    keystore,
    network,
    nativeCurrency,
    gasCurrencyPresetMap,
    safeInstance,
    portfolio,
    fromCurrency,
    nativeToDefaultRate,
    currencies,
    approvals,
    crossRates,
}: {
    nativeToDefaultRate: FXRate2<CryptoCurrency, DefaultCurrency> | null
    estimate: FeeAndGasEstimates
    fromCurrency: CryptoCurrency
    nativeCurrency: CryptoCurrency
    network: Network
    keystore: Safe4337
    gasCurrencyPresetMap: GasCurrencyPresetMap
    portfolio: ServerPortfolio2
    safeInstance: SafeInstance
    approvals: CryptoMoney[]
    crossRates: FXRate2<CryptoCurrency, CryptoCurrency>[]
    currencies: KnownCryptoCurrencies
}): {
    fee: NonSponsoredUserOperationFee
    maxBalance: CryptoMoney
} => {
    const userOperationFee = calculateUserOperationFee({
        network,
        nativeCurrency,
        bundlerGasEstimate: estimate,
        keystore,
        safeInstance,
        gasCurrencyPresetMap,
        portfolio,
        currencies,
        nativeToDefaultRate,
        crossRates,
        approvals,
    })

    const selectedTokenOnForm = portfolio.tokens.find(
        (token) => token.balance.currency.id === fromCurrency.id
    )

    if (!selectedTokenOnForm) {
        // impossible state?
        return {
            fee: userOperationFee,
            maxBalance: {
                amount: 0n,
                currency: fromCurrency,
            },
        }
    }

    if (
        userOperationFee.selectedFee.feeInTokenCurrency.currency.id ===
        selectedTokenOnForm.balance.currency.id
    ) {
        return {
            fee: userOperationFee,
            maxBalance:
                selectedTokenOnForm.balance.amount >
                userOperationFee.selectedFee.feeInTokenCurrency.amount
                    ? {
                          amount:
                              selectedTokenOnForm.balance.amount -
                              userOperationFee.selectedFee.feeInTokenCurrency
                                  .amount,
                          currency: fromCurrency,
                      }
                    : {
                          amount: 0n,
                          currency: fromCurrency,
                      },
        }
    }
    return {
        fee: userOperationFee,
        maxBalance: {
            amount: selectedTokenOnForm.balance.amount,
            currency: fromCurrency,
        },
    }
}

const calculateUserOperationFee = ({
    bundlerGasEstimate,
    safeInstance,
    gasCurrencyPresetMap,
    keystore,
    nativeCurrency,
    portfolio,
    network,
    currencies,
    nativeToDefaultRate,
    approvals,
    crossRates,
}: {
    bundlerGasEstimate: FeeAndGasEstimates
    safeInstance: SafeInstance
    keystore: Safe4337
    nativeCurrency: CryptoCurrency
    gasCurrencyPresetMap: GasCurrencyPresetMap
    network: Network
    portfolio: ServerPortfolio2
    currencies: KnownCryptoCurrencies
    approvals: CryptoMoney[]
    crossRates: FXRate2<CryptoCurrency, CryptoCurrency>[]

    nativeToDefaultRate: FXRate2<CryptoCurrency, DefaultCurrency> | null
}): NonSponsoredUserOperationFee => {
    const normalizedEstimate = ensureMinGasAndPrice(
        bundlerGasEstimate,
        safeInstance,
        keystore
    )

    const feeInNativeTokenCurrency = calculateAmount(
        normalizedEstimate,
        nativeCurrency
    )

    const feeInDefaultCurrency = nativeToDefaultRate
        ? applyRate2({
              baseAmount: feeInNativeTokenCurrency,
              rate: nativeToDefaultRate,
          })
        : null

    const paymasterEstimate = addPaymasterBuffer(normalizedEstimate)

    const gasCurrencyId = gasCurrencyPresetMap[network.hexChainId]

    // FIXME :: @Nicvaniek rather return crossRates with supported currencies so we don't have to do this and find crossRates
    const supportedCurrencies: CryptoCurrency[] = PAYMASTER_MAP[
        network.hexChainId
    ]
        .map((id) => currencies[id])
        .filter(
            (currency) =>
                currency.id === nativeCurrency.id ||
                !!crossRates.find((rate) => rate.quote.id === currency.id)
        )

    const nativeFeeOption: NativeGasAbstractionTransactionFee = {
        type: 'native_gas_abstraction_transaction_fee',
        feeInTokenCurrency: feeInNativeTokenCurrency,
        feeInDefaultCurrency: feeInDefaultCurrency,
        gasEstimate: normalizedEstimate.gasEstimate,
        gasPrice: normalizedEstimate.gasPrice,
        callData: '0x', // in vanity flow callData in fee not used
    }

    const feeOptions: NonSponsoredGasAbstractionTransactionFee[] =
        supportedCurrencies.map((currency) => {
            if (currency.id === feeInNativeTokenCurrency.currency.id) {
                return nativeFeeOption
            }
            return calculateERC20GasAbstractionTransactionFee({
                network,
                feeSelectedCurrencyId: currency.id,
                paymasterGasEstimate: paymasterEstimate,
                nativeToDefaultRate,
                crossRates,
                approvals,
            })
        })

    if (gasCurrencyId) {
        const selectedFeeOption = feeOptions.find(
            ({ feeInTokenCurrency }) =>
                feeInTokenCurrency.currency.id === gasCurrencyId
        )

        if (selectedFeeOption) {
            return {
                type: 'non_sponsored_user_operation_fee',
                nativeCurrency,
                feeOptions,
                selectedFee: selectedFeeOption,
            }
        }
    }

    const filteredPortfolioTokens = portfolio.tokens
        .filter((token) =>
            supportedCurrencies.find(
                ({ id }) => token.balance.currency.id === id
            )
        )
        .toSorted((a, b) => {
            const defaultCurrencyA = a.priceInDefaultCurrency?.amount || 0n
            const defaultCurrencyB = b.priceInDefaultCurrency?.amount || 0n

            if (defaultCurrencyA > defaultCurrencyB) return -1
            if (defaultCurrencyA < defaultCurrencyB) return 1
            return 0
        })

    if (!filteredPortfolioTokens.length) {
        return {
            type: 'non_sponsored_user_operation_fee',
            nativeCurrency,
            feeOptions,
            selectedFee: nativeFeeOption,
        }
    }

    const nativeToken =
        filteredPortfolioTokens.find(
            (token) =>
                token.balance.currency.id ===
                    feeInNativeTokenCurrency.currency.id &&
                token.balance.amount > feeInNativeTokenCurrency.amount
        ) ||
        (filteredPortfolioTokens[0].balance.currency.id ===
        feeInNativeTokenCurrency.currency.id
            ? filteredPortfolioTokens[0]
            : null)

    if (nativeToken) {
        return {
            type: 'non_sponsored_user_operation_fee',
            nativeCurrency,
            feeOptions,
            selectedFee: nativeFeeOption,
        }
    }

    return {
        type: 'non_sponsored_user_operation_fee',
        nativeCurrency,
        feeOptions,
        selectedFee:
            feeOptions.find(
                (option) =>
                    option.feeInTokenCurrency.currency.id ===
                    filteredPortfolioTokens[0].balance.currency.id
            ) || nativeFeeOption,
    }
}

const calculateERC20GasAbstractionTransactionFee = ({
    feeSelectedCurrencyId,
    network,
    paymasterGasEstimate,
    nativeToDefaultRate,
    crossRates,
    approvals,
}: {
    approvals: CryptoMoney[]
    paymasterGasEstimate: FeeAndGasEstimates
    crossRates: FXRate2<CryptoCurrency, CryptoCurrency>[]
    network: Network
    nativeToDefaultRate: FXRate2<CryptoCurrency, DefaultCurrency> | null
    feeSelectedCurrencyId: CurrencyId
}): ERC20GasAbstractionTransactionFee => {
    const crossRate = crossRates.find(
        (crossRate) => crossRate.quote.id === feeSelectedCurrencyId
    )

    const erc20FeeInNativeCurrency = calculateAmount(
        paymasterGasEstimate,
        network.nativeCurrency
    )

    if (!crossRate) {
        throw new ImperativeError(
            "we don't get cross rate for fee selected token",
            { currencyId: feeSelectedCurrencyId }
        )
    }

    const feeInTokenCurrency = applyRate2({
        baseAmount: erc20FeeInNativeCurrency,
        rate: crossRate,
    })

    const allowance = approvals.find(
        (approval) => approval.currency.id === feeSelectedCurrencyId
    )

    if (!allowance) {
        throw new ImperativeError(
            "we don't get allowance for fee selected token",
            { currencyId: feeSelectedCurrencyId }
        )
    }
    const aproval = calculateIfGasAbstractionCurrencyApprovalNeeded({
        feeAmount: feeInTokenCurrency,
        allowance,
    })

    switch (aproval.type) {
        case 'approval_not_required': {
            const erc20FeeInDefaultCurrency = nativeToDefaultRate
                ? applyRate2({
                      baseAmount: erc20FeeInNativeCurrency,
                      rate: nativeToDefaultRate,
                  })
                : null

            return {
                type: 'erc20_gas_abstraction_transaction_fee',
                feeInTokenCurrency,
                feeInDefaultCurrency: erc20FeeInDefaultCurrency,
                gasEstimate: paymasterGasEstimate.gasEstimate,
                gasPrice: paymasterGasEstimate.gasPrice,
                callData: '0x', // in vanity flow callData in fee not used
            }
        }
        case 'approval_required': {
            const paymasterWithApproval = addAprovalBuffer(paymasterGasEstimate)
            const erc20FeeInNativeCurrency = calculateAmount(
                paymasterWithApproval,
                network.nativeCurrency
            )
            const erc20FeeInDefaultCurrency = nativeToDefaultRate
                ? applyRate2({
                      baseAmount: erc20FeeInNativeCurrency,
                      rate: nativeToDefaultRate,
                  })
                : null

            return {
                type: 'erc20_gas_abstraction_transaction_fee',
                feeInTokenCurrency,
                feeInDefaultCurrency: erc20FeeInDefaultCurrency,
                gasEstimate: paymasterWithApproval.gasEstimate,
                gasPrice: paymasterWithApproval.gasPrice,
                callData: '0x', // in vanity flow callData in fee not used
            }
        }
        default:
            return notReachable(aproval)
    }
}

/**
 * @deprecated use UserOperationWithSignature2 from domain
 */
export type UserOperation = {
    type: 'user_operation_with_signature'
    sender: Web3.address.Address
    callData: Hex.Hexadecimal
    nonce: bigint
    entrypoint: Web3.address.Address

    signature: Hex.Hexadecimal

    initCode: Hex.Hexadecimal

    maxFeePerGas: bigint
    maxPriorityFeePerGas: bigint

    callGasLimit: Hex.Hexadecimal
    verificationGasLimit: bigint
    preVerificationGas: bigint

    paymasterAndData: Hex.Hexadecimal
}

/**
 * @deprecated use sign from domain
 */
export const sign = async ({
    userOperationRequest,
    sessionPassword,
    networkRPCMap,
    networkMap,
    signal,
}: {
    userOperationRequest: SafeTransactionRequest
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<UserOperation | BoostedUserOperationWithSignature> => {
    const { network, form, keyStore, safeInstance } = userOperationRequest

    const ethSendTransaction = createTransferEthSendTransaction({
        network,
        amount: form.amount,
        from: form.fromWallet.address,
        to: form.toAddress,
    })

    const ethSendMetaTransaction =
        ethSendTransactionToMetaTransactionData(ethSendTransaction)

    const metaTransactions = (() => {
        const addOwner = createAddOwnerMetaTransaction({
            safeAddress: safeInstance.safeAddress,
            owner: keyStore.localSignerKeyStore.address,
            treshold: 1n,
        })

        switch (safeInstance.type) {
            case 'deployed':
                return safeInstance.owners.includes(
                    keyStore.localSignerKeyStore.address
                )
                    ? []
                    : [addOwner]
            case 'not_deployed':
                return [addOwner]
            default:
                return notReachable(safeInstance)
        }
    })()

    const metaAllowanceTransactions = await (async () => {
        switch (form.fee.type) {
            case 'sponsored_user_operation_fee':
                return []
            case 'non_sponsored_user_operation_fee':
                switch (form.fee.selectedFee.type) {
                    case 'native_gas_abstraction_transaction_fee':
                        return []
                    case 'erc20_gas_abstraction_transaction_fee':
                        const allowance = await fetchAllowance({
                            currency:
                                form.fee.selectedFee.feeInTokenCurrency
                                    .currency,
                            owner: form.fromWallet.address,
                            spender: PAYMASTER_ADDRESS,
                            networkRPCMap,
                            networkMap,
                            signal,
                        })

                        const approvalNeeded =
                            calculateIfGasAbstractionCurrencyApprovalNeeded({
                                allowance,
                                feeAmount:
                                    form.fee.selectedFee.feeInTokenCurrency,
                            })

                        switch (approvalNeeded.type) {
                            case 'approval_not_required':
                                return []
                            case 'approval_required': {
                                const approvalRequest =
                                    createApprovalTransaction({
                                        amount: approvalNeeded.amount,
                                        owner: form.fromWallet.address,
                                        spender: PAYMASTER_ADDRESS,
                                    })

                                return [
                                    ethSendTransactionToMetaTransactionData(
                                        approvalRequest
                                    ),
                                ]
                            }
                            default:
                                return notReachable(approvalNeeded)
                        }
                    default:
                        return notReachable(form.fee.selectedFee)
                }
            default:
                return notReachable(form.fee)
        }
    })()

    const callData = metaTransactionDatasToUserOperationCallData({
        metaTransactionDatas: [
            ...metaTransactions,
            ethSendMetaTransaction,
            ...metaAllowanceTransactions,
        ],
    })

    const initCode = (() => {
        switch (safeInstance.type) {
            case 'not_deployed':
                return safeInstance.deploymentInitCode
            case 'deployed':
                return '0x'
            default:
                return notReachable(safeInstance)
        }
    })()

    const dummySignature = (() => {
        switch (safeInstance.type) {
            case 'deployed':
                return safeInstance.owners.includes(
                    keyStore.localSignerKeyStore.address
                )
                    ? DUMMY_EOA_SIGNATURE
                    : DUMMY_PASSKEY_SIGNATURE

            case 'not_deployed':
                return DUMMY_PASSKEY_SIGNATURE
            default:
                return notReachable(safeInstance)
        }
    })()

    const initialUserOperation: InitialUserOperation = {
        sender: form.fromWallet.address,
        callData,
        nonce: userOperationRequest.nonce,
        initCode,
        signature: dummySignature,
        paymasterAndData: '0x',
    }

    const paymasterAndData = await getPaymasterAndData({
        signal,
        userOperationRequest,
        initialUserOperation,
    })

    const userOperationWithoutSignature = (():
        | UserOperationWithoutSignature
        | BoostedUserOperationWithoutSignature => {
        switch (form.fee.type) {
            case 'non_sponsored_user_operation_fee':
                return {
                    type: 'user_operation_without_signature' as const,
                    sender: initialUserOperation.sender,
                    callData: initialUserOperation.callData,
                    entrypointNonce: initialUserOperation.nonce,
                    entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
                    initCode: initialUserOperation.initCode,
                    paymasterAndData,
                    maxFeePerGas: form.fee.selectedFee.gasPrice.maxFeePerGas,
                    maxPriorityFeePerGas:
                        form.fee.selectedFee.gasPrice.maxPriorityFeePerGas,
                    callGasLimit: form.fee.selectedFee.gasEstimate.callGasLimit,
                    verificationGasLimit:
                        form.fee.selectedFee.gasEstimate.verificationGasLimit,
                    preVerificationGas:
                        form.fee.selectedFee.gasEstimate.preVerificationGas,
                }
            case 'sponsored_user_operation_fee':
                return {
                    type: 'boosted_user_operation_without_signature' as const,
                    sender: initialUserOperation.sender,
                    callData: initialUserOperation.callData,
                    entrypointNonce: initialUserOperation.nonce,
                    entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
                    initCode: initialUserOperation.initCode,
                    callGasLimit: form.fee.gasEstimate.callGasLimit,
                    verificationGasLimit:
                        form.fee.gasEstimate.verificationGasLimit,
                }
            default:
                return notReachable(form.fee)
        }
    })()

    const userOperationHash = calculateUserOperationHash({
        network,
        userOperation: userOperationWithoutSignature,
        now: Date.now(),
    })

    const signature = await getSignature({
        userOperationHash,
        network,
        safeInstance,
        keyStore,
        sessionPassword,
    })

    switch (userOperationWithoutSignature.type) {
        case 'user_operation_without_signature':
            return {
                type: 'user_operation_with_signature',
                initCode: initialUserOperation.initCode,
                callData: initialUserOperation.callData,
                sender: initialUserOperation.sender,
                nonce: initialUserOperation.nonce,

                entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,

                callGasLimit: Hex.fromBigInt(
                    userOperationWithoutSignature.callGasLimit
                ),
                maxFeePerGas: userOperationWithoutSignature.maxFeePerGas,
                maxPriorityFeePerGas:
                    userOperationWithoutSignature.maxPriorityFeePerGas,
                preVerificationGas:
                    userOperationWithoutSignature.preVerificationGas,
                verificationGasLimit:
                    userOperationWithoutSignature.verificationGasLimit,
                paymasterAndData,
                signature,
            }
        case 'boosted_user_operation_without_signature':
            return {
                ...userOperationWithoutSignature,
                type: 'boosted_user_operation_with_signature',
                signature,
            }
        default:
            return notReachable(userOperationWithoutSignature)
    }
}

const fetchERC20PaymasterAndDataV2 = async ({
    initialUserOperation,
    network,
    nonce,
    signal,
    fee,
}: {
    fee: ERC20GasAbstractionTransactionFee
    network: Network
    nonce: Hex.Hexadecimal
    initialUserOperation: InitialUserOperation
    signal?: AbortSignal
}): Promise<string> => {
    switch (network.type) {
        // TODO :: @Nicvaniek create narrowed network type for chains that support gas abstraction
        case 'predefined':
        case 'testnet':
            return fetchPaymasterResponse({
                network,
                signal,
                request: {
                    id: generateRandomNumber(),
                    jsonrpc: '2.0',
                    method: 'pm_sponsorUserOperation',
                    params: [
                        {
                            sender: initialUserOperation.sender,
                            initCode: initialUserOperation.initCode || '0x',
                            callData: initialUserOperation.callData,
                            nonce,
                            maxFeePerGas: fromBigInt(fee.gasPrice.maxFeePerGas),
                            maxPriorityFeePerGas: fromBigInt(
                                fee.gasPrice.maxPriorityFeePerGas
                            ),
                            callGasLimit:
                                fee.gasEstimate.callGasLimit.toString(),
                            verificationGasLimit:
                                fee.gasEstimate.verificationGasLimit.toString(),
                            preVerificationGas:
                                fee.gasEstimate.preVerificationGas.toString(),
                        },
                        {
                            mode: 'ERC20',
                            calculateGasLimits: false,
                            tokenInfo: {
                                feeTokenAddress:
                                    fee.feeInTokenCurrency.currency.address,
                            },
                        },
                    ],
                },
            }).then((response) =>
                parsePaymasterAndData(response).getSuccessResultOrThrow(
                    'Failed to parse ERC20 paymaster and data response'
                )
            )

        case 'custom':
            throw new ImperativeError('Custom network not supported')
        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}

const getPaymasterAndData = async ({
    signal,
    userOperationRequest,
    initialUserOperation,
}: {
    userOperationRequest: SafeTransactionRequest
    initialUserOperation: InitialUserOperation
    signal?: AbortSignal
}): Promise<Hex.Hexadecimal> => {
    const { fee } = userOperationRequest.form

    switch (fee.type) {
        case 'non_sponsored_user_operation_fee':
            switch (fee.selectedFee.type) {
                case 'erc20_gas_abstraction_transaction_fee':
                    return (await fetchERC20PaymasterAndDataV2({
                        signal,
                        initialUserOperation,
                        network: userOperationRequest.network,
                        nonce: Hex.fromBigInt(userOperationRequest.nonce),
                        fee: fee.selectedFee,
                    })) as Hex.Hexadecimal
                case 'native_gas_abstraction_transaction_fee':
                    return '0x' as Hex.Hexadecimal
                default:
                    return notReachable(fee.selectedFee)
            }
        case 'sponsored_user_operation_fee':
            return '0x' as Hex.Hexadecimal
        default:
            return notReachable(fee)
    }
}

const getSignature = async ({
    userOperationHash,
    network,
    safeInstance,
    sessionPassword,
    keyStore,
}: {
    network: Network
    safeInstance: SafeInstance
    sessionPassword: string
    userOperationHash: UserOperationHash
    keyStore: Safe4337
}): Promise<Hex.Hexadecimal> => {
    const { safeDeplymentConfig, localSignerKeyStore } = keyStore

    switch (safeInstance.type) {
        case 'deployed':
            if (safeInstance.owners.includes(localSignerKeyStore.address)) {
                const signature = await signUserOperationHashWithLocalSigner({
                    keyStore,
                    network,
                    sessionPassword,
                    userOperationHash,
                    dApp: null,
                })

                return signature as Hex.Hexadecimal
            }

            return signUserOperationHashWithPassKey({
                passkey: safeDeplymentConfig.passkeyOwner,
                userOperationHash,
                sessionPassword,
            })
        case 'not_deployed':
            return signUserOperationHashWithPassKey({
                passkey: safeDeplymentConfig.passkeyOwner,
                userOperationHash,
                sessionPassword,
            })
        default:
            return notReachable(safeInstance)
    }
}
