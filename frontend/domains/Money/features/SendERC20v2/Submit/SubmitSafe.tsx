import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { Actions } from '@zeal/uikit/Actions'
import { Button, CompressedButton } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Screen } from '@zeal/uikit/Screen'

import { noop, notReachable } from '@zeal/toolkit'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import { AppErrorBanner } from '@zeal/domains/Error/components/AppErrorBanner'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStore } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkHexId, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { SubmittedUserOperationPending } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import { PendingSendTransactionActivity } from '@zeal/domains/Transactions'
import {
    GasPaymentMethod,
    keystoreToUserEventType,
    RPCType,
} from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'
import { UserOperationFee } from '@zeal/domains/UserOperation'
import { submitUserOperationToBundler } from '@zeal/domains/UserOperation/api/submitUserOperationToBundler'

import { FormLayout } from './FormLayout'

import { SafeTransactionRequest } from '../fetch'
import { SafeFeeWidget } from '../SafeFeeWidget'
import { sign } from '../UserOperation'

type Props = {
    networkRPCMap: NetworkRPCMap
    installationId: string
    transactionRequest: SafeTransactionRequest
    sessionPassword: string
    accountsMap: AccountsMap
    networkMap: NetworkMap
    actionSource: ActionSource2
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'internal_send_transaction_closed' }
    | {
          type: 'internal_send_transaction_submitted'
          address: Web3.address.Address
          pendingSendTransactionActivity: PendingSendTransactionActivity
      }

const submit = async ({
    userOperationRequest,
    networkRPCMap,
    networkMap,
    sessionPassword,
    signal,
}: {
    userOperationRequest: SafeTransactionRequest
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    signal: AbortSignal
}): Promise<{
    submittedUserOperation: SubmittedUserOperationPending
    networkHexId: NetworkHexId
}> => {
    const signedRequest = await sign({
        userOperationRequest,
        sessionPassword,
        networkRPCMap,
        networkMap,
        signal,
    })

    const submittedUserOperation = await submitUserOperationToBundler({
        userOperationWithSignature: (() => {
            switch (signedRequest.type) {
                case 'boosted_user_operation_with_signature':
                    return signedRequest
                case 'user_operation_with_signature':
                    return {
                        type: 'user_operation_with_signature',
                        entrypointNonce: userOperationRequest.nonce,
                        sender: signedRequest.sender,
                        callData: signedRequest.callData,
                        entrypoint: signedRequest.entrypoint,
                        signature: signedRequest.signature,
                        initCode: signedRequest.initCode,
                        maxFeePerGas: signedRequest.maxFeePerGas,
                        maxPriorityFeePerGas:
                            signedRequest.maxPriorityFeePerGas,
                        callGasLimit: Hexadecimal.toBigInt(
                            signedRequest.callGasLimit
                        ),
                        verificationGasLimit:
                            signedRequest.verificationGasLimit,
                        preVerificationGas: signedRequest.preVerificationGas,
                        paymasterAndData: signedRequest.paymasterAndData,
                    }
                default:
                    return notReachable(signedRequest)
            }
        })(),
        network: userOperationRequest.network,
        networkRPCMap,
        actionSource: {
            type: 'internal',
            transactionEventSource: 'send',
        },
    })

    return {
        submittedUserOperation,
        networkHexId: userOperationRequest.network.hexChainId,
    }
}

export const SubmitSafe = ({
    transactionRequest,
    installationId,
    sessionPassword,
    networkRPCMap,
    networkMap,
    accountsMap,
    actionSource,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(submit, {
        type: 'loading',
        params: {
            userOperationRequest: transactionRequest,
            installationId,
            sessionPassword,
            networkRPCMap,
            networkMap,
        },
    })

    const onMsgLive = useLiveRef(onMsg)

    const transactionRequestLive = useLiveRef(transactionRequest)

    useEffect(() => {
        const keystoreType = keystoreToUserEventType(
            transactionRequestLive.current.keyStore
        )
        const network = transactionRequestLive.current.network.hexChainId
        const source = actionSource.transactionEventSource
        const keystoreId = transactionRequestLive.current.keyStore.id
        switch (loadable.type) {
            case 'loading':
                postUserEvent({
                    type: 'TransactionRequestedEvent',
                    installationId,
                    keystoreType,
                    network,
                    source,
                    keystoreId,
                    rpcType: getRpcType(
                        transactionRequestLive.current.keyStore,
                        installationId
                    ),
                })

                break
            case 'loaded':
                postUserEvent({
                    type: 'TransactionSubmittedEvent',
                    keystoreType,
                    installationId,
                    network,
                    source,
                    keystoreId,
                    gasPaymentMethod: getGasPaymentMethod(
                        transactionRequestLive.current.form.fee
                    ),
                })

                onMsgLive.current({
                    type: 'internal_send_transaction_submitted',
                    address:
                        transactionRequestLive.current.form.fromWallet.address,
                    pendingSendTransactionActivity: {
                        type: 'pending_send',
                        amount: transactionRequestLive.current.form.amount,
                        amountInDefaultCurrency:
                            transactionRequestLive.current.form
                                .amountInDefaultCurrency,
                        receiver: transactionRequestLive.current.form.toAddress,
                        networkHexId: loadable.data.networkHexId,
                        actionSource: {
                            type: 'internal',
                            transactionEventSource: 'send',
                        },
                        submittedUserOperation:
                            loadable.data.submittedUserOperation,
                    },
                })
                break
            case 'error':
                captureError(loadable.error)
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [
        actionSource.transactionEventSource,
        installationId,
        loadable,
        onMsgLive,
        transactionRequestLive,
    ])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return (
                <Screen
                    padding="form"
                    background="light"
                    onNavigateBack={() =>
                        onMsg({ type: 'internal_send_transaction_closed' })
                    }
                >
                    <Column spacing={16} shrink alignY="stretch">
                        <FormLayout
                            transactionRequest={transactionRequest}
                            accountsMap={accountsMap}
                            networkMap={networkMap}
                        />
                        <SafeFeeWidget
                            disabled
                            onMsg={noop}
                            fee={transactionRequest.form.fee}
                        />
                        <Actions variant="default">
                            <CompressedButton
                                disabled
                                size="regular"
                                variant="secondary"
                                onClick={noop}
                            >
                                {({ color, size }) => (
                                    <BackIcon size={size} color={color} />
                                )}
                            </CompressedButton>
                            <Button
                                size="regular"
                                variant="primary"
                                disabled
                                loading
                                onClick={noop}
                            />
                        </Actions>
                    </Column>
                </Screen>
            )
        case 'error':
            const error = parseAppError(loadable.error)
            return (
                <Screen
                    padding="form"
                    background="light"
                    onNavigateBack={() =>
                        onMsg({ type: 'internal_send_transaction_closed' })
                    }
                >
                    <Column spacing={16} shrink alignY="stretch">
                        <FormLayout
                            transactionRequest={transactionRequest}
                            accountsMap={accountsMap}
                            networkMap={networkMap}
                        />
                        <AppErrorBanner
                            error={error}
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'try_again_clicked':
                                        setLoadable({
                                            type: 'loading',
                                            params: loadable.params,
                                        })
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        notReachable(msg.type)
                                }
                            }}
                            installationId={installationId}
                        />
                        <Actions variant="default">
                            <Button
                                size="regular"
                                variant="secondary"
                                onClick={() => {
                                    onMsg({
                                        type: 'internal_send_transaction_closed',
                                    })
                                }}
                            >
                                <FormattedMessage
                                    id="actions.cancel"
                                    defaultMessage="Cancel"
                                />
                            </Button>
                            <Button
                                size="regular"
                                variant="secondary"
                                onClick={() => {
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                }}
                            >
                                <FormattedMessage
                                    id="actions.retry"
                                    defaultMessage="Retry"
                                />
                            </Button>
                        </Actions>
                    </Column>
                </Screen>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}

const getGasPaymentMethod = (fee: UserOperationFee): GasPaymentMethod => {
    switch (fee.type) {
        case 'sponsored_user_operation_fee':
            return { type: 'sponsored' }
        case 'non_sponsored_user_operation_fee':
            switch (fee.selectedFee.type) {
                case 'native_gas_abstraction_transaction_fee':
                    return { type: 'native' }
                case 'erc20_gas_abstraction_transaction_fee':
                    return {
                        type: 'erc20',
                        token: fee.selectedFee.feeInTokenCurrency.currency.code,
                    }
                default:
                    return notReachable(fee.selectedFee)
            }
            break
        default:
            return notReachable(fee)
    }
}
const getRpcType = (keyStore: KeyStore, _installationId: string): RPCType => {
    switch (keyStore.type) {
        case 'track_only':
        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
            return 'rpc'
        case 'safe_4337':
            return 'biconomyBundler'
        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}
