import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { ImperativeError } from '@zeal/toolkit/Error'
import { memoizeOne } from '@zeal/toolkit/Function/memoizeOne'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { values } from '@zeal/toolkit/Object'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { SAFE_4337_MODULE_ENTRYPOINT_ADDRESS } from '@zeal/domains/Address/constants'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    DefaultCurrency,
    GasCurrencyPresetMap,
    KnownCryptoCurrencies,
} from '@zeal/domains/Currency'
import { requestAllowance } from '@zeal/domains/Currency/api/fetchAllowance'
import { fetchStaticCurrencies } from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import {
    PAYMASTER_ADDRESS,
    PAYMASTER_MAP,
} from '@zeal/domains/Currency/constants'
import { FXRate2 } from '@zeal/domains/FXRate'
import { fetchCrossRates } from '@zeal/domains/FXRate/api/fetchCrossRates'
import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'
import { EOA, KeyStoreMap, Safe4337, TrackOnly } from '@zeal/domains/KeyStore'
import { requestSafeOwners } from '@zeal/domains/KeyStore/api/fetchSafeOwners'
import { SafeInstance } from '@zeal/domains/KeyStore/helpers/fetchSafe4337Instance'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { getSafeDeploymentInitCode } from '@zeal/domains/KeyStore/helpers/getSafeDeploymentInitCode'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { fetchServerPortfolioWithCache } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { fetchRPCBatch2 } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import {
    FeeForecastResponse,
    FeePresetMap,
} from '@zeal/domains/Transactions/domains/FeeForecast'
import { UserOperationFee } from '@zeal/domains/UserOperation'
import { requestCurrentEntrypointNonce2 } from '@zeal/domains/UserOperation/api/fetchCurrentEntrypointNonce'
import { fetchBiconomyBundlerFeeAndGasEstimates } from '@zeal/domains/UserOperation/api/fetchFeeAndGasEstimatesFromBundler'
import { fetchPimlicoGasEstimates } from '@zeal/domains/UserOperation/api/fetchGasEstimates'
import { getSignatureVGLBuffer } from '@zeal/domains/UserOperation/helpers/getSignatureVGLBuffer'

import {
    createInitialUserOperation,
    getFeeAndMaxBalance,
} from './UserOperation'
import { SEND_SPONSORED_ON_NETWORKS } from './UserOperation/constants'

export type Pollable = PollableData<Data, Params>

export type Form = {
    cryptoCurrency: CryptoCurrency
    amount: string | null
    toAddress: Web3.address.Address
    fromWallet: Account
    stage: 'edit' | 'confirm'
}

export type Params = {
    currencyHiddenMap: CurrencyHiddenMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    presetMap: FeePresetMap
    keyStoreMap: KeyStoreMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    installationId: string
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
    cacheKey: string
    form: Form
}

export type Data = {
    serverPortfolio: ServerPortfolio2
    fees:
        | {
              type: EOA['type']
              keystore: EOA
              fees: FeeForecastResponse
              maxBalance: CryptoMoney
          }
        | {
              type: Safe4337['type']
              keystore: Safe4337
              safeInstance: SafeInstance
              fee: UserOperationFee
              maxBalance: CryptoMoney
          }
        | {
              type: TrackOnly['type']
              keystore: TrackOnly
              maxBalance: CryptoMoney
          }

    nonce: bigint
}

export type ValidatedForm = {
    amount: CryptoMoney
    amountInDefaultCurrency: FiatMoney | null
    toAddress: Web3.address.Address
    fromWallet: Account
    fee: UserOperationFee
    maxBalance: CryptoMoney
}

type EOATransactionRequest = {
    type: 'eoa_transaction_request'
    form: ValidatedForm
}

export type SafeTransactionRequest = {
    type: 'safe_operation_request'
    network: Network
    form: ValidatedForm
    keyStore: Safe4337
    nonce: bigint
    safeInstance: SafeInstance
}

export type TransactionRequest = EOATransactionRequest | SafeTransactionRequest

type RPCDataParams = {
    isSponsored: boolean
    fromWallet: Account
    currencies: KnownCryptoCurrencies
    network: Network
    keystore: Safe4337
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}

const fetchRPCData = async ({
    fromWallet,
    isSponsored,
    network,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
    currencies,
    keystore,
    signal,
}: RPCDataParams): Promise<{
    safeInstance: SafeInstance
    crossRates: FXRate2<CryptoCurrency, CryptoCurrency>[]
    nonce: bigint
    allowances: CryptoMoney[]
    nativeToDefaultRate: FXRate2<CryptoCurrency, DefaultCurrency> | null
}> => {
    const fromAddress = fromWallet.address as Web3.address.Address

    const isSmartWalletSupportedNetwork = (() => {
        switch (network.smartWalletSupport.type) {
            case 'supported':
                return true
            case 'not_supported':
                return false
            default:
                return notReachable(network.smartWalletSupport)
        }
    })()

    const shouldRequestRatesAndAllowance =
        !isSponsored && isSmartWalletSupportedNetwork

    const [nativeToDefaultRate, crossRates, [nonce, owners, ...allowances]] =
        await Promise.all([
            !shouldRequestRatesAndAllowance
                ? null
                : fetchRate({
                      networkMap,
                      networkRPCMap,
                      cryptoCurrency: network.nativeCurrency,
                      defaultCurrencyConfig,
                      signal,
                  }),

            !shouldRequestRatesAndAllowance
                ? []
                : fetchCrossRates({
                      baseCurrency: network.nativeCurrency,
                      quoteCurrencies: PAYMASTER_MAP[network.hexChainId]
                          .filter(
                              (currencyId) =>
                                  currencyId !== network.nativeCurrency.id
                          )
                          .map((currencyId) => currencies[currencyId]),
                      networkMap,
                      networkRPCMap,
                      signal,
                  }).then((ratesMap) =>
                      values(ratesMap).filter(excludeNullValues)
                  ),

            fetchRPCBatch2(
                [
                    requestCurrentEntrypointNonce2({
                        entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
                        address: fromAddress,
                    }),
                    requestSafeOwners({ safeAddress: fromAddress }),

                    ...(!shouldRequestRatesAndAllowance
                        ? []
                        : PAYMASTER_MAP[network.hexChainId]
                              .filter(
                                  (currencyId) =>
                                      currencyId !== network.nativeCurrency.id
                              )
                              .map((currencyId) => {
                                  const currency = currencies[currencyId]
                                  return requestAllowance({
                                      currency,
                                      owner: fromWallet.address,
                                      spender: PAYMASTER_ADDRESS,
                                  })
                              })),
                ],
                { networkRPCMap, network, signal }
            ),
        ])
    const safeInstance: SafeInstance = owners
        ? {
              type: 'deployed',
              safeAddress: fromAddress,
              entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
              owners: owners,
          }
        : {
              type: 'not_deployed',
              entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
              safeAddress: fromAddress,
              deploymentInitCode: getSafeDeploymentInitCode(
                  keystore.safeDeplymentConfig
              ),
          }
    return {
        safeInstance,
        allowances,
        nativeToDefaultRate,
        crossRates,
        nonce,
    }
}

const fetchRPCDataWithCache = memoizeOne(
    ({
        cacheKey: _,
        signal: __,
        ...rest
    }: RPCDataParams & { cacheKey: string }) => fetchRPCData(rest),
    ({ cacheKey, fromWallet, network }) =>
        `${cacheKey}${fromWallet.address}${network.hexChainId}`
)

export const fetch = async ({
    form,
    defaultCurrencyConfig,
    networkMap,
    installationId,
    signal,
    networkRPCMap,
    keyStoreMap,
    gasCurrencyPresetMap,
    currencyHiddenMap,
    cacheKey,
}: Params): Promise<Data> => {
    const keystore = getKeyStore({
        address: form.fromWallet.address,
        keyStoreMap,
    })

    const network = findNetworkByHexChainId(
        form.cryptoCurrency.networkHexChainId,
        networkMap
    )

    const fromAddress = form.fromWallet.address
    switch (keystore.type) {
        case 'safe_4337':
            const nativeCurrency = network.nativeCurrency
            const currencies = await fetchStaticCurrencies()

            // dirty way to optize fetching
            const portfolioPromise = fetchServerPortfolioWithCache({
                variant: 'only_tokens' as const,
                address: fromAddress,
                defaultCurrencyConfig,
                networkMap,
                installationId,
                currencyHiddenMap,
                cacheKey,
                signal,
                networkRPCMap,
            })

            const isSponsored = SEND_SPONSORED_ON_NETWORKS.includes(
                network.hexChainId
            )

            const {
                crossRates,
                allowances,
                nonce,
                safeInstance,
                nativeToDefaultRate,
            } = await fetchRPCDataWithCache({
                isSponsored,
                fromWallet: form.fromWallet,
                cacheKey,
                defaultCurrencyConfig,
                network,
                networkMap,
                networkRPCMap,
                signal,
                keystore,
                currencies,
            })

            if (isSponsored) {
                const [serverPortfolio, gasEstimate] = await Promise.all([
                    portfolioPromise,
                    fetchPimlicoGasEstimates({
                        entrypoint: safeInstance.entrypoint,
                        network,
                        initialUserOperation: createInitialUserOperation({
                            network,
                            nonce,
                            safeInstance,
                            keystore,
                            form,
                        }),
                        verificationGasLimitBuffer: getSignatureVGLBuffer({
                            safeInstance,
                            keystore,
                        }),
                        callGasLimitBuffer: 0n,
                        actionSource: {
                            type: 'internal',
                            transactionEventSource: 'send',
                        },
                        signal,
                    }),
                ])

                const selectedTokenOnForm = serverPortfolio.tokens.find(
                    (token) =>
                        token.balance.currency.id === form.cryptoCurrency.id
                )

                return {
                    nonce,
                    serverPortfolio,
                    fees: {
                        type: 'safe_4337',
                        fee: {
                            type: 'sponsored_user_operation_fee',
                            gasEstimate,
                        },
                        keystore,
                        safeInstance,
                        maxBalance: selectedTokenOnForm
                            ? selectedTokenOnForm.balance
                            : {
                                  amount: 0n,
                                  currency: form.cryptoCurrency,
                              },
                    },
                }
            }

            const [serverPortfolio, estimate] = await Promise.all([
                portfolioPromise,
                fetchBiconomyBundlerFeeAndGasEstimates({
                    entrypoint: safeInstance.entrypoint,
                    network,
                    initialUserOperation: createInitialUserOperation({
                        network,
                        nonce,
                        safeInstance,
                        keystore,
                        form,
                    }),
                    callGasLimitBuffer: 0n,
                    actionSource: {
                        type: 'internal',
                        transactionEventSource: 'send',
                    },
                    signal,
                }),
            ])

            const { fee, maxBalance } = getFeeAndMaxBalance({
                portfolio: serverPortfolio,
                approvals: allowances,
                crossRates,
                network,
                safeInstance,
                keystore,
                gasCurrencyPresetMap,
                nativeCurrency,
                fromCurrency: form.cryptoCurrency,
                estimate,
                nativeToDefaultRate,
                currencies,
            })

            return {
                nonce,
                serverPortfolio,
                fees: {
                    type: 'safe_4337',
                    fee,
                    keystore,
                    safeInstance,
                    maxBalance,
                },
            }

        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
        case 'track_only':
            throw new ImperativeError('eoa not implemented yet')
        default:
            return notReachable(keystore)
    }
}
