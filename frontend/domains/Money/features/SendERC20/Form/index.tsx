import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    FiatCurrency,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { fetchFeeAdjustedBalance } from '@zeal/domains/Currency/api/fetchFeeAdjustedBalance'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { FXRate2 } from '@zeal/domains/FXRate'
import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'
import { KeyStore, KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import {
    DEFAULT_NETWORK_HEX_IDS_TO_SPONSOR,
    findNetworkByHexChainId,
} from '@zeal/domains/Network/constants'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'
import { createTransferEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/createERC20EthSendTransaction'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'
import { Form as FormType } from './validation'

type Props = {
    installationId: string
    initialForm: FormType
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    account: Account
    sessionPassword: string
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    cardConfig: CardConfig
    portfolio: ServerPortfolio2
    defaultCurrencyConfig: DefaultCurrencyConfig
    gasCurrencyPresetMap: GasCurrencyPresetMap
    isEthereumNetworkFeeWarningSeen: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof Layout>, { type: 'on_submit_form' | 'close' }>
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'track_wallet_clicked'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_account_create_request'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_address_scanned_and_add_label'
          }
      >

const fetchMaxBalance = async ({
    serverPortfolio,
    gasCurrencyPresetMap,
    keyStore,
    cryptoCurrency,
    from,
    signal,
    networkMap,
    defaultCurrencyConfig,
    networkRPCMap,
}: {
    from: Account
    cryptoCurrency: CryptoCurrency
    serverPortfolio: ServerPortfolio2
    keyStore: KeyStore
    gasCurrencyPresetMap: GasCurrencyPresetMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<CryptoMoney> => {
    const balance = getBalanceByCryptoCurrency2({
        currency: cryptoCurrency,
        serverPortfolio,
    })

    const network = findNetworkByHexChainId(
        cryptoCurrency.networkHexChainId,
        networkMap
    )

    const trx = createTransferEthSendTransaction({
        amount: balance,
        from: from.address,
        network,
        to: from.address, // Sending to self should be same gas
    })

    return fetchFeeAdjustedBalance({
        fromAddress: from.address,
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        fromCurrency: cryptoCurrency,
        gasCurrencyPresetMap,
        keyStore,
        network,
        networksToSponsor: DEFAULT_NETWORK_HEX_IDS_TO_SPONSOR,
        serverPortfolio,
        requests: [trx],
        actionSource: {
            type: 'internal',
            transactionEventSource: 'sendGasEstimation',
        },
        signal,
    })
}

const fetch = async ({
    form,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
    signal,
}: {
    form: FormType
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<FXRate2<CryptoCurrency, FiatCurrency> | null> =>
    fetchRate({
        cryptoCurrency: form.cryptoCurrency,
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        signal,
    })

export const Form = ({
    initialForm,
    networkMap,
    networkRPCMap,
    account,
    accountsMap,
    portfolioMap,
    keyStoreMap,
    customCurrencies,
    sessionPassword,
    cardConfig,
    currencyHiddenMap,
    currencyPinMap,
    installationId,
    portfolio,
    defaultCurrencyConfig,
    gasCurrencyPresetMap,
    isEthereumNetworkFeeWarningSeen,
    onMsg,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    const [pollable, setPollable] = useLoadedPollableData(
        fetch,
        {
            type: 'loaded' as const,
            params: {
                form: initialForm,
                networkMap,
                defaultCurrencyConfig,
                networkRPCMap,
            },
            data: getTokenByCryptoCurrency3({
                currency: initialForm.cryptoCurrency,
                serverPortfolio: portfolio,
            }).rate,
        },
        { stopIf: () => false, pollIntervalMilliseconds: 2000 }
    )

    const keyStore = getKeyStore({
        keyStoreMap,
        address: account.address,
    })

    const [maxBalanceLoadable, setMaxBalanceLoadable] = useLoadableData(
        fetchMaxBalance,
        {
            type: 'loading',
            params: {
                cryptoCurrency: pollable.params.form.cryptoCurrency,
                from: account,
                defaultCurrencyConfig,
                gasCurrencyPresetMap,
                keyStore,
                networkMap,
                networkRPCMap,
                serverPortfolio: portfolio,
            },
        }
    )

    useEffect(() => {
        setMaxBalanceLoadable((old) => ({
            ...old,
            type: 'loading',
            params: {
                ...old.params,
                serverPortfolio: portfolio,
                cryptoCurrency: pollable.params.form.cryptoCurrency,
            },
        }))
    }, [pollable.params.form.cryptoCurrency, portfolio, setMaxBalanceLoadable])

    useEffect(() => {
        switch (maxBalanceLoadable.type) {
            case 'error':
                captureError(maxBalanceLoadable.error, {
                    extra: {
                        context: 'maxButton balance correction on senderc20',
                    },
                })
                break

            case 'loaded':
            case 'loading':
                break

            /* istanbul ignore next */
            default:
                notReachable(maxBalanceLoadable)
        }
    }, [maxBalanceLoadable])

    return (
        <>
            <Layout
                installationId={installationId}
                maxBalanceLoadable={maxBalanceLoadable}
                portfolio={portfolio}
                pollable={pollable}
                accountsMap={accountsMap}
                networkMap={networkMap}
                account={account}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_submit_form':
                            onMsg(msg)
                            break
                        case 'on_select_token':
                            setModal({ type: 'select_token' })
                            break
                        case 'on_select_to_address':
                            setModal({ type: 'select_to_address' })
                            break
                        case 'on_amount_change':
                            setPollable({
                                type: 'loaded',
                                data: pollable.data,
                                params: {
                                    ...pollable.params,
                                    form: {
                                        ...pollable.params.form,
                                        amount: msg.amount,
                                    },
                                },
                            })
                            break

                        case 'on_amount_in_default_currency_change':
                            setPollable({
                                type: 'loaded',
                                data: pollable.data,
                                params: {
                                    form: {
                                        type: 'amount_in_default_currency',
                                        amount: msg.amount,
                                        cryptoCurrency:
                                            pollable.params.form.cryptoCurrency,
                                        toAddress:
                                            pollable.params.form.toAddress,
                                    },
                                    networkMap,
                                    networkRPCMap,
                                    defaultCurrencyConfig,
                                },
                            })
                            break

                        case 'on_crypto_amount_change':
                            setPollable({
                                type: 'loaded',
                                data: pollable.data,
                                params: {
                                    form: {
                                        type: 'amount_in_crypto',
                                        amount: msg.amount,
                                        cryptoCurrency:
                                            pollable.params.form.cryptoCurrency,
                                        toAddress:
                                            pollable.params.form.toAddress,
                                    },
                                    networkMap,
                                    networkRPCMap,
                                    defaultCurrencyConfig,
                                },
                            })
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                defaultCurrencyConfig={defaultCurrencyConfig}
                installationId={installationId}
                portfolio={portfolio}
                currencyHiddenMap={currencyHiddenMap}
                cardConfig={cardConfig}
                currencyPinMap={currencyPinMap}
                portfolioMap={portfolioMap}
                keyStoreMap={keyStoreMap}
                customCurrencies={customCurrencies}
                sessionPassword={sessionPassword}
                accountsMap={accountsMap}
                state={modal}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                account={account}
                selectedCurrency={pollable.params.form.cryptoCurrency}
                toAddress={pollable.params.form.toAddress}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_accounts_create_success_animation_finished':
                        case 'track_wallet_clicked':
                        case 'add_wallet_clicked':
                        case 'hardware_wallet_clicked':
                            setModal({ type: 'closed' })
                            onMsg(msg)
                            break
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                            onMsg(msg)
                            break

                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_currency_selected':
                            setModal({ type: 'closed' })
                            setPollable({
                                type: 'reloading',
                                data: null,
                                params: {
                                    networkMap,
                                    networkRPCMap,
                                    form: {
                                        ...pollable.params.form,
                                        type: 'amount_in_crypto',
                                        cryptoCurrency: msg.currency,
                                        amount: null,
                                    },
                                    defaultCurrencyConfig,
                                },
                            })
                            break

                        case 'on_add_label_skipped':
                            setModal({ type: 'closed' })
                            setPollable({
                                type: 'loaded',
                                data: pollable.data,
                                params: {
                                    ...pollable.params,
                                    form: {
                                        ...pollable.params.form,
                                        toAddress: msg.address,
                                    },
                                },
                            })
                            break
                        case 'on_address_scanned':
                            setModal({ type: 'closed' })
                            setPollable({
                                type: 'loaded',
                                data: pollable.data,
                                params: {
                                    ...pollable.params,
                                    form: {
                                        ...pollable.params.form,
                                        toAddress: msg.address,
                                    },
                                },
                            })
                            break
                        case 'account_item_clicked':
                            setModal({ type: 'closed' })
                            setPollable({
                                type: 'loaded',
                                data: pollable.data,
                                params: {
                                    ...pollable.params,
                                    form: {
                                        ...pollable.params.form,
                                        toAddress: msg.account.address,
                                    },
                                },
                            })
                            break
                        case 'on_address_scanned_and_add_label':
                        case 'on_add_label_to_track_only_account_during_send':
                        case 'on_account_create_request':
                            setModal({ type: 'closed' })
                            setPollable({
                                type: 'loaded',
                                data: pollable.data,
                                params: {
                                    ...pollable.params,
                                    form: {
                                        ...pollable.params.form,
                                        toAddress:
                                            msg.accountsWithKeystores[0].account
                                                .address,
                                    },
                                },
                            })
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
