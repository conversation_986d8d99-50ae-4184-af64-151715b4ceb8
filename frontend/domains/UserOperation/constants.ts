import { notReachable } from '@zeal/toolkit'
import { getEnvironment } from '@zeal/toolkit/Environment'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import * as Web3 from '@zeal/toolkit/Web3'

import { Passkey } from '@zeal/domains/KeyStore/domains/Passkey'
import { NetworkHexId } from '@zeal/domains/Network'
import {
    ARBITRUM,
    AVALANCHE,
    BASE,
    BLAST,
    BSC,
    ETHEREUM,
    GNOSIS,
    LINEA,
    MANTLE,
    OPTIMISM,
    POLYGON,
    POLYGON_ZKEVM,
} from '@zeal/domains/Network/constants'

export const EOA_SIGNATURE_VERIFICATION_GAS_LIMIT_BUFFER = 10_000n
export const APPROVAL_CALL_GAS_LIMIT_BUFFER = 55_000n
export const TOKEN_PAYMASTER_VERIFICATION_GAS_LIMIT_BUFFER = 80_000n

export const DUMMY_EOA_SIGNATURE: Hexadecimal.Hexadecimal =
    '0x000065e98695000065e988edee0086e2069f385dad41712e0fc5fe77a4708c389625bee48c13baf309abe22d6d15c37281a22c060ddda326e2ad8b0a79b480cff0caae9da3ff1884407b13701f'

export const DUMMY_PASSKEY_SIGNATURE: Hexadecimal.Hexadecimal =
    '0x000065e82bf3000065e82e4b000000000000000000000000c52dd40051c9f39b4410b39a6ee74ac5452041c700000000000000000000000000000000000000000000000000000000000000410000000000000000000000000000000000000000000000000000000000000001e000000000000000000000000000000000000000000000000000000000000000a0000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000244daeb748c0107d0a044eda66ada5117fd7883b3add890c1c0ac0c78438856ed8b529c0854b799b989dd2a8594e0bab4e99b85c0056b2db918626df28ce107c7b0000000000000000000000000000000000000000000000000000000000000025da65b32028a48ea134b3044ebead8ef7c06c45e960870e8068337930b436a92e190000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000a47b2274797065223a22776562617574686e2e676574222c226368616c6c656e6765223a225a6343556c357a494547744b76394935686d666b36317575677571522d5f6e52566530306e3946566b5459222c226f726967696e223a226368726f6d652d657874656e73696f6e3a2f2f6865616d6e6a626e666c63696b6367676f69706c6962666f6d6d66626b6a706a222c2263726f73734f726967696e223a66616c73657d00000000000000000000000000000000000000000000000000000000'

export const PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA = null
export const PIMLICO_BOOSTED_USER_OP_PVG = 0n
export const PIMLICO_BOOSTED_USER_OPERATION_MAX_FEE = 0n

export const USER_OPERATION_EVENT_SIGNATURE =
    '0x49628fd1471006c1482da88028e9ce4dbb080b815c9b0344d39e5a8e6ec1419f'

export const PAYMASTERS_FEE_RECEIVERS: Record<
    NetworkHexId,
    Web3.address.Address
> = {
    // ******************************************

    [ETHEREUM.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [POLYGON.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [LINEA.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [BSC.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [AVALANCHE.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [ARBITRUM.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [OPTIMISM.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [BASE.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [MANTLE.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),

    // 0x00000f7365cA6C59A2C93719ad53d567ed49c14C

    [BLAST.hexChainId]: Web3.address.staticFromString(
        '0x00000f7365cA6C59A2C93719ad53d567ed49c14C'
    ),
    [POLYGON_ZKEVM.hexChainId]: Web3.address.staticFromString(
        '0x00000f7365cA6C59A2C93719ad53d567ed49c14C'
    ),
    [GNOSIS.hexChainId]: Web3.address.staticFromString(
        '0x00000f7365cA6C59A2C93719ad53d567ed49c14C'
    ),
}

export const ENTRYPOINT_ABI = [
    {
        inputs: [
            { internalType: 'address', name: 'sender', type: 'address' },
            {
                internalType: 'uint192',
                name: 'key',
                type: 'uint192',
            },
        ],
        name: 'getNonce',
        outputs: [{ internalType: 'uint256', name: 'nonce', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
] as const

export const SAFE_4337_MODULE_ABI = [
    {
        inputs: [
            {
                components: [
                    {
                        internalType: 'address',
                        name: 'sender',
                        type: 'address',
                    },
                    {
                        internalType: 'uint256',
                        name: 'nonce',
                        type: 'uint256',
                    },
                    {
                        internalType: 'bytes',
                        name: 'initCode',
                        type: 'bytes',
                    },
                    {
                        internalType: 'bytes',
                        name: 'callData',
                        type: 'bytes',
                    },
                    {
                        internalType: 'uint256',
                        name: 'callGasLimit',
                        type: 'uint256',
                    },
                    {
                        internalType: 'uint256',
                        name: 'verificationGasLimit',
                        type: 'uint256',
                    },
                    {
                        internalType: 'uint256',
                        name: 'preVerificationGas',
                        type: 'uint256',
                    },
                    {
                        internalType: 'uint256',
                        name: 'maxFeePerGas',
                        type: 'uint256',
                    },
                    {
                        internalType: 'uint256',
                        name: 'maxPriorityFeePerGas',
                        type: 'uint256',
                    },
                    {
                        internalType: 'bytes',
                        name: 'paymasterAndData',
                        type: 'bytes',
                    },
                    {
                        internalType: 'bytes',
                        name: 'signature',
                        type: 'bytes',
                    },
                ],
                internalType: 'struct UserOperation',
                name: 'userOp',
                type: 'tuple',
            },
        ],
        name: 'getOperationHash',
        outputs: [
            {
                internalType: 'bytes32',
                name: 'operationHash',
                type: 'bytes32',
            },
        ],
        stateMutability: 'view',
        type: 'function',
    },
] as const

export const SAFE_ABI = [
    {
        inputs: [
            {
                internalType: 'address',
                name: 'owner',
                type: 'address',
            },
            {
                internalType: 'uint256',
                name: '_threshold',
                type: 'uint256',
            },
        ],
        name: 'addOwnerWithThreshold',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'uint256', name: 'value', type: 'uint256' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            { internalType: 'uint8', name: 'operation', type: 'uint8' },
        ],
        name: 'executeUserOp',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'uint256', name: 'value', type: 'uint256' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            {
                internalType: 'enum Enum.Operation',
                name: 'operation',
                type: 'uint8',
            },
            { internalType: 'uint256', name: 'safeTxGas', type: 'uint256' },
            { internalType: 'uint256', name: 'baseGas', type: 'uint256' },
            { internalType: 'uint256', name: 'gasPrice', type: 'uint256' },
            { internalType: 'address', name: 'gasToken', type: 'address' },
            {
                internalType: 'address payable',
                name: 'refundReceiver',
                type: 'address',
            },
            { internalType: 'bytes', name: 'signatures', type: 'bytes' },
        ],
        name: 'execTransaction',
        outputs: [{ internalType: 'bool', name: 'success', type: 'bool' }],
        stateMutability: 'payable',
        type: 'function',
    },
    {
        inputs: [],
        name: 'nonce',
        outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'uint256', name: 'value', type: 'uint256' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            {
                internalType: 'enum Enum.Operation',
                name: 'operation',
                type: 'uint8',
            },
            { internalType: 'uint256', name: 'safeTxGas', type: 'uint256' },
            { internalType: 'uint256', name: 'baseGas', type: 'uint256' },
            { internalType: 'uint256', name: 'gasPrice', type: 'uint256' },
            { internalType: 'address', name: 'gasToken', type: 'address' },
            {
                internalType: 'address',
                name: 'refundReceiver',
                type: 'address',
            },
            { internalType: 'uint256', name: '_nonce', type: 'uint256' },
        ],
        name: 'getTransactionHash',
        outputs: [{ internalType: 'bytes32', name: '', type: 'bytes32' }],
        stateMutability: 'view',
        type: 'function',
    },
] as const

export const passkeySignerGasBufferConfig: Record<
    Passkey['signerVersion'],
    { verificationGasLimitBuffer: bigint }
> = {
    v1: {
        verificationGasLimitBuffer: 500_000n,
    },
    v2: {
        verificationGasLimitBuffer: (() => {
            const environment = getEnvironment()
            const platformVersion = `${ZealPlatform.OS}_${environment}` as const
            // FIXME :: @Kat - lower these values accordingly after new version has been adopted
            switch (platformVersion) {
                case 'web_production':
                case 'ios_production':
                case 'android_production':
                case 'ios_local':
                case 'ios_development':
                    return 1_000_000n
                case 'android_local':
                case 'android_development':
                case 'web_local':
                case 'web_development':
                    return 1_200_000n
                /* istanbul ignore next */
                default:
                    return notReachable(platformVersion)
            }
        })(),
    },
}
