import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

export type InitialUserOperation = {
    type: 'initial_user_operation'
    sender: Web3.address.Address
    callData: Hexadecimal.Hexadecimal
    nonce: bigint
    entrypoint: Web3.address.Address

    initCode: Hexadecimal.Hexadecimal | null // if we need to deploy, this going to be set to something
}

export type UserOperationHash = {
    type: 'user_operation_hash'

    encodedValidFrom: Hexadecimal.Hexadecimal
    encodedValidUntil: Hexadecimal.Hexadecimal
    hash: Hexadecimal.Hexadecimal
}

export type UserOperationWithSignature = {
    type: 'user_operation_with_signature'
    sender: Web3.address.Address
    callData: Hexadecimal.Hexadecimal
    entrypointNonce: bigint
    entrypoint: Web3.address.Address

    signature: Hexadecimal.Hexadecimal

    initCode: Hexadecimal.Hexadecimal | null

    maxFeePerGas: bigint
    maxPriorityFeePerGas: bigint

    callGasLimit: bigint
    verificationGasLimit: bigint
    preVerificationGas: bigint

    paymasterAndData: Hexadecimal.Hexadecimal | null
}

export type UserOperationWithoutSignature = {
    type: 'user_operation_without_signature'
    sender: Web3.address.Address
    callData: Hexadecimal.Hexadecimal
    entrypointNonce: bigint
    entrypoint: Web3.address.Address

    initCode: Hexadecimal.Hexadecimal | null

    maxFeePerGas: bigint
    maxPriorityFeePerGas: bigint

    callGasLimit: bigint // if we need to deploy we need to add here passkeysigner overhead
    verificationGasLimit: bigint
    preVerificationGas: bigint

    paymasterAndData: Hexadecimal.Hexadecimal | null
}

export type BoostedUserOperationWithoutSignature = {
    type: 'boosted_user_operation_without_signature'
    sender: Web3.address.Address
    callData: Hexadecimal.Hexadecimal

    entrypointNonce: bigint
    entrypoint: Web3.address.Address

    initCode: Hexadecimal.Hexadecimal | null

    callGasLimit: bigint
    verificationGasLimit: bigint
}

export type BoostedUserOperationWithSignature = {
    type: 'boosted_user_operation_with_signature'
    sender: Web3.address.Address
    callData: Hexadecimal.Hexadecimal
    entrypointNonce: bigint
    entrypoint: Web3.address.Address

    initCode: Hexadecimal.Hexadecimal | null

    callGasLimit: bigint
    verificationGasLimit: bigint

    signature: Hexadecimal.Hexadecimal
}
