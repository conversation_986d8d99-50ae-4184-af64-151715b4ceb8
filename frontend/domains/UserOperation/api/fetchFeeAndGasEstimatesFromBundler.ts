import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hex from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import { bigint, object, Result, shape } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { ActionSource2 } from '@zeal/domains/Main'
import { Network } from '@zeal/domains/Network'
import {
    fetchBiconomyBundlerResponseWithRetries,
    fetchPimlicoBundlerResponseWithRetries,
} from '@zeal/domains/UserOperation/api/fetchBundlerResponse'

import { BundlerGasEstimate, FeeAndGasEstimates } from '../GasEstimate'

const parseGasEstimates = (
    input: unknown
): Result<unknown, BundlerGasEstimate> =>
    object(input).andThen((obj) =>
        shape({
            preVerificationGas: bigint(obj.preVerificationGas),
            callGasLimit: bigint(obj.callGasLimit),
            verificationGasLimit: bigint(obj.verificationGasLimit),
        })
    )

const parse = (input: unknown): Result<unknown, FeeAndGasEstimates> =>
    object(input).andThen((obj) =>
        shape({
            gasEstimate: parseGasEstimates(obj),
            gasPrice: shape({
                maxFeePerGas: bigint(obj.maxFeePerGas),
                maxPriorityFeePerGas: bigint(obj.maxPriorityFeePerGas),
            }),
        })
    )

export const fetchBiconomyBundlerFeeAndGasEstimates = async ({
    network,
    initialUserOperation,
    actionSource,
    callGasLimitBuffer,
    entrypoint,
    signal,
}: {
    initialUserOperation: {
        sender: Web3.address.Address
        nonce: bigint
        initCode: Hex.Hexadecimal | null
        callData: Hex.Hexadecimal
        paymasterAndData: Hex.Hexadecimal | null
        signature: Hex.Hexadecimal
    }
    entrypoint: Web3.address.Address
    network: Network
    callGasLimitBuffer: bigint
    actionSource: ActionSource2
    signal?: AbortSignal
}): Promise<FeeAndGasEstimates> => {
    // TODO :: @Nicvaniek create narrowed network type for chains that support gas abstraction
    switch (network.type) {
        case 'predefined':
        case 'testnet': {
            const gasEstimateResponse =
                await fetchBiconomyBundlerResponseWithRetries({
                    request: {
                        id: generateRandomNumber(),
                        jsonrpc: '2.0',
                        method: 'eth_estimateUserOperationGas',
                        params: [
                            {
                                callData: initialUserOperation.callData,
                                initCode: initialUserOperation.initCode || '0x',
                                nonce: Hex.fromBigInt(
                                    initialUserOperation.nonce
                                ),
                                sender: initialUserOperation.sender,
                                signature: initialUserOperation.signature,
                                paymasterAndData:
                                    initialUserOperation.paymasterAndData ||
                                    '0x',
                            },
                            entrypoint,
                        ],
                    },
                    network,
                    actionSource,
                    signal,
                }).then((response) =>
                    parse(response).getSuccessResultOrThrow(
                        'Failed to parse gas estimate response'
                    )
                )

            return {
                ...gasEstimateResponse,
                gasEstimate: {
                    ...gasEstimateResponse.gasEstimate,
                    callGasLimit:
                        gasEstimateResponse.gasEstimate.callGasLimit +
                        callGasLimitBuffer,
                },
            }
        }
        case 'custom':
            throw new ImperativeError('Custom network not supported')
        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}

export const fetchPimlicoBundlerGasEstimates = async ({
    network,
    initialUserOperation,
    actionSource,
    entrypoint,
    signal,
}: {
    initialUserOperation: {
        sender: Web3.address.Address
        nonce: bigint
        initCode: Hex.Hexadecimal | null
        callData: Hex.Hexadecimal
        paymasterAndData: Hex.Hexadecimal | null
        signature: Hex.Hexadecimal
    }
    entrypoint: Web3.address.Address
    network: Network
    actionSource: ActionSource2
    signal?: AbortSignal
}): Promise<BundlerGasEstimate> => {
    switch (network.type) {
        case 'predefined':
        case 'testnet':
            return fetchPimlicoBundlerResponseWithRetries({
                request: {
                    id: generateRandomNumber(),
                    jsonrpc: '2.0',
                    method: 'eth_estimateUserOperationGas',
                    params: [
                        {
                            callData: initialUserOperation.callData,
                            initCode: initialUserOperation.initCode || '0x',
                            nonce: Hex.fromBigInt(initialUserOperation.nonce),
                            sender: initialUserOperation.sender,
                            signature: initialUserOperation.signature,
                            paymasterAndData:
                                initialUserOperation.paymasterAndData || '0x',
                        },
                        entrypoint,
                    ],
                },
                actionSource,
                network,
                signal,
            }).then((res) =>
                parseGasEstimates(res).getSuccessResultOrThrow(
                    'Failed to parse pimlico gas estimate response'
                )
            )
        case 'custom':
            throw new ImperativeError('Custom network not supported')
        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}
