import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import * as Web3 from '@zeal/toolkit/Web3'

import { SAFE_4337_MODULE_ENTRYPOINT_ADDRESS } from '@zeal/domains/Address/constants'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { SafeInstance } from '@zeal/domains/KeyStore/helpers/fetchSafe4337Instance'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'

import { fetchPaymasterResponse } from './fetchPaymasterResponse'

import { UserOperationFee2 } from '../GasAbstractionTransactionFee'
import { calculateUserOperationHash } from '../helpers/calculateUserOperationHash'
import { createAddOwnerMetaTransaction } from '../helpers/createAddOwnerMetaTransaction'
import { metaTransactionDatasToUserOperationCallData } from '../helpers/metaTransactionDatasToUserOperationCallData'
import { signUserOperationHashWithLocalSigner } from '../helpers/signUserOperationHashWithLocalSigner'
import { signUserOperationHashWithPassKey } from '../helpers/signUserOperationHashWithPassKey'
import { MetaTransactionData } from '../MetaTransactionData'
import {
    BoostedUserOperationWithSignature,
    UserOperationHash,
    UserOperationWithSignature,
} from '../UserOperation'

type InitialUserOperation = {
    sender: Web3.address.Address
    entrypointNonce: bigint
    initCode: Hexadecimal.Hexadecimal | null
    callData: Hexadecimal.Hexadecimal
}

const getSignature = async ({
    userOperationHash,
    network,
    safeInstance,
    sessionPassword,
    keyStore,
}: {
    network: Network
    safeInstance: SafeInstance
    sessionPassword: string
    userOperationHash: UserOperationHash
    keyStore: Safe4337
}): Promise<Hexadecimal.Hexadecimal> => {
    const { safeDeplymentConfig, localSignerKeyStore } = keyStore

    switch (safeInstance.type) {
        case 'deployed':
            if (safeInstance.owners.includes(localSignerKeyStore.address)) {
                return signUserOperationHashWithLocalSigner({
                    keyStore,
                    network,
                    sessionPassword,
                    userOperationHash,
                    dApp: null,
                })
            }

            return signUserOperationHashWithPassKey({
                passkey: safeDeplymentConfig.passkeyOwner,
                userOperationHash,
                sessionPassword,
            })
        case 'not_deployed':
            return signUserOperationHashWithPassKey({
                passkey: safeDeplymentConfig.passkeyOwner,
                userOperationHash,
                sessionPassword,
            })
        default:
            return notReachable(safeInstance)
    }
}

const fetchPaymasterAndData = async ({
    initialUserOperation,
    network,
    entrypointNonce,
    signal,
    fee,
}: {
    fee: UserOperationFee2
    network: Network
    entrypointNonce: bigint
    initialUserOperation: InitialUserOperation
    signal?: AbortSignal
}): Promise<Hexadecimal.Hexadecimal | null> => {
    switch (fee.type) {
        case 'non_sponsored_user_operation_fee':
            switch (fee.selectedFee.type) {
                case 'erc20_gas_abstraction_transaction_fee':
                case 'erc20_gas_abstraction_transaction_fee_with_paymaster_approval':
                    switch (network.type) {
                        // TODO :: @Nicvaniek create narrowed network type for chains that support gas abstraction
                        case 'predefined':
                        case 'testnet':
                            return fetchPaymasterResponse({
                                network,
                                signal,
                                request: {
                                    id: generateRandomNumber(),
                                    jsonrpc: '2.0',
                                    method: 'pm_sponsorUserOperation',
                                    params: [
                                        {
                                            sender: initialUserOperation.sender,
                                            initCode:
                                                initialUserOperation.initCode ||
                                                '0x',
                                            callData:
                                                initialUserOperation.callData,
                                            nonce: Hexadecimal.fromBigInt(
                                                entrypointNonce
                                            ),
                                            maxFeePerGas:
                                                Hexadecimal.fromBigInt(
                                                    fee.selectedFee.gasPrice
                                                        .maxFeePerGas
                                                ),
                                            maxPriorityFeePerGas:
                                                Hexadecimal.fromBigInt(
                                                    fee.selectedFee.gasPrice
                                                        .maxPriorityFeePerGas
                                                ),
                                            callGasLimit:
                                                fee.selectedFee.gasEstimate.callGasLimit.toString(),
                                            verificationGasLimit:
                                                fee.selectedFee.gasEstimate.verificationGasLimit.toString(),
                                            preVerificationGas:
                                                fee.selectedFee.gasEstimate.preVerificationGas.toString(),
                                        },
                                        {
                                            mode: 'ERC20',
                                            calculateGasLimits: false,
                                            tokenInfo: {
                                                feeTokenAddress:
                                                    fee.selectedFee
                                                        .feeInTokenCurrency
                                                        .currency.address,
                                            },
                                        },
                                    ],
                                },
                            }).then((response) =>
                                Hexadecimal.parse(
                                    response
                                ).getSuccessResultOrThrow(
                                    'Failed to parse ERC20 paymaster and data response'
                                )
                            )

                        case 'custom':
                            throw new ImperativeError(
                                'Custom network not supported when signing user operation'
                            )

                        /* istanbul ignore next */
                        default:
                            return notReachable(network)
                    }

                case 'native_gas_abstraction_transaction_fee':
                    return null

                default:
                    return notReachable(fee.selectedFee)
            }

        case 'sponsored_user_operation_fee':
            return null

        default:
            return notReachable(fee)
    }
}

const calculateAddOwnerMetaTransaction = ({
    safeInstance,
    keyStore,
}: {
    safeInstance: SafeInstance
    keyStore: Safe4337
}): MetaTransactionData | null => {
    const addOwner = createAddOwnerMetaTransaction({
        safeAddress: safeInstance.safeAddress,
        owner: keyStore.localSignerKeyStore.address,
        treshold: 1n,
    })

    switch (safeInstance.type) {
        case 'deployed':
            return safeInstance.owners.includes(
                keyStore.localSignerKeyStore.address
            )
                ? null
                : addOwner
        case 'not_deployed':
            return addOwner
        default:
            return notReachable(safeInstance)
    }
}

const calculateApprovalMetaTransaction = async ({
    fee,
    safeInstance,
    networkRPCMap,
    networkMap,
    signal,
}: {
    fee: UserOperationFee2
    safeInstance: SafeInstance
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<MetaTransactionData | null> => {
    switch (fee.type) {
        case 'sponsored_user_operation_fee':
            return null
        case 'non_sponsored_user_operation_fee':
            switch (fee.selectedFee.type) {
                case 'native_gas_abstraction_transaction_fee':
                case 'erc20_gas_abstraction_transaction_fee':
                    return null

                case 'erc20_gas_abstraction_transaction_fee_with_paymaster_approval':
                    return fee.selectedFee.approval

                default:
                    return notReachable(fee.selectedFee)
            }
        default:
            return notReachable(fee)
    }
}

export const sign = async ({
    keyStore,
    network,
    safeInstance,
    entrypointNonce,
    sessionPassword,
    networkRPCMap,
    networkMap,
    fee,
    metaTransactionDatas,
    signal,
}: {
    metaTransactionDatas: MetaTransactionData[]
    network: Network
    keyStore: Safe4337
    entrypointNonce: bigint
    fee: UserOperationFee2
    safeInstance: SafeInstance
    sessionPassword: string

    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<UserOperationWithSignature | BoostedUserOperationWithSignature> => {
    const addOwnerMetaTransaction = calculateAddOwnerMetaTransaction({
        safeInstance,
        keyStore,
    })

    const allowanceMetaTransaction: MetaTransactionData | null =
        await calculateApprovalMetaTransaction({
            fee,
            safeInstance,
            networkRPCMap,
            networkMap,
            signal,
        })

    const callData = metaTransactionDatasToUserOperationCallData({
        metaTransactionDatas: [
            addOwnerMetaTransaction,
            ...metaTransactionDatas,
            allowanceMetaTransaction,
        ].filter(excludeNullValues),
    })

    const initCode = (() => {
        switch (safeInstance.type) {
            case 'not_deployed':
                return safeInstance.deploymentInitCode
            case 'deployed':
                return null
            default:
                return notReachable(safeInstance)
        }
    })()

    const initialUserOperation: InitialUserOperation = {
        sender: safeInstance.safeAddress,
        callData,
        entrypointNonce,
        initCode,
    }

    const paymasterAndData = await fetchPaymasterAndData({
        signal,
        network,
        fee,
        entrypointNonce,
        initialUserOperation,
    })

    const userOperationWithoutSignature = (() => {
        switch (fee.type) {
            case 'non_sponsored_user_operation_fee':
                return {
                    type: 'user_operation_without_signature' as const,
                    sender: initialUserOperation.sender,
                    callData: initialUserOperation.callData,
                    entrypointNonce: initialUserOperation.entrypointNonce,
                    entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
                    initCode: initialUserOperation.initCode,
                    paymasterAndData,
                    maxFeePerGas: fee.selectedFee.gasPrice.maxFeePerGas,
                    maxPriorityFeePerGas:
                        fee.selectedFee.gasPrice.maxPriorityFeePerGas,
                    callGasLimit: fee.selectedFee.gasEstimate.callGasLimit,
                    verificationGasLimit:
                        fee.selectedFee.gasEstimate.verificationGasLimit,
                    preVerificationGas:
                        fee.selectedFee.gasEstimate.preVerificationGas,
                }
            case 'sponsored_user_operation_fee':
                return {
                    type: 'boosted_user_operation_without_signature' as const,
                    sender: initialUserOperation.sender,
                    callData: initialUserOperation.callData,
                    entrypointNonce: initialUserOperation.entrypointNonce,
                    entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
                    initCode: initialUserOperation.initCode,
                    callGasLimit: fee.gasEstimate.callGasLimit,
                    verificationGasLimit: fee.gasEstimate.verificationGasLimit,
                    preVerificationGas: fee.gasEstimate.preVerificationGas,
                }
            default:
                return notReachable(fee)
        }
    })()

    const userOperationHash = calculateUserOperationHash({
        network,
        userOperation: userOperationWithoutSignature,
        now: Date.now(),
    })

    const signature = await getSignature({
        userOperationHash,
        network,
        safeInstance,
        keyStore,
        sessionPassword,
    })

    switch (userOperationWithoutSignature.type) {
        case 'user_operation_without_signature':
            return {
                type: 'user_operation_with_signature',
                initCode: initialUserOperation.initCode,
                callData: initialUserOperation.callData,
                sender: initialUserOperation.sender,

                entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
                entrypointNonce: initialUserOperation.entrypointNonce,

                callGasLimit: userOperationWithoutSignature.callGasLimit,
                maxFeePerGas: userOperationWithoutSignature.maxFeePerGas,
                maxPriorityFeePerGas:
                    userOperationWithoutSignature.maxPriorityFeePerGas,
                preVerificationGas:
                    userOperationWithoutSignature.preVerificationGas,
                verificationGasLimit:
                    userOperationWithoutSignature.verificationGasLimit,
                paymasterAndData,
                signature,
            }
        case 'boosted_user_operation_without_signature':
            return {
                ...userOperationWithoutSignature,
                type: 'boosted_user_operation_with_signature',
                signature,
            }
        default:
            return notReachable(userOperationWithoutSignature)
    }
}
