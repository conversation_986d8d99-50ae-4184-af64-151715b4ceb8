import { generateRandomNumber } from '@zeal/toolkit/Number'
import { bigint } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { Network, NetworkRPCMap } from '@zeal/domains/Network'
import { EthCall } from '@zeal/domains/RPCRequest'
import {
    fetchRPCResponseWithRetry2,
    Request,
} from '@zeal/domains/RPCRequest/api/fetchRPCResponse'

import { ENTRYPOINT_ABI } from '../constants'

export const requestCurrentEntrypointNonce2 = ({
    entrypoint,
    address,
}: {
    entrypoint: Web3.address.Address
    address: Web3.address.Address
}): Request<bigint> => {
    const request: EthCall = {
        id: generateRandomNumber(),
        jsonrpc: '2.0',
        method: 'eth_call',
        params: [
            {
                to: entrypoint,
                data: Web3.abi.encodeFunctionData({
                    abi: ENTRYPOINT_ABI,
                    functionName: 'getNonce',
                    args: [address, 0n],
                }),
            },
            'latest',
        ],
    }
    return {
        request,
        parser: (response: unknown) =>
            bigint(response).getSuccessResultOrThrow(
                'Failed to parse current entrypoint nonce of sender'
            ),
    }
}
/**
 *  @deprecated use requestCurrentEntrypointNonce2 as part of batch
 **/
export const fetchCurrentEntrypointNonce = async ({
    entrypoint,
    address,
    network,
    networkRPCMap,
    signal,
}: {
    entrypoint: Web3.address.Address
    address: Web3.address.Address
    network: Network
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<bigint> =>
    fetchRPCResponseWithRetry2(
        requestCurrentEntrypointNonce2({ address, entrypoint }),
        { network, networkRPCMap, signal }
    )
