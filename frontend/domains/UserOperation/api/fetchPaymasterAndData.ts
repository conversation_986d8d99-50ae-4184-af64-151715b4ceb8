import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { fromBigInt } from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import { object, Result } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { Network } from '@zeal/domains/Network'
import { SimulatedUserOperationRequest } from '@zeal/domains/TransactionRequest'
import { ERC20GasAbstractionTransactionFee } from '@zeal/domains/UserOperation'

import { fetchPaymasterResponse } from './fetchPaymasterResponse'

import { UserOperationFee2 } from '../GasAbstractionTransactionFee'

export const parsePaymasterAndData = (
    input: unknown
): Result<unknown, Hexadecimal.Hexadecimal> =>
    object(input).andThen((obj) => Hexadecimal.parse(obj.paymasterAndData))

/**
 *
 * @deprecated please use fetchPaymasterAndData2
 */
export const fetchERC20PaymasterAndData = async ({
    userOperationRequest,
    selectedFee,
    nonce,
    network,
    signal,
}: {
    userOperationRequest: SimulatedUserOperationRequest
    nonce: bigint
    selectedFee: ERC20GasAbstractionTransactionFee
    network: Network
    installationId: string
    signal?: AbortSignal
}): Promise<Hexadecimal.Hexadecimal> => {
    switch (network.type) {
        // TODO :: @Nicvaniek create narrowed network type for chains that support gas abstraction
        case 'predefined':
        case 'testnet':
            return fetchPaymasterResponse({
                network,
                signal,
                request: {
                    id: generateRandomNumber(),
                    jsonrpc: '2.0',
                    method: 'pm_sponsorUserOperation',
                    params: [
                        {
                            sender: userOperationRequest.account.address,
                            nonce: fromBigInt(nonce),
                            initCode: userOperationRequest.initCode || '0x',
                            callData: selectedFee.callData,
                            maxFeePerGas: fromBigInt(
                                selectedFee.gasPrice.maxFeePerGas
                            ),
                            maxPriorityFeePerGas: fromBigInt(
                                selectedFee.gasPrice.maxPriorityFeePerGas
                            ),
                            callGasLimit:
                                selectedFee.gasEstimate.callGasLimit.toString(),
                            verificationGasLimit:
                                selectedFee.gasEstimate.verificationGasLimit.toString(),
                            preVerificationGas:
                                selectedFee.gasEstimate.preVerificationGas.toString(),
                        },
                        {
                            mode: 'ERC20',
                            calculateGasLimits: false,
                            tokenInfo: {
                                feeTokenAddress:
                                    selectedFee.feeInTokenCurrency.currency
                                        .address,
                            },
                        },
                    ],
                },
            }).then((response) =>
                parsePaymasterAndData(response).getSuccessResultOrThrow(
                    'Failed to parse ERC20 paymaster and data response'
                )
            )

        case 'custom':
            throw new ImperativeError('Custom network not supported')
        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}
export const fetchPaymasterAndData2 = async ({
    network,
    entrypointNonce,
    signal,
    fee,
    callData,
    sender,
    initCode,
}: {
    fee: UserOperationFee2
    network: Network
    entrypointNonce: bigint
    sender: Web3.address.Address
    initCode: Hexadecimal.Hexadecimal | null
    callData: Hexadecimal.Hexadecimal
    signal?: AbortSignal
}): Promise<Hexadecimal.Hexadecimal | null> => {
    switch (fee.type) {
        case 'non_sponsored_user_operation_fee':
            switch (fee.selectedFee.type) {
                case 'erc20_gas_abstraction_transaction_fee':
                case 'erc20_gas_abstraction_transaction_fee_with_paymaster_approval':
                    switch (network.type) {
                        // TODO :: @Nicvaniek create narrowed network type for chains that support gas abstraction
                        case 'predefined':
                        case 'testnet':
                            return fetchPaymasterResponse({
                                network,
                                signal,
                                request: {
                                    id: generateRandomNumber(),
                                    jsonrpc: '2.0',
                                    method: 'pm_sponsorUserOperation',
                                    params: [
                                        {
                                            sender: sender,
                                            initCode: initCode || '0x',
                                            callData: callData,
                                            nonce: Hexadecimal.fromBigInt(
                                                entrypointNonce
                                            ),
                                            maxFeePerGas:
                                                Hexadecimal.fromBigInt(
                                                    fee.selectedFee.gasPrice
                                                        .maxFeePerGas
                                                ),
                                            maxPriorityFeePerGas:
                                                Hexadecimal.fromBigInt(
                                                    fee.selectedFee.gasPrice
                                                        .maxPriorityFeePerGas
                                                ),
                                            callGasLimit:
                                                fee.selectedFee.gasEstimate.callGasLimit.toString(),
                                            verificationGasLimit:
                                                fee.selectedFee.gasEstimate.verificationGasLimit.toString(),
                                            preVerificationGas:
                                                fee.selectedFee.gasEstimate.preVerificationGas.toString(),
                                        },
                                        {
                                            mode: 'ERC20',
                                            calculateGasLimits: false,
                                            tokenInfo: {
                                                feeTokenAddress:
                                                    fee.selectedFee
                                                        .feeInTokenCurrency
                                                        .currency.address,
                                            },
                                        },
                                    ],
                                },
                            }).then((response) =>
                                Hexadecimal.parse(
                                    response
                                ).getSuccessResultOrThrow(
                                    'Failed to parse ERC20 paymaster and data response'
                                )
                            )

                        case 'custom':
                            throw new ImperativeError(
                                'Custom network not supported when signing user operation'
                            )

                        /* istanbul ignore next */
                        default:
                            return notReachable(network)
                    }

                case 'native_gas_abstraction_transaction_fee':
                    return null

                default:
                    return notReachable(fee.selectedFee)
            }

        case 'sponsored_user_operation_fee':
            return null

        default:
            return notReachable(fee)
    }
}
