import { notReachable } from '@zeal/toolkit'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { values } from '@zeal/toolkit/Object'
import * as Web3 from '@zeal/toolkit/Web3'

import { SAFE_4337_MODULE_ENTRYPOINT_ADDRESS } from '@zeal/domains/Address/constants'
import { CryptoCurrency, CurrencyId } from '@zeal/domains/Currency'
import { requestAllowance } from '@zeal/domains/Currency/api/fetchAllowance'
import { fetchCryptoCurrency2 } from '@zeal/domains/Currency/api/fetchCryptoCurrency2'
import {
    PAYMASTER_ADDRESS,
    PAYMASTER_MAP,
} from '@zeal/domains/Currency/constants'
import { createApprovalTransaction } from '@zeal/domains/Currency/helpers/createApprovalTransaction'
import { fetchCrossRates } from '@zeal/domains/FXRate/api/fetchCrossRates'
import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { ActionSource2 } from '@zeal/domains/Main'
import { CryptoMoney } from '@zeal/domains/Money'
import { isGreaterThan2 } from '@zeal/domains/Money/helpers/compare'
import { mulByNumber } from '@zeal/domains/Money/helpers/mul'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    fetchRPCBatch2,
    fetchRPCResponseWithRetry2,
} from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import {
    ERC20GasAbstractionTransactionFee,
    GasAbstractionTransactionFeeResponse,
} from '@zeal/domains/UserOperation'
import {
    fetchBiconomyGasEstimates,
    fetchPimlicoGasEstimates,
} from '@zeal/domains/UserOperation/api/fetchGasEstimates'
import { PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA } from '@zeal/domains/UserOperation/constants'

import {
    fetchCurrentEntrypointNonce,
    requestCurrentEntrypointNonce2,
} from './fetchCurrentEntrypointNonce'

import { ethSendTransactionToMetaTransactionData } from '../helpers/ethSendTransactionToMetaTransactionData'
import { metaTransactionDatasToUserOperationCallData } from '../helpers/metaTransactionDatasToUserOperationCallData'
import { MetaTransactionData } from '../MetaTransactionData'
import { InitialUserOperation } from '../UserOperation'

const APPROVAL_SOFT_CAP_BUFFER = 2 // If allowance is less than soft cap buffer we include approval for hard cap buffer
const APPROVAL_HARD_CAP_BUFFER = 5

export const calculateIfGasAbstractionCurrencyApprovalNeeded = ({
    allowance,
    feeAmount,
}: {
    allowance: CryptoMoney
    feeAmount: CryptoMoney
}):
    | { type: 'approval_required'; amount: CryptoMoney }
    | {
          type: 'approval_not_required'
      } => {
    const softCap = mulByNumber(feeAmount, APPROVAL_SOFT_CAP_BUFFER)
    const hardCap = mulByNumber(feeAmount, APPROVAL_HARD_CAP_BUFFER)

    if (isGreaterThan2(allowance, softCap)) {
        return {
            type: 'approval_not_required',
        }
    } else {
        return {
            type: 'approval_required',
            amount: hardCap,
        }
    }
}

const fetchAllowanceMap = async ({
    currencies,
    networkRPCMap,
    owner,
    spender,
    network,
    signal,
}: {
    currencies: CryptoCurrency[]
    owner: Web3.address.Address
    spender: Web3.address.Address
    networkRPCMap: NetworkRPCMap
    network: Network
    signal?: AbortSignal
}): Promise<Record<CurrencyId, CryptoMoney | null>> => {
    if (currencies.length === 0) {
        return {}
    }

    const allowanceResults = await fetchRPCBatch2(
        currencies.map((currency) =>
            requestAllowance({ currency, owner, spender })
        ),
        {
            networkRPCMap,
            network,
            signal,
        }
    )

    return allowanceResults.reduce(
        (map, allowance) => {
            map[allowance.currency.id] = allowance
            return map
        },
        {} as Record<CurrencyId, CryptoMoney | null>
    )
}

export const fetchGasAbstractionTransactionFees = async ({
    serverPortfolio,
    sponsored,
    network,
    initCode,
    metaTransactionDatas,
    sender,
    verificationGasLimitBuffer,
    callGasLimitBuffer,
    entrypoint,
    networkRPCMap,
    dummySignature,
    defaultCurrencyConfig,
    networkMap,
    actionSource,
    signal,
}: {
    sponsored: boolean
    serverPortfolio: ServerPortfolio2 | null
    metaTransactionDatas: MetaTransactionData[]
    initCode: Hexadecimal.Hexadecimal | null
    entrypoint: Web3.address.Address
    network: Network
    networkRPCMap: NetworkRPCMap
    verificationGasLimitBuffer: bigint
    callGasLimitBuffer: bigint
    sender: Web3.address.Address
    dummySignature: Hexadecimal.Hexadecimal
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    actionSource: ActionSource2
    signal?: AbortSignal
}): Promise<GasAbstractionTransactionFeeResponse> => {
    const callData = metaTransactionDatasToUserOperationCallData({
        metaTransactionDatas,
    })

    if (sponsored) {
        const nonce = await fetchRPCResponseWithRetry2(
            requestCurrentEntrypointNonce2({
                entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
                address: sender,
            }),
            { network, networkRPCMap, signal }
        )

        return {
            type: 'sponsored_transaction',
            fee: {
                type: 'sponsored_gas_abstraction_transaction_fee_2',
                callData,
                gasEstimate: await fetchPimlicoGasEstimates({
                    initialUserOperation: {
                        callData,
                        initCode: (initCode || '0x') as Hexadecimal.Hexadecimal,
                        nonce,
                        sender,
                        signature: dummySignature,
                        paymasterAndData:
                            PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA,
                    },
                    entrypoint,
                    actionSource,
                    network,
                    verificationGasLimitBuffer,
                    callGasLimitBuffer,
                    signal,
                }),
            },
        }
    }

    const nativeCurrency = network.nativeCurrency

    const [nonce, nativeToDefaultRate, currenciesMap] = await Promise.all([
        fetchCurrentEntrypointNonce({
            network,
            entrypoint,
            address: sender,
            networkRPCMap,
            signal,
        }),
        fetchRate({
            networkMap,
            networkRPCMap,
            cryptoCurrency: nativeCurrency,
            defaultCurrencyConfig,
            signal,
        }),
        fetchCryptoCurrency2({
            networkRPCMap,
            currencies: PAYMASTER_MAP[network.hexChainId],
        }),
    ])

    // TODO @resetko-zeal remove native currency from map ZEAL-3783
    const gasAbstractionCurrencies = values(currenciesMap).filter(
        (currency) => currency.id !== nativeCurrency.id
    )

    const initialUserOperation: InitialUserOperation = {
        type: 'initial_user_operation',
        callData,
        initCode,
        nonce,
        sender,
        entrypoint,
    }

    const gasCalculationResponse = await fetchBiconomyGasEstimates({
        initialUserOperation,
        network,
        verificationGasLimitBuffer,
        callGasLimitBuffer,
        dummySignature,
        actionSource,
        signal,
    })

    const {
        gasPrice,
        nonPaymasterGasEstimate,
        tokenPaymasterWithApprovalGasEstimate,
        tokenPaymasterWithoutApprovalGasEstimate,
    } = gasCalculationResponse

    const feeInNativeTokenCurrency: CryptoMoney = {
        currency: nativeCurrency,
        amount: nonPaymasterGasEstimate.totalGas * gasPrice.maxFeePerGas,
    }

    const feeInDefaultCurrency = nativeToDefaultRate
        ? applyRate2({
              baseAmount: feeInNativeTokenCurrency,
              rate: nativeToDefaultRate,
          })
        : null

    const nativeCurrencyOption = {
        type: 'native_gas_abstraction_transaction_fee' as const,
        feeInTokenCurrency: feeInNativeTokenCurrency,
        feeInDefaultCurrency,
        gasPrice,
        gasEstimate: {
            callGasLimit: nonPaymasterGasEstimate.callGasLimit,
            verificationGasLimit: nonPaymasterGasEstimate.verificationGasLimit,
            preVerificationGas: nonPaymasterGasEstimate.preVerificationGas,
        },
        callData,
    }

    const crossRatesMap = await fetchCrossRates({
        baseCurrency: nativeCurrency,
        quoteCurrencies: gasAbstractionCurrencies,
        networkMap,
        networkRPCMap,
        signal,
    })

    const portfolioCurrencyIds = new Set<CurrencyId>(
        serverPortfolio?.tokens.map((t) => t.balance.currency.id) ?? []
    )

    const currenciesInPortfolio = gasAbstractionCurrencies.filter((currency) =>
        portfolioCurrencyIds.has(currency.id)
    )

    const allowanceMap = await fetchAllowanceMap({
        currencies: currenciesInPortfolio,
        networkRPCMap,
        owner: sender,
        spender: PAYMASTER_ADDRESS,
        network,
        signal,
    })

    const erc20Options: (ERC20GasAbstractionTransactionFee | null)[] =
        await Promise.all(
            gasAbstractionCurrencies.map(async (gasAbstractionCurrency) => {
                const crossRate = crossRatesMap[gasAbstractionCurrency.id]

                if (!crossRate) {
                    return null
                }

                const nonApprovalFeeInNativeToken: CryptoMoney = {
                    currency: gasAbstractionCurrency,
                    amount:
                        tokenPaymasterWithoutApprovalGasEstimate.totalGas *
                        gasPrice.maxFeePerGas,
                }

                const nonApprovalFeeInUSD = nativeToDefaultRate
                    ? applyRate2({
                          baseAmount: nonApprovalFeeInNativeToken,
                          rate: nativeToDefaultRate,
                      })
                    : null

                const nonApprovalFeeInERC20 = applyRate2({
                    baseAmount: nonApprovalFeeInNativeToken,
                    rate: crossRate,
                })

                const erc20OptionWithoutApproval = {
                    type: 'erc20_gas_abstraction_transaction_fee' as const,
                    feeInTokenCurrency: nonApprovalFeeInERC20,
                    feeInDefaultCurrency: nonApprovalFeeInUSD,
                    gasPrice,
                    gasEstimate: tokenPaymasterWithoutApprovalGasEstimate,
                    callData,
                }

                const isInPortfolio = portfolioCurrencyIds.has(
                    gasAbstractionCurrency.id
                )

                if (!isInPortfolio) {
                    // if you don't have token you cannot pay in it anyway so we will show min amount without additional network call
                    return erc20OptionWithoutApproval
                }

                const allowance = allowanceMap[gasAbstractionCurrency.id]

                if (!allowance) {
                    return erc20OptionWithoutApproval
                }

                const approvalNeeded =
                    calculateIfGasAbstractionCurrencyApprovalNeeded({
                        allowance,
                        feeAmount: nonApprovalFeeInERC20,
                    })

                switch (approvalNeeded.type) {
                    case 'approval_not_required':
                        return erc20OptionWithoutApproval
                    case 'approval_required': {
                        const approvalRequest = createApprovalTransaction({
                            amount: approvalNeeded.amount,
                            owner: sender,
                            spender: PAYMASTER_ADDRESS,
                        })

                        const withApprovalFeeInNativeToken: CryptoMoney = {
                            currency: gasAbstractionCurrency,
                            amount:
                                tokenPaymasterWithApprovalGasEstimate.totalGas *
                                gasPrice.maxFeePerGas,
                        }

                        const withApprovalFeeInUSD = nativeToDefaultRate
                            ? applyRate2({
                                  baseAmount: withApprovalFeeInNativeToken,
                                  rate: nativeToDefaultRate,
                              })
                            : null

                        const withApprovalFeeInERC20 = applyRate2({
                            baseAmount: withApprovalFeeInNativeToken,
                            rate: crossRate,
                        })

                        return {
                            type: 'erc20_gas_abstraction_transaction_fee' as const,
                            feeInTokenCurrency: withApprovalFeeInERC20,
                            feeInDefaultCurrency: withApprovalFeeInUSD,
                            gasPrice,
                            gasEstimate: tokenPaymasterWithApprovalGasEstimate,
                            callData:
                                metaTransactionDatasToUserOperationCallData({
                                    metaTransactionDatas: [
                                        ...metaTransactionDatas,
                                        ethSendTransactionToMetaTransactionData(
                                            approvalRequest
                                        ),
                                    ],
                                }),
                        }
                    }
                    /* istanbul ignore next */
                    default:
                        return notReachable(approvalNeeded)
                }
            })
        )

    const erc20OptionsWithNullsRemoved = erc20Options.filter(
        (feeOption): feeOption is ERC20GasAbstractionTransactionFee =>
            feeOption !== null
    )

    return {
        type: 'non_sponsored_transaction',
        fees: [nativeCurrencyOption, ...erc20OptionsWithNullsRemoved],
    }
}
