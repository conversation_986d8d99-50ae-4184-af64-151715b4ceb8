import { post as bePost } from '@zeal/api/requestBackend'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { withRetries } from '@zeal/toolkit/Function'
import { parse as parseJSON } from '@zeal/toolkit/JSON'
import { failure, object, Result, string, success } from '@zeal/toolkit/Result'

import {
    parseBiconomyBundlerResponseErrorFromHttpError,
    parsePimlicoBundlerErrorResponse,
} from '@zeal/domains/Error/domains/BundlerError/parsers/parseBundlerResponseError'
import { ActionSource2 } from '@zeal/domains/Main'
import { PredefinedNetwork, TestNetwork } from '@zeal/domains/Network'
import { refineBundlerExecutionError } from '@zeal/domains/UserOperation/api/refineBundlerExecutionError'

export type BiconomyBundlerRequest =
    | EthEstimateUserOperationGas
    | EthSendUserOperation
    | BiconomyGetUserOperationStatus

export type PimlicoBundlerRequest =
    | EthEstimateUserOperationGas
    | EthSendUserOperation
    | PimlicoGetUserOperationStatus

export type EthEstimateUserOperationGas = {
    method: 'eth_estimateUserOperationGas'
    params:
        | [
              {
                  sender: string
                  callData: string
                  nonce: string
                  initCode: string // 0x if null
                  paymasterAndData: string // can set to 0x
                  signature: string // this should be a dummy signature
              },
              string, // entrypoint address
          ]
        | [
              {
                  sender: string
                  callData: string
                  nonce: string
                  initCode: string // 0x if null
                  paymasterAndData: string // can set to 0x
                  signature: string // this should be a dummy signature
              },
              string, // entrypoint address
              object, // state overrides
          ]
} & Common

export type EthSendUserOperation = {
    method: 'eth_sendUserOperation'
    params: [
        {
            sender: string
            nonce: string
            initCode: string // 0x if null
            callData: string
            callGasLimit: string
            verificationGasLimit: string
            preVerificationGas: string
            maxPriorityFeePerGas: string
            maxFeePerGas: string
            paymasterAndData: string
            signature: string
        },
        string, // entrypoint address
    ]
} & Common

export type BiconomyGetUserOperationStatus = {
    method: 'biconomy_getUserOperationStatus'
    params: [string] // userOperationHash
} & Common

export type PimlicoGetUserOperationStatus = {
    method: 'pimlico_getUserOperationStatus'
    params: [string] // userOperationHash
} & Common

type Common = {
    id: number | string
    jsonrpc: '2.0'
}

const parseBundlerResponse = (input: unknown): Result<unknown, unknown> =>
    string(input)
        .andThen(parseJSON)
        .andThen(object)
        .andThen((obj) =>
            obj.error ? failure(obj.error) : success(obj.result)
        )

const BUNDLER_RETRY_DELAY_MS = 1000
const BUNDLER_RETRY_COUNT = 5

const fetchBiconomyBundlerResponse = async ({
    request,
    network,
    actionSource,
    signal,
}: {
    request: BiconomyBundlerRequest
    network: PredefinedNetwork | TestNetwork
    actionSource: ActionSource2
    signal?: AbortSignal
}): Promise<unknown> => {
    return await bePost(
        `/proxy/bundler/${network.hexChainId}/`,
        { body: request },
        signal
    )
        .catch(async (error) => {
            const bundlerError = parseBiconomyBundlerResponseErrorFromHttpError(
                {
                    input: error,
                    actionSource,
                }
            ).getSuccessResult()

            if (bundlerError) {
                switch (bundlerError.type) {
                    case 'bundler_error_user_operation_reverted_during_execution_phase':
                        throw await refineBundlerExecutionError({
                            originalError: bundlerError,
                            request,
                            network,
                            signal,
                        })
                    default:
                        throw bundlerError
                }
            }

            throw error
        })
        .then((response) =>
            parseBundlerResponse(response).getSuccessResultOrThrow(
                'Failed to parse proxy bundler response'
            )
        )
}

// FIXME :: @Nicvaniek do on proxy
const NETWORK_TO_PIMLICO_CHAIN_NAME_MAP: Partial<
    Record<PredefinedNetwork['name'] | TestNetwork['name'], string>
> = {
    Ethereum: 'ethereum',
    Arbitrum: 'arbitrum',
    Polygon: 'polygon',
    Optimism: 'optimism',
    Gnosis: 'gnosis',
    Linea: 'linea',
    Base: 'base',
    Avalanche: 'avalanche',
    Celo: 'celo',
    BSC: 'binance',
    Blast: 'blast',
    Mantle: 'mantle',
    OPBNB: 'opbnb',
    EthereumSepolia: 'sepolia',
}

const fetchPimlicoBundlerResponse = async ({
    request,
    network,
    actionSource,
    signal,
}: {
    request: PimlicoBundlerRequest
    network: PredefinedNetwork | TestNetwork
    actionSource: ActionSource2
    signal?: AbortSignal
}): Promise<unknown> => {
    const chain = NETWORK_TO_PIMLICO_CHAIN_NAME_MAP[network.name]

    if (!chain) {
        throw new ImperativeError(
            'Trying to use pimlico bundler on unsupported chain',
            { network: network.name }
        )
    }

    const response = await bePost(
        `/proxy/plc/${chain}/rpc`,
        { query: undefined, body: request },
        signal
    )

    const parsed = parseBundlerResponse(response)

    switch (parsed.type) {
        case 'Failure':
            throw parsePimlicoBundlerErrorResponse({
                response: parsed.reason,
                request,
                networkHexId: network.hexChainId,
                actionSource,
            }).getSuccessResultOrThrow('Failed to parse Pimlico error response')
        case 'Success':
            return parsed.data
        default:
            return notReachable(parsed)
    }
}

export const fetchBiconomyBundlerResponseWithRetries = withRetries({
    retries: BUNDLER_RETRY_COUNT,
    delayMs: BUNDLER_RETRY_DELAY_MS,
    fn: fetchBiconomyBundlerResponse,
})

export const fetchPimlicoBundlerResponseWithRetries = withRetries({
    retries: BUNDLER_RETRY_COUNT,
    delayMs: BUNDLER_RETRY_DELAY_MS,
    fn: fetchPimlicoBundlerResponse,
})
