import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { fromBigInt } from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'

import { ActionSource2 } from '@zeal/domains/Main'
import { Network, NetworkRPCMap } from '@zeal/domains/Network'
import { fetchBlockNumber } from '@zeal/domains/RPCRequest/api/fetchBlockNumber'
import { SubmittedUserOperationPending } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import {
    BoostedUserOperationWithSignature,
    UserOperationWithSignature,
} from '@zeal/domains/UserOperation'
import {
    EthSendUserOperation,
    fetchBiconomyBundlerResponseWithRetries,
    fetchPimlicoBundlerResponseWithRetries,
} from '@zeal/domains/UserOperation/api/fetchBundlerResponse'
import {
    PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA,
    PIMLICO_BOOSTED_USER_OP_PVG,
    PIMLICO_BOOSTED_USER_OPERATION_MAX_FEE,
} from '@zeal/domains/UserOperation/constants'

export const submitUserOperationToBundler = async ({
    userOperationWithSignature,
    network,
    networkRPCMap,
    actionSource,
    signal,
}: {
    userOperationWithSignature:
        | UserOperationWithSignature
        | BoostedUserOperationWithSignature
    network: Network
    networkRPCMap: NetworkRPCMap
    actionSource: ActionSource2
    signal?: AbortSignal
}): Promise<SubmittedUserOperationPending> => {
    switch (network.type) {
        case 'predefined':
        case 'testnet': {
            switch (userOperationWithSignature.type) {
                case 'user_operation_with_signature': {
                    const request: EthSendUserOperation = {
                        id: generateRandomNumber(),
                        jsonrpc: '2.0',
                        method: 'eth_sendUserOperation',
                        params: [
                            {
                                sender: userOperationWithSignature.sender,
                                callData: userOperationWithSignature.callData,
                                initCode:
                                    userOperationWithSignature.initCode || '0x',
                                nonce: fromBigInt(
                                    userOperationWithSignature.entrypointNonce
                                ),
                                maxFeePerGas: fromBigInt(
                                    userOperationWithSignature.maxFeePerGas
                                ),
                                maxPriorityFeePerGas: fromBigInt(
                                    userOperationWithSignature.maxPriorityFeePerGas
                                ),
                                callGasLimit: fromBigInt(
                                    userOperationWithSignature.callGasLimit
                                ),
                                verificationGasLimit: fromBigInt(
                                    userOperationWithSignature.verificationGasLimit
                                ),
                                preVerificationGas: fromBigInt(
                                    userOperationWithSignature.preVerificationGas
                                ),
                                paymasterAndData:
                                    userOperationWithSignature.paymasterAndData ||
                                    '0x',
                                signature: userOperationWithSignature.signature,
                            },
                            userOperationWithSignature.entrypoint,
                        ],
                    }

                    const [hashResponse, blockNumber] = await Promise.all([
                        fetchBiconomyBundlerResponseWithRetries({
                            request,
                            network,
                            actionSource,
                            signal,
                        }),
                        fetchBlockNumber({
                            networkRPCMap,
                            signal,
                            network,
                        }),
                    ])
                    const userOperationHash = Hexadecimal.parse(
                        hashResponse
                    ).getSuccessResultOrThrow(
                        'Failed to parse user operation hash'
                    )

                    return {
                        type: 'user_operation',
                        queuedAt: Date.now(),
                        userOperationHash,
                        sender: userOperationWithSignature.sender,
                        submittedBlock: BigInt(blockNumber),
                        state: 'pending',
                    }
                }

                case 'boosted_user_operation_with_signature': {
                    const boostedRequest: EthSendUserOperation = {
                        id: generateRandomNumber(),
                        jsonrpc: '2.0',
                        method: 'eth_sendUserOperation',
                        params: [
                            {
                                sender: userOperationWithSignature.sender,
                                callData: userOperationWithSignature.callData,
                                initCode:
                                    userOperationWithSignature.initCode || '0x',
                                nonce: fromBigInt(
                                    userOperationWithSignature.entrypointNonce
                                ),
                                maxFeePerGas: fromBigInt(
                                    PIMLICO_BOOSTED_USER_OPERATION_MAX_FEE
                                ),
                                maxPriorityFeePerGas: fromBigInt(
                                    PIMLICO_BOOSTED_USER_OPERATION_MAX_FEE
                                ),
                                callGasLimit: fromBigInt(
                                    userOperationWithSignature.callGasLimit
                                ),
                                verificationGasLimit: fromBigInt(
                                    userOperationWithSignature.verificationGasLimit
                                ),
                                preVerificationGas: fromBigInt(
                                    PIMLICO_BOOSTED_USER_OP_PVG
                                ),
                                paymasterAndData:
                                    PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA ||
                                    '0x',
                                signature: userOperationWithSignature.signature,
                            },
                            userOperationWithSignature.entrypoint,
                        ],
                    }

                    const [boostedResponse, blockNumber] = await Promise.all([
                        fetchPimlicoBundlerResponseWithRetries({
                            request: boostedRequest,
                            network,
                            actionSource,
                            signal,
                        }),
                        fetchBlockNumber({
                            networkRPCMap,
                            signal,
                            network,
                        }),
                    ])

                    const userOperationHash = Hexadecimal.parse(
                        boostedResponse
                    ).getSuccessResultOrThrow(
                        'Failed to parse pimlico user operation hash'
                    )

                    return {
                        type: 'boosted_user_operation',
                        state: 'pending',
                        queuedAt: Date.now(),
                        userOperationHash,
                        sender: userOperationWithSignature.sender,
                        submittedBlock: BigInt(blockNumber),
                    }
                }
                default:
                    return notReachable(userOperationWithSignature)
            }
        }
        case 'custom':
            throw new ImperativeError(
                'Cannot submit user operation on custom network'
            ) // TODO :: @Nicvaniek create narrowed network type for chains that support gas abstraction
        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}
