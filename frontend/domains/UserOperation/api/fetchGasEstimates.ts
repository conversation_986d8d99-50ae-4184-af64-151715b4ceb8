import * as Hex from '@zeal/toolkit/Hexadecimal'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

import { ActionSource2 } from '@zeal/domains/Main'
import { Network } from '@zeal/domains/Network'
import {
    BundlerGasEstimate,
    BundlerGasPrice,
    InitialUserOperation,
} from '@zeal/domains/UserOperation'
import {
    fetchBiconomyBundlerFeeAndGasEstimates,
    fetchPimlicoBundlerGasEstimates,
} from '@zeal/domains/UserOperation/api/fetchFeeAndGasEstimatesFromBundler'
import {
    APPROVAL_CALL_GAS_LIMIT_BUFFER,
    PIMLICO_BOOSTED_USER_OP_PVG,
    TOKEN_PAYMASTER_VERIFICATION_GAS_LIMIT_BUFFER,
} from '@zeal/domains/UserOperation/constants'

export const PAYMASTER_VERIFICATION_GAS_LIMIT_MULTIPLIER = 3n
export const PRECISION = 1000000
export const MINIMUM_CALL_GAS_LIMIT = 25_000n // This is roughly the gas needed for a native transfer. The callGasLimit should never be lower than this.
export const MAX_FEE_PER_GAS_BUFFER_MULTIPLIER = 1.1 // 10% buffer on MaxFeePerGas -> preferably push this down to 0 but can cause issues with rejected UserOps from biconomy

type TotalGasEstimate = BundlerGasEstimate & { totalGas: bigint }

type GasCalculationResponse = {
    gasPrice: BundlerGasPrice
    nonPaymasterGasEstimate: TotalGasEstimate
    tokenPaymasterWithoutApprovalGasEstimate: TotalGasEstimate
    tokenPaymasterWithApprovalGasEstimate: TotalGasEstimate
}

const calculateTotalGas = ({
    gasEstimate,
}: {
    gasEstimate: BundlerGasEstimate
}): bigint =>
    gasEstimate.callGasLimit +
    gasEstimate.verificationGasLimit +
    gasEstimate.preVerificationGas

export const fetchBiconomyGasEstimates = async ({
    network,
    initialUserOperation,
    verificationGasLimitBuffer,
    callGasLimitBuffer,
    dummySignature,
    actionSource,
    signal,
}: {
    initialUserOperation: InitialUserOperation
    network: Network
    verificationGasLimitBuffer: bigint
    callGasLimitBuffer: bigint
    dummySignature: Hexadecimal
    actionSource: ActionSource2
    signal?: AbortSignal
}): Promise<GasCalculationResponse> => {
    const bundlerResponse = await fetchBiconomyBundlerFeeAndGasEstimates({
        initialUserOperation: {
            callData: initialUserOperation.callData,
            nonce: initialUserOperation.nonce,
            initCode: initialUserOperation.initCode,
            sender: initialUserOperation.sender,
            signature: dummySignature,
            paymasterAndData: null,
        },
        entrypoint: initialUserOperation.entrypoint,
        network,
        actionSource,
        callGasLimitBuffer,
        signal,
    })
    const bufferedGasPrice = {
        maxFeePerGas:
            (bundlerResponse.gasPrice.maxFeePerGas *
                BigInt(MAX_FEE_PER_GAS_BUFFER_MULTIPLIER * PRECISION)) /
            BigInt(PRECISION),
        maxPriorityFeePerGas: bundlerResponse.gasPrice.maxPriorityFeePerGas,
    }

    const adjustedCallGasLimit =
        bundlerResponse.gasEstimate.callGasLimit < MINIMUM_CALL_GAS_LIMIT
            ? MINIMUM_CALL_GAS_LIMIT
            : bundlerResponse.gasEstimate.callGasLimit

    const sigBufferedVerificationGasLimit =
        bundlerResponse.gasEstimate.verificationGasLimit +
        verificationGasLimitBuffer

    // Token Paymaster calculations (ERC20 payments)

    const tokenPaymasterVerificationGasLimit =
        sigBufferedVerificationGasLimit +
        TOKEN_PAYMASTER_VERIFICATION_GAS_LIMIT_BUFFER

    const approvalBufferedCallGasLimit =
        adjustedCallGasLimit + APPROVAL_CALL_GAS_LIMIT_BUFFER

    // No paymaster calculations (native payment)

    const nonPaymasterTotalGas = calculateTotalGas({
        gasEstimate: {
            callGasLimit: adjustedCallGasLimit,
            verificationGasLimit: sigBufferedVerificationGasLimit,
            preVerificationGas: bundlerResponse.gasEstimate.preVerificationGas,
        },
    })

    const nonPaymasterGasEstimate: TotalGasEstimate = {
        callGasLimit: adjustedCallGasLimit,
        verificationGasLimit: sigBufferedVerificationGasLimit,
        preVerificationGas: bundlerResponse.gasEstimate.preVerificationGas,
        totalGas: nonPaymasterTotalGas,
    }

    // Paymaster without approval calculations

    const paymasterWithoutApprovalTotalGas = calculateTotalGas({
        gasEstimate: {
            callGasLimit: adjustedCallGasLimit,
            verificationGasLimit:
                tokenPaymasterVerificationGasLimit *
                PAYMASTER_VERIFICATION_GAS_LIMIT_MULTIPLIER,
            preVerificationGas: bundlerResponse.gasEstimate.preVerificationGas,
        },
    })

    const paymasterWithoutApprovalGasEstimate: TotalGasEstimate = {
        callGasLimit: adjustedCallGasLimit,
        verificationGasLimit: tokenPaymasterVerificationGasLimit,
        preVerificationGas: bundlerResponse.gasEstimate.preVerificationGas,
        totalGas: paymasterWithoutApprovalTotalGas,
    }

    // Paymaster with approval calculations

    const paymasterWithApprovalTotalGas = calculateTotalGas({
        gasEstimate: {
            callGasLimit: approvalBufferedCallGasLimit,
            verificationGasLimit:
                tokenPaymasterVerificationGasLimit *
                PAYMASTER_VERIFICATION_GAS_LIMIT_MULTIPLIER,
            preVerificationGas: bundlerResponse.gasEstimate.preVerificationGas,
        },
    })

    const paymasterWithApprovalGasEstimate: TotalGasEstimate = {
        callGasLimit: approvalBufferedCallGasLimit,
        verificationGasLimit: tokenPaymasterVerificationGasLimit,
        preVerificationGas: bundlerResponse.gasEstimate.preVerificationGas,
        totalGas: paymasterWithApprovalTotalGas,
    }

    return {
        gasPrice: bufferedGasPrice,
        nonPaymasterGasEstimate,
        tokenPaymasterWithoutApprovalGasEstimate:
            paymasterWithoutApprovalGasEstimate,
        tokenPaymasterWithApprovalGasEstimate: paymasterWithApprovalGasEstimate,
    }
}

export const fetchPimlicoGasEstimates = async ({
    network,
    entrypoint,
    initialUserOperation,
    verificationGasLimitBuffer,
    callGasLimitBuffer,
    actionSource,
    signal,
}: {
    initialUserOperation: {
        sender: Web3.address.Address
        nonce: bigint
        initCode: Hex.Hexadecimal | null
        callData: Hex.Hexadecimal
        paymasterAndData: Hex.Hexadecimal | null
        signature: Hex.Hexadecimal
    }
    entrypoint: Web3.address.Address
    network: Network
    verificationGasLimitBuffer: bigint
    callGasLimitBuffer: bigint
    actionSource: ActionSource2
    signal?: AbortSignal
}): Promise<BundlerGasEstimate> => {
    const bundlerResponse = await fetchPimlicoBundlerGasEstimates({
        initialUserOperation,
        entrypoint,
        network,
        actionSource,
        signal,
    })

    const adjustedCallGasLimit =
        bundlerResponse.callGasLimit < MINIMUM_CALL_GAS_LIMIT
            ? MINIMUM_CALL_GAS_LIMIT
            : bundlerResponse.callGasLimit

    const bufferedCallGasLimit = adjustedCallGasLimit + callGasLimitBuffer

    const sigBufferedVerificationGasLimit =
        bundlerResponse.verificationGasLimit + verificationGasLimitBuffer

    return {
        callGasLimit: bufferedCallGasLimit,
        verificationGasLimit: sigBufferedVerificationGasLimit,
        preVerificationGas: PIMLICO_BOOSTED_USER_OP_PVG,
    }
}
