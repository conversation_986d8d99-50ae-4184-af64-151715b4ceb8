import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'

import { Account } from '@zeal/domains/Account'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { SignedUserOperationRequest } from '@zeal/domains/TransactionRequest'
import { submitUserOperationRequest } from '@zeal/domains/TransactionRequest/api/submitUserOperationRequest'
import {
    SubmittedUserOperationCompleted,
    SubmittedUserOperationFailed,
    SubmittedUserOperationRejected,
} from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import { FetchSimulationByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import { BoostedUserOperationWithoutSignature } from '@zeal/domains/UserOperation'

import { fetchCurrentEntrypointNonce } from './fetchCurrentEntrypointNonce'
import { monitorSubmittedUserOperation } from './monitorSubmittedUserOperation'
import { simulateSafeTransaction } from './simulateSafeTransaction'

import { calculateUserOperationHash } from '../helpers/calculateUserOperationHash'
import { signUserOperationHashWithLocalSigner } from '../helpers/signUserOperationHashWithLocalSigner'
import { signUserOperationHashWithPassKey } from '../helpers/signUserOperationHashWithPassKey'

type Params = {
    rpcRequestsToBundle: EthSendTransaction[]
    fetchSimulationByRequest: FetchSimulationByRequest
    network: Network
    networkMap: NetworkMap
    keyStore: Safe4337
    account: Account
    networkRPCMap: NetworkRPCMap
    dApp: DAppSiteInfo | null
    portfolio: ServerPortfolio2 | null
    defaultCurrencyConfig: DefaultCurrencyConfig
    sessionPassword: string
    installationId: string
    actionSource: ActionSource2
    signal?: AbortSignal
}

// FIXME @resetko-zeal check this fetcher if it can use new types
export const submitAndMonitorSponsoredUserOperation = async ({
    account,
    dApp,
    defaultCurrencyConfig,
    fetchSimulationByRequest,
    installationId,
    keyStore,
    network,
    networkMap,
    networkRPCMap,
    portfolio,
    rpcRequestsToBundle,
    sessionPassword,
    actionSource,
    signal,
}: Params): Promise<
    | SubmittedUserOperationCompleted
    | SubmittedUserOperationRejected
    | SubmittedUserOperationFailed
> => {
    const { feeForecast, userOperationRequest } = await simulateSafeTransaction(
        {
            account,
            dApp,
            defaultCurrencyConfig,
            fetchSimulationByRequest,
            keyStore,
            network,
            networkMap,
            networkRPCMap,
            portfolio,
            rpcRequestsToBundle,
            signal,
            sponsored: true,
            actionSource,
        }
    )

    const entrypointNonce = await fetchCurrentEntrypointNonce({
        network: userOperationRequest.network,
        address: userOperationRequest.account.address,
        entrypoint: userOperationRequest.entrypoint,
        networkRPCMap,
        signal,
    })

    const selectedFee = (() => {
        switch (feeForecast.type) {
            case 'sponsored_transaction':
                return feeForecast.fee
            case 'non_sponsored_transaction':
                throw new ImperativeError(
                    'Non-sponsored fee forecast returned for sponsored transaction'
                )

            default:
                return notReachable(feeForecast)
        }
    })()

    const userOperationWithoutSignature: BoostedUserOperationWithoutSignature =
        {
            type: 'boosted_user_operation_without_signature',
            callData: selectedFee.callData,
            sender: userOperationRequest.account.address,
            entrypointNonce,
            entrypoint: userOperationRequest.entrypoint,
            initCode: userOperationRequest.initCode,
            callGasLimit: selectedFee.gasEstimate.callGasLimit,
            verificationGasLimit: selectedFee.gasEstimate.verificationGasLimit,
        }

    const userOperationHash = calculateUserOperationHash({
        network,
        userOperation: userOperationWithoutSignature,
        now: Date.now(),
    })

    const signedUserOperationRequest: SignedUserOperationRequest =
        await (async () => {
            switch (userOperationRequest.type) {
                case 'simulated_safe_with_add_owner_user_operation_request':
                case 'simulated_safe_deployment_bundle_user_operation_request': {
                    const signature = await signUserOperationHashWithPassKey({
                        passkey: keyStore.safeDeplymentConfig.passkeyOwner,
                        userOperationHash,
                        sessionPassword,
                    })

                    return {
                        ...userOperationRequest,
                        type: 'signed_safe_user_operation_request',
                        userOperationWithSignature: {
                            ...userOperationWithoutSignature,
                            type: 'boosted_user_operation_with_signature',
                            signature,
                        },
                    }
                }

                case 'simulated_safe_without_deployment_bundle_user_operation_request': {
                    const signature =
                        await signUserOperationHashWithLocalSigner({
                            keyStore,
                            network,
                            sessionPassword,
                            userOperationHash,
                            dApp,
                        })

                    return {
                        ...userOperationRequest,
                        type: 'signed_safe_user_operation_request',
                        userOperationWithSignature: {
                            ...userOperationWithoutSignature,
                            type: 'boosted_user_operation_with_signature',
                            signature,
                        },
                    }
                }
                /* istanbul ignore next */
                default:
                    return notReachable(userOperationRequest)
            }
        })()

    const submittedToBundlerUserOperationRequest =
        await submitUserOperationRequest({
            networkRPCMap,
            userOperationRequest: signedUserOperationRequest,
            actionSource,
            signal,
        })

    return monitorSubmittedUserOperation({
        installationId,
        network,
        networkRPCMap,
        submittedUserOperation:
            submittedToBundlerUserOperationRequest.submittedUserOperation,
        signal,
    })
}
