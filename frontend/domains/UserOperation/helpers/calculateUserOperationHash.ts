import { notReachable } from '@zeal/toolkit'
import { keccak256 } from '@zeal/toolkit/Crypto'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { encodeAbiParameters, encodePacked } from '@zeal/toolkit/Web3/abi'

import {
    SAFE_4337_MODULE_ADDRESS,
    SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
} from '@zeal/domains/Address/constants'
import { Network } from '@zeal/domains/Network'

import {
    PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA,
    PIMLICO_BOOSTED_USER_OP_PVG,
    PIMLICO_BOOSTED_USER_OPERATION_MAX_FEE,
} from '../constants'
import {
    BoostedUserOperationWithoutSignature,
    UserOperationHash,
    UserOperationWithoutSignature,
} from '../UserOperation'

// keccak256("EIP712Domain(uint256 chainId,address verifyingContract)")
const DOMAIN_SEPARATOR_TYPEHASH =
    '0x47e79534a245952e8b16893a336b85a3d9ea9fa8c573f3d803afb92a79469218' as const

// keccak256("SafeOp(address safe,uint256 nonce,bytes initCode,bytes callData,uint256 callGasLimit,uint256 verificationGasLimit,uint256 preVerificationGas,uint256 maxFeePerGas,uint256 maxPriorityFeePerGas,bytes paymasterAndData,uint48 validAfter,uint48 validUntil,address entryPoint)")
const SAFE_OP_TYPEHASH =
    '0x84aa190356f56b8c87825f54884392a9907c23ee0f8e1ea86336b763faf021bd' as const

const EIP191_PREFIX = '0x1901' as const

const EMPTY_BYTES_HASH =
    '0xc5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470' as const

const THIRTY_MINUTES_IN_SEC = 30 * 60

const getSafeOpHash = ({
    userOperation,
    validAfter,
    validUntil,
}: {
    userOperation:
        | UserOperationWithoutSignature
        | BoostedUserOperationWithoutSignature
    validAfter: number
    validUntil: number
}): Hexadecimal.Hexadecimal => {
    const initCodeHash = userOperation.initCode
        ? keccak256(userOperation.initCode as Hexadecimal.Hexadecimal)
        : EMPTY_BYTES_HASH
    const callDataHash = keccak256(
        userOperation.callData as Hexadecimal.Hexadecimal
    )
    const {
        maxFeePerGas,
        maxPriorityFeePerGas,
        preVerificationGas,
        paymasterAndData,
    } = (() => {
        switch (userOperation.type) {
            case 'user_operation_without_signature':
                return {
                    maxFeePerGas: userOperation.maxFeePerGas,
                    maxPriorityFeePerGas: userOperation.maxPriorityFeePerGas,
                    preVerificationGas: userOperation.preVerificationGas,
                    paymasterAndData: userOperation.paymasterAndData,
                }
            case 'boosted_user_operation_without_signature':
                return {
                    maxFeePerGas: PIMLICO_BOOSTED_USER_OPERATION_MAX_FEE,
                    maxPriorityFeePerGas:
                        PIMLICO_BOOSTED_USER_OPERATION_MAX_FEE,
                    preVerificationGas: PIMLICO_BOOSTED_USER_OP_PVG,
                    paymasterAndData:
                        PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA,
                }
            default:
                return notReachable(userOperation)
        }
    })()

    const paymasterAndDataHash = paymasterAndData
        ? keccak256(paymasterAndData as Hexadecimal.Hexadecimal)
        : EMPTY_BYTES_HASH

    return keccak256(
        encodeAbiParameters(
            [
                { type: 'bytes32', name: 'typeHash' },
                { type: 'address', name: 'safe' },
                { type: 'uint256', name: 'nonce' },
                { type: 'bytes32', name: 'initCodeHash' },
                { type: 'bytes32', name: 'callDataHash' },
                { type: 'uint256', name: 'callGasLimit' },
                { type: 'uint256', name: 'verificationGasLimit' },
                { type: 'uint256', name: 'preVerificationGas' },
                { type: 'uint256', name: 'maxFeePerGas' },
                { type: 'uint256', name: 'maxPriorityFeePerGas' },
                { type: 'bytes32', name: 'paymasterAndDataHash' },
                { type: 'uint48', name: 'validAfter' },
                { type: 'uint48', name: 'validUntil' },
                { type: 'address', name: 'entryPoint' },
            ],
            [
                SAFE_OP_TYPEHASH,
                userOperation.sender as `0x${string}`,
                userOperation.entrypointNonce,
                initCodeHash,
                callDataHash,
                userOperation.callGasLimit,
                userOperation.verificationGasLimit,
                preVerificationGas,
                maxFeePerGas,
                maxPriorityFeePerGas,
                paymasterAndDataHash,
                validAfter,
                validUntil,
                SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
            ]
        )
    )
}

export const calculateUserOperationHash = ({
    userOperation,
    network,
    now,
}: {
    userOperation:
        | UserOperationWithoutSignature
        | BoostedUserOperationWithoutSignature
    network: Network
    now: number
}): UserOperationHash => {
    const nowInSeconds = Math.round(now / 1000)
    const validAfter = nowInSeconds - THIRTY_MINUTES_IN_SEC
    const validUntil = nowInSeconds + THIRTY_MINUTES_IN_SEC

    const chainId = BigInt(network.hexChainId)
    const domainSeparator = keccak256(
        encodeAbiParameters(
            [
                { type: 'bytes32', name: 'typeHash' },
                { type: 'uint256', name: 'chainId' },
                { type: 'address', name: 'verifyingContract' },
            ],
            [DOMAIN_SEPARATOR_TYPEHASH, chainId, SAFE_4337_MODULE_ADDRESS]
        )
    )

    const safeOpStructHash = getSafeOpHash({
        userOperation,
        validAfter,
        validUntil,
    })

    const preHash = Hexadecimal.concat(
        EIP191_PREFIX,
        domainSeparator,
        safeOpStructHash
    )
    const finalHash = keccak256(preHash)

    return {
        type: 'user_operation_hash',
        hash: finalHash,
        encodedValidFrom: encodePacked(['uint48'], [validAfter]),
        encodedValidUntil: encodePacked(['uint48'], [validUntil]),
    }
}
