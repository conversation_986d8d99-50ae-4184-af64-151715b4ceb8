import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useLoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef, usePrevious } from '@zeal/toolkit/React'

import { fetchSubmittedCardTopUp } from '@zeal/domains/Card/api/fetchSubmittedCardTopUp'
import { CARD_NETWORK } from '@zeal/domains/Card/constants'
// eslint-disable-next-line zeal-domains/no-feature-deep-import
import { SubmittedCardTopUp } from '@zeal/domains/Card/features/CardAddCash/CardAddCashV2/types' // FIXME :: @Nicvaniek
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import {
    captureTransactionFailureError,
    captureUserOperationFailureError,
} from '@zeal/domains/Error/helpers/captureTransactionFailureError'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { EOA } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { keystoreToUserEventType } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'

type Props = {
    submittedCardTopUp: SubmittedCardTopUp
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_pending_card_top_up_state_changed'
          submittedCardTopUp: SubmittedCardTopUp
      }
    | MsgOf<typeof Layout>

const POLL_INTERVAL_MS = 2000

const getNetwork = (
    submittedCardTopUp: SubmittedCardTopUp,
    networkMap: NetworkMap
): Network => {
    switch (submittedCardTopUp.type) {
        case 'send_safe':
        case 'send_eoa':
        case 'earn_sign_typed_data':
        case 'swap_sign_typed_data':
            return CARD_NETWORK
        case 'swap_eoa_native_send_transaction':
        case 'swap_safe_native_send_transaction':
            return findNetworkByHexChainId(
                submittedCardTopUp.fromAmount.currency.networkHexChainId,
                networkMap
            )
        default:
            return notReachable(submittedCardTopUp)
    }
}

const getEOAKeystore = (submittedCardTopUp: SubmittedCardTopUp): EOA => {
    switch (submittedCardTopUp.type) {
        case 'send_eoa':
        case 'swap_eoa_native_send_transaction':
            return submittedCardTopUp.keyStore
        case 'send_safe':
        case 'earn_sign_typed_data':
        case 'swap_sign_typed_data':
        case 'swap_safe_native_send_transaction':
            throw new ImperativeError('Not EOA keystore type')
        default:
            return notReachable(submittedCardTopUp)
    }
}

export const PendingCardTopUpBanner = ({
    submittedCardTopUp,
    networkMap,
    installationId,
    onMsg,
    networkRPCMap,
}: Props) => {
    const [pollable, setPollable] = useLoadedPollableData(
        fetchSubmittedCardTopUp,
        {
            type: 'reloading',
            params: {
                submittedCardTopUp,
                networkMap,
                networkRPCMap,
            },
            data: submittedCardTopUp,
        },
        {
            stopIf: (pollable) => {
                switch (pollable.type) {
                    case 'loaded':
                    case 'reloading':
                    case 'subsequent_failed':
                        switch (pollable.data.state.type) {
                            case 'completed':
                            case 'failed_user_operation':
                            case 'failed_tx':
                            case 'failed_bungee':
                                return true
                            case 'waiting_for_user_operation':
                            case 'waiting_for_transaction':
                            case 'waiting_for_swap':
                                return false
                            default:
                                return notReachable(pollable.data.state)
                        }
                    default:
                        return notReachable(pollable)
                }
            },
            pollIntervalMilliseconds: POLL_INTERVAL_MS,
        }
    )

    const captureErrorOnce = useCaptureErrorOnce()
    const onMsgLive = useLiveRef(onMsg)

    const previous = usePrevious(pollable.data)

    useEffect(() => {
        if (previous && previous.state.type !== pollable.data.state.type) {
            onMsgLive.current({
                type: 'on_pending_card_top_up_state_changed',
                submittedCardTopUp: pollable.data,
            })
            setPollable({
                ...pollable,
                params: {
                    ...pollable.params,
                    submittedCardTopUp: pollable.data,
                },
            })
        }
    }, [onMsgLive, pollable, previous, setPollable])

    useEffect(() => {
        switch (pollable.type) {
            case 'subsequent_failed':
                captureErrorOnce(pollable.error)
                break
            case 'loaded':
            case 'reloading':
                switch (pollable.data.state.type) {
                    case 'waiting_for_user_operation':
                    case 'waiting_for_transaction':
                    case 'waiting_for_swap':
                    case 'completed':
                        break
                    case 'failed_tx': {
                        const network = getNetwork(
                            submittedCardTopUp,
                            networkMap
                        )

                        const keyStore = getEOAKeystore(submittedCardTopUp)

                        postUserEvent({
                            type: 'TransactionFailedEvent',
                            installationId,
                            keystoreType: keystoreToUserEventType(keyStore),
                            state: 'failed',
                            source: submittedCardTopUp.actionSource
                                .transactionEventSource,
                            network: network.hexChainId,
                        })
                        captureTransactionFailureError({
                            actionSource: submittedCardTopUp.actionSource,
                            keyStore,
                            network,
                            transaction: pollable.data.state.transaction,
                        })
                        break
                    }
                    case 'failed_user_operation': {
                        const network = getNetwork(
                            submittedCardTopUp,
                            networkMap
                        )

                        postUserEvent({
                            type: 'TransactionFailedEvent',
                            installationId,
                            keystoreType: 'Safe',
                            network: network.hexChainId,
                            state: pollable.data.state.userOperation.state,
                            source: submittedCardTopUp.actionSource
                                .transactionEventSource,
                        })
                        captureUserOperationFailureError({
                            userOperation: pollable.data.state.userOperation,
                            actionSource: submittedCardTopUp.actionSource,
                            network,
                        })
                        break
                    }
                    case 'failed_bungee':
                        captureError(new ImperativeError('Bungee failed')) // FIXME :: @Nicvaniek - new event + specific error for Sentry
                        break
                    default:
                        return notReachable(pollable.data.state)
                }
                break
            default:
                return notReachable(pollable)
        }
    }, [
        captureErrorOnce,
        installationId,
        networkMap,
        onMsgLive,
        pollable,
        submittedCardTopUp,
    ])

    return <Layout submittedCardTopUp={pollable.data} onMsg={onMsg} />
}
