import React from 'react'
import { FormattedMessage } from 'react-intl'

import { AvatarSize, Badge, BadgeSize, DoubleAvatar } from '@zeal/uikit/Avatar'
import { DismissibleBanner } from '@zeal/uikit/DismissibleBanner'
import { BoldThickZealCard as ZealCard } from '@zeal/uikit/Icon/BoldThickZealCard'
import { Clock } from '@zeal/uikit/Icon/Clock'
import { SpamFolder } from '@zeal/uikit/Icon/SpamFolder'
import { ProgressThin } from '@zeal/uikit/ProgressThin'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { intervalToDuration } from '@zeal/toolkit/Date'
import { useCurrentTimestamp } from '@zeal/toolkit/Date/useCurrentTimestamp'
import { useReadableDurationWithSeconds } from '@zeal/toolkit/Date/useReadableDuration'
import { RangeInt } from '@zeal/toolkit/Range'

// eslint-disable-next-line zeal-domains/no-feature-deep-import
import {
    CardTopUpProgressState,
    SubmittedCardTopUp,
} from '@zeal/domains/Card/features/CardAddCash/CardAddCashV2/types' // FIXME :: @Nicvaniek
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { TakerAvatar } from '@zeal/domains/Earn/components/TakerAvatar'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { convertStableCoinToFiat } from '@zeal/domains/Money/helpers/convertStableCoinToFiat'

type Props = {
    submittedCardTopUp: SubmittedCardTopUp
    onMsg: (msg: Msg) => void
}

type Msg = {
    type: 'on_card_top_up_banner_dismissed'
    submittedCardTopUp: SubmittedCardTopUp
}

export const Layout = ({ submittedCardTopUp, onMsg }: Props) => {
    return (
        <DismissibleBanner
            title={<Title submittedCardTopUp={submittedCardTopUp} />}
            subtitle={<SubTitle submittedCardTopUp={submittedCardTopUp} />}
            bottom={<Progress submittedCardTopUp={submittedCardTopUp} />}
            icon={() => (
                <DoubleAvatar
                    back={({ size }) => (
                        <BackAvatar
                            submittedCardTopUp={submittedCardTopUp}
                            size={size}
                        />
                    )}
                    front={({ size }) => (
                        <ZealCard size={size} color="gray20" />
                    )}
                    badge={({ size }) => (
                        <StateBadge
                            state={submittedCardTopUp.state}
                            size={size}
                        />
                    )}
                />
            )}
            variant="light"
            onDismiss={() =>
                onMsg({
                    type: 'on_card_top_up_banner_dismissed',
                    submittedCardTopUp,
                })
            }
        />
    )
}

const BackAvatar = ({
    submittedCardTopUp,
    size,
}: {
    submittedCardTopUp: SubmittedCardTopUp
    size: AvatarSize
}) => {
    switch (submittedCardTopUp.type) {
        case 'send_safe':
        case 'send_eoa':
            return (
                <CurrencyAvatar
                    size={size}
                    currency={submittedCardTopUp.amount.currency}
                />
            )
        case 'swap_sign_typed_data':
        case 'swap_eoa_native_send_transaction':
        case 'swap_safe_native_send_transaction':
            return (
                <CurrencyAvatar
                    size={size}
                    currency={submittedCardTopUp.fromAmount.currency}
                />
            )
        case 'earn_sign_typed_data':
            return (
                <TakerAvatar
                    takerType={submittedCardTopUp.takerType}
                    size={size}
                    vairiant="rounded"
                />
            )
        default:
            return notReachable(submittedCardTopUp)
    }
}

const StateBadge = ({
    size,
    state,
}: {
    size: BadgeSize
    state: CardTopUpProgressState
}) => {
    switch (state.type) {
        case 'waiting_for_user_operation':
        case 'waiting_for_transaction':
        case 'waiting_for_swap':
            return (
                <Badge
                    outlineColor="surfaceDefault"
                    backgroundColor="surfaceDefault"
                    size={size}
                >
                    <Clock size={size} color="blue30" />
                </Badge>
            )
        case 'failed_user_operation':
        case 'failed_tx':
        case 'failed_bungee':
            return (
                <Badge
                    outlineColor="surfaceDefault"
                    backgroundColor="red40"
                    size={size}
                >
                    <SpamFolder size={size} color="surfaceDefault" />
                </Badge>
            )
        case 'completed':
            return null
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}

const Title = ({
    submittedCardTopUp,
}: {
    submittedCardTopUp: SubmittedCardTopUp
}) => {
    const amount = (() => {
        switch (submittedCardTopUp.type) {
            case 'send_safe':
            case 'send_eoa':
                return convertStableCoinToFiat({
                    money: submittedCardTopUp.amount,
                })
            case 'earn_sign_typed_data':
            case 'swap_sign_typed_data':
            case 'swap_eoa_native_send_transaction':
            case 'swap_safe_native_send_transaction':
                return convertStableCoinToFiat({
                    money: submittedCardTopUp.toAmount,
                })
            default:
                return notReachable(submittedCardTopUp)
        }
    })()

    return (
        <FormattedMessage
            id="card-top-up.banner.title"
            defaultMessage="Depositing {amount}"
            values={{
                amount: (
                    <FormattedMoneyPrecise
                        money={amount}
                        sign={null}
                        withSymbol
                    />
                ),
            }}
        />
    )
}

const SubTitle = ({
    submittedCardTopUp,
}: {
    submittedCardTopUp: SubmittedCardTopUp
}) => {
    const now = useCurrentTimestamp({ refreshIntervalMs: 1000 })
    const formatHumanReadableDuration = useReadableDurationWithSeconds()

    switch (submittedCardTopUp.state.type) {
        case 'completed':
            return (
                <Text variant="footnote" weight="regular" color="green30">
                    <FormattedMessage
                        id="card-top-up.banner.subtitle.completed"
                        defaultMessage="Completed"
                    />
                </Text>
            )
        case 'waiting_for_user_operation':
        case 'waiting_for_transaction':
        case 'waiting_for_swap':
            const duration = intervalToDuration({
                start: submittedCardTopUp.startedAtMs,
                end: now,
            })

            return (
                <Text variant="footnote" weight="regular" color="blue30">
                    <FormattedMessage
                        id="card-top-up.banner.subtitle.pending"
                        defaultMessage="{timerString} Pending"
                        values={{
                            timerString: formatHumanReadableDuration(duration),
                        }}
                    />
                </Text>
            )
        case 'failed_user_operation':
        case 'failed_tx':
        case 'failed_bungee':
            return (
                <Text variant="footnote" weight="regular" color="red40">
                    <FormattedMessage
                        id="card-top-up.banner.subtitle.failed"
                        defaultMessage="Failed"
                    />
                </Text>
            )
        default:
            return notReachable(submittedCardTopUp.state)
    }
}

const getProgress = ({
    submittedCardTopUp,
}: {
    submittedCardTopUp: SubmittedCardTopUp
}): {
    progress: RangeInt<0, 100>
    color: React.ComponentProps<typeof ProgressThin>['background']
} => {
    switch (submittedCardTopUp.state.type) {
        case 'completed':
            return { progress: 100, color: 'success' }
        case 'failed_tx':
        case 'failed_user_operation':
        case 'failed_bungee':
            return { progress: 100, color: 'critical' }
        case 'waiting_for_user_operation':
        case 'waiting_for_transaction':
            return { progress: 30, color: 'neutral' }
        case 'waiting_for_swap':
            return { progress: 60, color: 'neutral' }
        default:
            return notReachable(submittedCardTopUp.state)
    }
}

const Progress = ({
    submittedCardTopUp,
}: {
    submittedCardTopUp: SubmittedCardTopUp
}) => {
    const { progress, color } = getProgress({ submittedCardTopUp })

    return (
        <ProgressThin
            animationTimeMs={300}
            initialProgress={null}
            progress={progress}
            background={color}
        />
    )
}
