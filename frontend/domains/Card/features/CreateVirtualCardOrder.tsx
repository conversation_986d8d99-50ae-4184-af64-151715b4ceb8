import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { get, post } from '@zeal/api/gnosisApi'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'
import { SuccessLayout } from '@zeal/uikit/SuccessLayout'

import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { ImperativeError } from '@zeal/toolkit/Error'
import { parse as parseJSON } from '@zeal/toolkit/JSON'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { ZealMobilePlatform, ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import {
    arrayOf,
    boolean,
    failure,
    object,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import {
    logAppsflyerEvent,
    logAppsflyerEventOncePerInstallation,
} from '@zeal/domains/Appsflyer'
import {
    CardSafeFullyConfigured,
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
    RechargePreferences,
} from '@zeal/domains/Card'
import { createVirtualCardOrder } from '@zeal/domains/Card/api/createVirtualCardOrder'
import { fetchGnosisPayAccountState2WithSilentLogin } from '@zeal/domains/Card/api/fetchGnosisPayAccountState'
import { fetchWalletAppInstalled } from '@zeal/domains/Card/api/fetchWalletAppInstalled'
import { loginWithCache } from '@zeal/domains/Card/api/login'
import { APPLE_PAY_NOT_SUPPORTED_COUNTRIES } from '@zeal/domains/Card/constants'
import { cashBackTermsConfig } from '@zeal/domains/Card/domains/Cashback/constants'
import { createDeployEarnWithRechargePreferencesTransactions } from '@zeal/domains/Card/helpers/createDeployEarnWithRechargePreferencesTransactions'
import { Earn } from '@zeal/domains/Earn'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { fetchSimulationByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'
import { submitAndMonitorSponsoredUserOperation } from '@zeal/domains/UserOperation/api/submitAndMonitorSponsoredUserOperation'

type Props = {
    cardSafe: CardSafeFullyConfigured
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    earn: Earn
    installationId: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    location: 'card_settings' | 'card_tab'
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'on_virtual_card_order_created_animation_completed'
          gnosisPayStateResult: GnosisPayStateResult
      }

type GnosisPayStateResult = Result<
    | { type: 'failed_to_fetch' }
    | {
          type: 'wallet_not_installed'
          gnosisState: GnosisPayAccountOnboardedState
      }
    | {
          type: 'platform_not_supported_wallet_connect'
          gnosisState: GnosisPayAccountOnboardedState
      }
    | {
          type: 'country_not_support_wallet'
          gnosisState: GnosisPayAccountOnboardedState
      },
    {
        gnosisState: GnosisPayAccountOnboardedState
        platform: ZealMobilePlatform
    }
>

const createOrderAndReadGnosisPayState = async ({
    cardReadOnlySigner,
    keyStore,
    sessionPassword,
    networkRPCMap,
    networkMap,
    defaultCurrencyConfig,
    signal,
}: {
    cardReadOnlySigner: Web3.address.Address
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<GnosisPayStateResult> => {
    await createVirtualCardOrder({
        sessionPassword,
        keyStore,
        readonlySignerAddress: cardReadOnlySigner,
        signal,
    })

    try {
        const gnosisState = await fetchGnosisPayAccountState2WithSilentLogin({
            sessionPassword,
            keyStore,
            readonlySignerAddress: cardReadOnlySigner,
            selectedCardId: null,
            networkRPCMap,
            networkMap,
            defaultCurrencyConfig,
        })

        switch (gnosisState.type) {
            case 'not_onboarded':
                throw new ImperativeError(
                    `impossible to have not_onboarded gnosis state after virtual card order creation`
                )

            case 'onboarded':
                switch (ZealPlatform.OS) {
                    case 'web':
                        return failure({
                            type: 'platform_not_supported_wallet_connect',
                            gnosisState,
                        })
                    case 'android':
                        return await calcualeWalletAppInstalled({
                            gnosisState,
                            platform: ZealPlatform,
                        })
                    case 'ios':
                        if (
                            gnosisState.residentialAddress?.country &&
                            APPLE_PAY_NOT_SUPPORTED_COUNTRIES.includes(
                                gnosisState.residentialAddress.country
                            )
                        ) {
                            return failure({
                                type: 'country_not_support_wallet',
                                gnosisState,
                            })
                        }

                        return await calcualeWalletAppInstalled({
                            gnosisState,
                            platform: ZealPlatform,
                        })
                    /* istanbul ignore next */
                    default:
                        return notReachable(ZealPlatform)
                }
            default:
                return notReachable(gnosisState)
        }
    } catch (error) {
        captureError(error)
        return failure({ type: 'failed_to_fetch' })
    }
}

const calcualeWalletAppInstalled = async ({
    gnosisState,
    platform,
}: {
    gnosisState: GnosisPayAccountOnboardedState
    platform: ZealMobilePlatform
}): Promise<
    Result<
        {
            type: 'wallet_not_installed'
            gnosisState: GnosisPayAccountOnboardedState
        },
        {
            gnosisState: GnosisPayAccountOnboardedState
            platform: ZealMobilePlatform
        }
    >
> => {
    const wallet = await fetchWalletAppInstalled()
    switch (wallet.type) {
        case 'wallet_app_installed':
            return success({ gnosisState, platform })
        case 'wallet_app_not_installed':
            return failure({ type: 'wallet_not_installed', gnosisState })

        /* istanbul ignore next */
        default:
            return notReachable(wallet)
    }
}

// FIXME :: @Nicvaniek - revert and accept terms on signup instead (after proper adoption of 0.3.15)
const createCardOrder = async ({
    sessionPassword,
    keyStore,
    cardConfig,
    networkRPCMap,
    networkMap,
    defaultCurrencyConfig,
    signal,
}: {
    cardConfig:
        | ReadonlySignerSelectedOnboardedCardConfig
        | ReadonlySignerSelectedCardConfig
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<GnosisPayStateResult> => {
    const cardReadOnlySigner = cardConfig.readonlySignerAddress

    const gnosisLoginInfo = await loginWithCache({
        sessionPassword,
        keyStore,
        readonlySignerAddress: cardReadOnlySigner,
        signal,
    })

    const termsResponse = await get(
        '/user/terms',
        {
            auth: { type: 'bearer_token', token: gnosisLoginInfo.token },
            query: undefined,
        },
        signal
    )

    const parsedTerms = string(termsResponse)
        .andThen(parseJSON)
        .andThen(object)
        .andThen((obj) =>
            arrayOf(obj.terms, (term) =>
                object(term).andThen((termObj) =>
                    shape({
                        type: string(termObj.type),
                        currentVersion: string(termObj.currentVersion),
                        accepted: boolean(termObj.accepted),
                    })
                )
            )
        )
        .getSuccessResultOrThrow('Failed to parse T&Cs response')

    const missingTerms = parsedTerms.filter(
        (term) => !term.accepted && term.type !== cashBackTermsConfig.id
    )

    if (!missingTerms.length) {
        return await createOrderAndReadGnosisPayState({
            sessionPassword,
            keyStore,
            cardReadOnlySigner,
            networkRPCMap,
            networkMap,
            defaultCurrencyConfig,
            signal,
        })
    }

    await Promise.all(
        missingTerms.map((term) =>
            post(
                '/user/terms',
                {
                    auth: {
                        type: 'bearer_token',
                        token: gnosisLoginInfo.token,
                    },
                    query: undefined,
                    body: {
                        terms: term.type,
                        version: term.currentVersion,
                    },
                },
                signal
            )
        )
    )

    return await createOrderAndReadGnosisPayState({
        sessionPassword,
        keyStore,
        cardReadOnlySigner,
        networkRPCMap,
        networkMap,
        defaultCurrencyConfig,
        signal,
    })
}

const deployEarnWithRechargePreferences = async ({
    earn,
    keyStore,
    cardReadOnlySigner,
    rechargePreferences,
    accountsMap,
    cardSafe,
    sessionPassword,
    networkRPCMap,
    networkMap,
    installationId,
    defaultCurrencyConfig,
}: {
    cardReadOnlySigner: Web3.address.Address
    cardSafe: CardSafeFullyConfigured
    rechargePreferences: RechargePreferences
    earn: Earn
    sessionPassword: string
    accountsMap: AccountsMap
    keyStore: Safe4337
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
}): Promise<void> => {
    const { configureEarnTransaction, configureRechargeTransaction } =
        createDeployEarnWithRechargePreferencesTransactions({
            cardReadOnlySigner,
            cardSafe,
            rechargePreferences,
            earn,
            accountsMap,
        })

    const owner = accountsMap[cardReadOnlySigner]

    // FIXME @resetko-zeal no error state handled?
    await submitAndMonitorSponsoredUserOperation({
        sessionPassword,
        account: owner,
        dApp: null,
        defaultCurrencyConfig,
        fetchSimulationByRequest: fetchSimulationByRequest,
        installationId,
        keyStore,
        network: EARN_NETWORK,
        networkMap,
        networkRPCMap,
        portfolio: null,
        rpcRequestsToBundle: [
            configureEarnTransaction,
            configureRechargeTransaction,
        ].filter(excludeNullValues),
        actionSource: {
            type: 'internal',
            transactionEventSource: 'earnDeployWithRechargePreferences',
        },
    })
}

const fetch = async ({
    cardConfig,
    earn,
    sessionPassword,
    keyStore,
    cardSafe,
    networkRPCMap,
    networkMap,
    defaultCurrencyConfig,
    installationId,
    accountsMap,
    signal,
}: {
    cardConfig:
        | ReadonlySignerSelectedOnboardedCardConfig
        | ReadonlySignerSelectedCardConfig
    cardSafe: CardSafeFullyConfigured
    earn: Earn
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    signal?: AbortSignal
}) => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_selected_fully_onboarded':
            return createCardOrder({
                sessionPassword,
                cardConfig,
                networkRPCMap,
                keyStore,
                defaultCurrencyConfig,
                networkMap,
                signal,
            })
        case 'card_readonly_signer_address_is_selected': {
            if (cardConfig.rechargePreferences) {
                switch (keyStore.type) {
                    case 'private_key_store':
                    case 'secret_phrase_key':
                        captureError(
                            new ImperativeError(
                                'Not safe 4337 key store detected during deploy earn with recharge preferences'
                            )
                        )
                        break
                    case 'safe_4337':
                        deployEarnWithRechargePreferences({
                            cardSafe,
                            cardReadOnlySigner:
                                cardConfig.readonlySignerAddress,
                            rechargePreferences: cardConfig.rechargePreferences,
                            earn,
                            sessionPassword,
                            keyStore,
                            networkRPCMap,
                            networkMap,
                            accountsMap,
                            installationId,
                            defaultCurrencyConfig,
                        }).catch((error) => {
                            captureError(
                                new Error(
                                    'Failed to deploy earn with recharge preferences during card activation',
                                    error
                                )
                            )
                        })
                        break
                    /* istanbul ignore next */
                    default:
                        return notReachable(keyStore)
                }
            }
            return createCardOrder({
                sessionPassword,
                cardConfig,
                networkRPCMap,
                keyStore,
                defaultCurrencyConfig,
                networkMap,
                signal,
            })
        }

        /* istanbul ignore next */
        default:
            return notReachable(cardConfig)
    }
}

export const CreateVirtualCardOrder = ({
    sessionPassword,
    keyStore,
    installationId,
    networkRPCMap,
    networkMap,
    defaultCurrencyConfig,
    cardConfig,
    cardSafe,
    earn,
    accountsMap,
    location,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            sessionPassword,
            keyStore,
            cardConfig,
            cardSafe,
            earn,
            accountsMap,
            installationId,
            networkMap,
            networkRPCMap,
            defaultCurrencyConfig,
        },
    })

    useEffect(() => {
        postUserEvent({
            type: 'VirtualCardCreationEnteredEvent',
            installationId,
            location,
        })
    }, [installationId, location])

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                postUserEvent({
                    type: 'VirtualCardCreatedEvent',
                    installationId,
                    location,
                })
                logAppsflyerEvent({
                    type: 'af_card_created',
                    cardType: 'virtual',
                })
                logAppsflyerEventOncePerInstallation({
                    type: 'af_card_created_first_time',
                    cardType: 'virtual',
                })
                break
            case 'loading':
            case 'error':
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [installationId, loadable, location])

    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    title={
                        <FormattedMessage
                            id="virtual-card-order.create-order.loading-text"
                            defaultMessage="Activating card"
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )

        case 'error':
            return (
                <>
                    <LoadingLayout
                        actionBar={
                            <ActionBar
                                left={
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => onMsg({ type: 'close' })}
                                    >
                                        {({ color }) => (
                                            <BackIcon size={24} color={color} />
                                        )}
                                    </IconButton>
                                }
                            />
                        }
                        title={
                            <FormattedMessage
                                id="virtual-card-order.create-order.loading-text"
                                defaultMessage="Activating card"
                            />
                        }
                        onClose={() => onMsg({ type: 'close' })}
                    />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        case 'loaded':
            return (
                <SuccessLayout
                    title={
                        <FormattedMessage
                            id="virtual-card-order.create-order.success-text"
                            defaultMessage="Card activated"
                        />
                    }
                    onAnimationComplete={() => {
                        onMsg({
                            type: 'on_virtual_card_order_created_animation_completed',
                            gnosisPayStateResult: loadable.data,
                        })
                    }}
                />
            )
        default:
            return notReachable(loadable)
    }
}
