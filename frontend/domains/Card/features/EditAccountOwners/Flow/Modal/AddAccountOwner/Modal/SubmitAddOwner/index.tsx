import { useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { SuccessLayout } from '@zeal/uikit/SuccessLayout'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
} from '@zeal/domains/Card'
import { GnosisPayReadOnlySigner } from '@zeal/domains/Card/api/fetchGnosisPayReadOnlySigners'
import { DELAY_MODULE_ABI } from '@zeal/domains/Card/constants'
import { SendDelayRelayTransaction } from '@zeal/domains/Card/features/SendDelayRelayTransaction'
import { predictDelayModAddress } from '@zeal/domains/Card/helpers/predictDelayModAddress'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import {
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
} from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { MetaTransactionData, OperationType } from '@zeal/domains/UserOperation'

import { AddReadOnlySigner } from './AddReadOnlySigner'
import { Confirmation } from './Confirmation'

type Props = {
    cardOwner: Account
    currentReadOnlySigners: GnosisPayReadOnlySigner[]
    keyStore: CardSlientSignKeyStore
    ownerToAdd: Account
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    network: PredefinedNetwork
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_add_card_owner_queued_successfully' }
    | Extract<
          MsgOf<typeof SendDelayRelayTransaction>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >

type State =
    | { type: 'confirmation' }
    | { type: 'add_read_only_signer' }
    | {
          type: 'submit_delay_relay_transaction'
          addOwnerTransaction: MetaTransactionData
      }
    | { type: 'success_layout' }

const createAddOwnerTransaction = ({
    ownerToAdd,
    cardSafeAddress,
}: {
    cardSafeAddress: Web3.address.Address
    ownerToAdd: Web3.address.Address
}): MetaTransactionData => ({
    to: predictDelayModAddress(cardSafeAddress),
    value: '0x0',
    operation: OperationType.Call,
    data: Web3.abi.encodeFunctionData({
        abi: DELAY_MODULE_ABI,
        functionName: 'enableModule',
        args: [ownerToAdd],
    }),
})

export const SubmitAddOwner = ({
    onMsg,
    ownerToAdd,
    keyStoreMap,
    accountsMap,
    gnosisPayAccountOnboardedState,
    network,
    networkMap,
    networkRPCMap,
    portfolioMap,
    currentReadOnlySigners,
    feePresetMap,
    gasCurrencyPresetMap,
    keyStore,
    cardOwner,
    sessionPassword,
    installationId,
    defaultCurrencyConfig,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'confirmation' })

    switch (state.type) {
        case 'confirmation':
            return (
                <Confirmation
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_continue_clicked': {
                                if (
                                    currentReadOnlySigners.some(
                                        (signer) =>
                                            signer.address ===
                                            ownerToAdd.address
                                    )
                                ) {
                                    setState({
                                        type: 'submit_delay_relay_transaction',
                                        addOwnerTransaction:
                                            createAddOwnerTransaction({
                                                ownerToAdd: ownerToAdd.address,
                                                cardSafeAddress:
                                                    gnosisPayAccountOnboardedState
                                                        .cardSafe.address,
                                            }),
                                    })
                                    break
                                }
                                setState({
                                    type: 'add_read_only_signer',
                                })
                                break
                            }

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'add_read_only_signer':
            return (
                <AddReadOnlySigner
                    signerToAdd={ownerToAdd}
                    sessionPassword={sessionPassword}
                    connectedCardReadOnlySigner={cardOwner}
                    keyStore={keyStore}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_readonly_signer_added':
                                setState({
                                    type: 'submit_delay_relay_transaction',
                                    addOwnerTransaction:
                                        createAddOwnerTransaction({
                                            ownerToAdd: ownerToAdd.address,
                                            cardSafeAddress:
                                                gnosisPayAccountOnboardedState
                                                    .cardSafe.address,
                                        }),
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'submit_delay_relay_transaction':
            return (
                <SendDelayRelayTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    transaction={state.addOwnerTransaction}
                    cardSafeAddress={
                        gnosisPayAccountOnboardedState.cardSafe.address
                    }
                    cardOwner={cardOwner}
                    keyStore={keyStore}
                    network={network}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_submit_delay_relay_transaction_success':
                                setState({ type: 'success_layout' })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'success_layout':
            return (
                <SuccessLayout
                    title={
                        <FormattedMessage
                            id="card.add-owner.queued"
                            defaultMessage="Add owner queued"
                        />
                    }
                    onAnimationComplete={() =>
                        onMsg({
                            type: 'on_add_card_owner_queued_successfully',
                        })
                    }
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
