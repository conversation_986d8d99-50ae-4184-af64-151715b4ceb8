import { useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { SuccessLayout } from '@zeal/uikit/SuccessLayout'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
} from '@zeal/domains/Card'
import { GnosisPayReadOnlySigner } from '@zeal/domains/Card/api/fetchGnosisPayReadOnlySigners'
import { DELAY_MODULE_ABI } from '@zeal/domains/Card/constants'
import { SendDelayRelayTransaction } from '@zeal/domains/Card/features/SendDelayRelayTransaction'
import { predictDelayModAddress } from '@zeal/domains/Card/helpers/predictDelayModAddress'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import {
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
} from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { MetaTransactionData, OperationType } from '@zeal/domains/UserOperation'

import { Confirmation } from './Confirmation'
import { RemoveReadOnlySigner } from './RemoveReadOnlySigner'

type Props = {
    ownerAddressToRemove: Web3.address.Address
    currentReadOnlySigners: GnosisPayReadOnlySigner[]
    currentOwners: Web3.address.Address[]
    cardOwner: Account
    keyStore: CardSlientSignKeyStore

    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    network: PredefinedNetwork
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_remove_card_owner_queued_successfully' }
    | Extract<
          MsgOf<typeof SendDelayRelayTransaction>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >

type State =
    | { type: 'confirmation' }
    | {
          type: 'remove_read_only_signer'
          readOnlySignerToRemove: GnosisPayReadOnlySigner
      }
    | {
          type: 'submit_delay_relay_transaction'
          removeOwnerTransaction: MetaTransactionData
      }
    | { type: 'success_layout' }

const FIRST_ADDRESS = Web3.address.staticFromString(
    '0x0000000000000000000000000000000000000001'
)

const createRemoveOwnerTransaction = ({
    cardSafeAddress,
    ownerToRemove,
    currentOwners,
}: {
    cardSafeAddress: Web3.address.Address
    ownerToRemove: Web3.address.Address
    currentOwners: Web3.address.Address[]
}): MetaTransactionData => {
    const previousOwnerIndexInList =
        currentOwners.findIndex((owner) => owner === ownerToRemove) - 1
    const previousOwnerAddressInList =
        previousOwnerIndexInList >= 0
            ? currentOwners[previousOwnerIndexInList]
            : FIRST_ADDRESS

    return {
        to: predictDelayModAddress(cardSafeAddress),
        value: '0x0',
        operation: OperationType.Call,
        data: Web3.abi.encodeFunctionData({
            abi: DELAY_MODULE_ABI,
            functionName: 'disableModule',
            args: [previousOwnerAddressInList, ownerToRemove],
        }),
    }
}

export const RemoveAccountOwner = ({
    onMsg,
    currentOwners,
    ownerAddressToRemove,
    keyStoreMap,
    accountsMap,
    gnosisPayAccountOnboardedState,
    network,
    networkMap,
    networkRPCMap,
    portfolioMap,
    currentReadOnlySigners,
    feePresetMap,
    gasCurrencyPresetMap,
    keyStore,
    cardOwner,
    sessionPassword,
    installationId,
    defaultCurrencyConfig,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'confirmation' })

    switch (state.type) {
        case 'confirmation':
            return (
                <Confirmation
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_continue_clicked':
                                const readOnlySignerToRemove =
                                    currentReadOnlySigners.find(
                                        (readOnlySigner) =>
                                            readOnlySigner.address ===
                                            ownerAddressToRemove
                                    )
                                if (readOnlySignerToRemove) {
                                    setState({
                                        type: 'remove_read_only_signer',
                                        readOnlySignerToRemove,
                                    })
                                    break
                                }

                                setState({
                                    type: 'submit_delay_relay_transaction',
                                    removeOwnerTransaction:
                                        createRemoveOwnerTransaction({
                                            ownerToRemove: ownerAddressToRemove,
                                            currentOwners,
                                            cardSafeAddress:
                                                gnosisPayAccountOnboardedState
                                                    .cardSafe.address,
                                        }),
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'remove_read_only_signer':
            return (
                <RemoveReadOnlySigner
                    installationId={installationId}
                    signerToRemove={state.readOnlySignerToRemove}
                    sessionPassword={sessionPassword}
                    keyStore={keyStore}
                    connectedCardReadOnlySigner={cardOwner}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_readonly_signer_removed':
                                setState({
                                    type: 'submit_delay_relay_transaction',
                                    removeOwnerTransaction:
                                        createRemoveOwnerTransaction({
                                            ownerToRemove: ownerAddressToRemove,
                                            currentOwners,
                                            cardSafeAddress:
                                                gnosisPayAccountOnboardedState
                                                    .cardSafe.address,
                                        }),
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'submit_delay_relay_transaction':
            return (
                <SendDelayRelayTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    transaction={state.removeOwnerTransaction}
                    cardSafeAddress={
                        gnosisPayAccountOnboardedState.cardSafe.address
                    }
                    cardOwner={cardOwner}
                    keyStore={keyStore}
                    network={network}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_submit_delay_relay_transaction_success':
                                setState({ type: 'success_layout' })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'success_layout':
            return (
                <SuccessLayout
                    title={
                        <FormattedMessage
                            id="card.remove-owner.queued"
                            defaultMessage="Remove owner queued"
                        />
                    }
                    onAnimationComplete={() =>
                        onMsg({
                            type: 'on_remove_card_owner_queued_successfully',
                        })
                    }
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
