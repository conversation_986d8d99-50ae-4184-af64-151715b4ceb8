import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Chain } from '@zeal/uikit/Chain'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldDisconnected } from '@zeal/uikit/Icon/BoldDisconnected'
import { BoldSwap } from '@zeal/uikit/Icon/BoldSwap'
import { InfoCircleOutline } from '@zeal/uikit/Icon/InfoCircleOutline'
import { Key } from '@zeal/uikit/Icon/Key'
import { QrCode } from '@zeal/uikit/Icon/QrCode'
import { ListItem } from '@zeal/uikit/ListItem'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { isDevelopment, isLocal } from '@zeal/toolkit/Environment'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { DelayQueueState } from '@zeal/domains/Card/api/fetchDelayQueueState'
import { SpendLimit } from '@zeal/domains/Card/domains/SpendLimit/features/SpendLimit'
import { formatCardNumber } from '@zeal/domains/Card/helpers/formatCardNumber'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { Earn } from '@zeal/domains/Earn'
import { IntercomButton } from '@zeal/domains/Intercom/features/IntercomButton'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { ActivatePhysicalCardWidget } from './ActivatePhysicalCardWidget'
import { AddToWalletListItem } from './AddToWalletListItem'
import { CardNotificationsListItem } from './CardNotificationsListItem'
import { EditOwnersListItem } from './EditOwnersListItem'
import { GetAnotherCardListItem } from './GetAnotherCardListItem'
import { SetRechargeListItem } from './SetRechargeListItem'
import { ViewPinListItem } from './ViewPinListItem'

type Props = {
    cardReadonlySigner: Account
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    earn: Earn
    keyStore: CardSlientSignKeyStore
    installationId: string
    delayQueueStatePollable: PollableData<DelayQueueState, unknown>
    notificationsConfig: NotificationsConfig
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currencyHiddenMap: CurrencyHiddenMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    keyStoreMap: KeyStoreMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    encryptedPassword: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_card_reveal_pin_clicked' }
    | { type: 'close' }
    | { type: 'on_disconnect_card_clicked' }
    | { type: 'on_show_card_address_clicked' }
    | { type: 'on_switch_card_clicked' }
    | MsgOf<typeof CardNotificationsListItem>
    | MsgOf<typeof SpendLimit>
    | MsgOf<typeof EditOwnersListItem>
    | MsgOf<typeof GetAnotherCardListItem>
    | MsgOf<typeof AddToWalletListItem>
    | MsgOf<typeof SetRechargeListItem>
    | MsgOf<typeof ActivatePhysicalCardWidget>
    | MsgOf<typeof ViewPinListItem>

export const Layout = ({
    cardReadonlySigner,
    cardConfig,
    earn,
    notificationsConfig,
    keyStore,
    installationId,
    gnosisPayAccountOnboardedState,
    networkMap,
    networkRPCMap,
    accountsMap,
    currencyHiddenMap,
    gasCurrencyPresetMap,
    portfolioMap,
    keyStoreMap,
    feePresetMap,
    sessionPassword,
    delayQueueStatePollable,
    defaultCurrencyConfig,
    encryptedPassword,
    onMsg,
}: Props) => {
    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => {
                onMsg({ type: 'close' })
            }}
        >
            <Column spacing={12} fill>
                <ActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <Text
                                    variant="title3"
                                    weight="semi_bold"
                                    color="textPrimary"
                                >
                                    <FormattedMessage
                                        id="card.settings.page.title"
                                        defaultMessage="Card Settings"
                                    />
                                </Text>
                            </Row>
                        </Clickable>
                    }
                    right={
                        <IntercomButton
                            variant="icon_button"
                            installationId={installationId}
                            location="card_settings"
                        />
                    }
                />
                <ScrollContainer withFloatingActions={false}>
                    <Column spacing={8} fill>
                        <ActivatePhysicalCardWidget
                            gnosisPayAccountOnboardedState={
                                gnosisPayAccountOnboardedState
                            }
                            onMsg={onMsg}
                        />

                        <Group variant="default">
                            <ListItem
                                size="regular"
                                aria-current={false}
                                avatar={({ size }) => (
                                    <QrCode size={size} color="teal40" />
                                )}
                                primaryText={
                                    <FormattedMessage
                                        id="card.settings.show-card-address"
                                        defaultMessage="Show card address"
                                    />
                                }
                                shortText={Web3.address.format(
                                    gnosisPayAccountOnboardedState.cardSafe
                                        .address
                                )}
                                onClick={() =>
                                    onMsg({
                                        type: 'on_show_card_address_clicked',
                                    })
                                }
                            />
                        </Group>
                        <Group variant="default">
                            <SetRechargeListItem
                                gnosisPayAccountOnboardedState={
                                    gnosisPayAccountOnboardedState
                                }
                                earn={earn}
                                onMsg={onMsg}
                            />

                            <SpendLimit
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                keyStore={keyStore}
                                delayQueueStatePollable={
                                    delayQueueStatePollable
                                }
                                networkMap={networkMap}
                                feePresetMap={feePresetMap}
                                gasCurrencyPresetMap={gasCurrencyPresetMap}
                                installationId={installationId}
                                portfolioMap={portfolioMap}
                                sessionPassword={sessionPassword}
                                keyStoreMap={keyStoreMap}
                                accountsMap={accountsMap}
                                gnosisPayAccountOnboardedState={
                                    gnosisPayAccountOnboardedState
                                }
                                networkRPCMap={networkRPCMap}
                                cardReadonlySigner={cardReadonlySigner}
                                onMsg={onMsg}
                            />
                            {isDevelopment() || isLocal() ? (
                                <ViewPinListItem
                                    encryptedPassword={encryptedPassword}
                                    installationId={installationId}
                                    gnosisPayAccountOnboardedState={
                                        gnosisPayAccountOnboardedState
                                    }
                                    readonlySignerAccount={cardReadonlySigner}
                                    keyStore={keyStore}
                                    onMsg={onMsg}
                                />
                            ) : (
                                (() => {
                                    const selectedCard =
                                        gnosisPayAccountOnboardedState.selectedCard
                                    switch (selectedCard.type) {
                                        case 'virtual':
                                            return null
                                        case 'physical':
                                            switch (selectedCard.state.status) {
                                                case 'cancelled':
                                                    return null
                                                case 'frozen':
                                                case 'active':
                                                    return (
                                                        <ListItem
                                                            size="regular"
                                                            aria-current={false}
                                                            avatar={({
                                                                size,
                                                            }) => (
                                                                <Key
                                                                    size={size}
                                                                    color="iconAccent2"
                                                                />
                                                            )}
                                                            primaryText={
                                                                <FormattedMessage
                                                                    id="card.settings.view-pin"
                                                                    defaultMessage="View PIN"
                                                                />
                                                            }
                                                            shortText={
                                                                <FormattedMessage
                                                                    id="card.settings.view-pin-description"
                                                                    defaultMessage="Always protect your PIN"
                                                                />
                                                            }
                                                            side={{
                                                                rightIcon: ({
                                                                    size,
                                                                }) => (
                                                                    <InfoCircleOutline
                                                                        size={
                                                                            size
                                                                        }
                                                                        color="iconDefault"
                                                                    />
                                                                ),
                                                            }}
                                                            onClick={() => {
                                                                onMsg({
                                                                    type: 'on_card_reveal_pin_clicked',
                                                                })
                                                            }}
                                                        />
                                                    )
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(
                                                        selectedCard.state
                                                            .status
                                                    )
                                            }
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(selectedCard)
                                    }
                                })()
                            )}

                            {(() => {
                                switch (ZealPlatform.OS) {
                                    case 'ios':
                                    case 'android':
                                        return (
                                            <CardNotificationsListItem
                                                installationId={installationId}
                                                notificationsConfig={
                                                    notificationsConfig
                                                }
                                                onMsg={onMsg}
                                            />
                                        )
                                    case 'web':
                                        return null

                                    default:
                                        return notReachable(ZealPlatform)
                                }
                            })()}
                        </Group>

                        <Group variant="default">
                            {(() => {
                                switch (
                                    gnosisPayAccountOnboardedState.kycStatus
                                ) {
                                    case 'not_started':
                                    case 'documents_requested':
                                    case 'verification_in_progress':
                                    case 'resubmission_requested':
                                    case 'failed':
                                        return null
                                    case 'approved':
                                        return (
                                            <GetAnotherCardListItem
                                                gnosisPayState={
                                                    gnosisPayAccountOnboardedState
                                                }
                                                defaultCurrencyConfig={
                                                    defaultCurrencyConfig
                                                }
                                                accountsMap={accountsMap}
                                                networkMap={networkMap}
                                                networkRPCMap={networkRPCMap}
                                                sessionPassword={
                                                    sessionPassword
                                                }
                                                cardConfig={cardConfig}
                                                earn={earn}
                                                keyStore={keyStore}
                                                installationId={installationId}
                                                onMsg={onMsg}
                                            />
                                        )
                                    default:
                                        return notReachable(
                                            gnosisPayAccountOnboardedState.kycStatus
                                        )
                                }
                            })()}

                            <AddToWalletListItem
                                gnosisPayAccountOnboardedState={
                                    gnosisPayAccountOnboardedState
                                }
                                onMsg={onMsg}
                                installationId={installationId}
                            />

                            <EditOwnersListItem
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                gnosisPayAccountOnboardedState={
                                    gnosisPayAccountOnboardedState
                                }
                                delayQueueStatePollable={
                                    delayQueueStatePollable
                                }
                                networkRPCMap={networkRPCMap}
                                installationId={installationId}
                                cardReadonlySigner={cardReadonlySigner}
                                accountsMap={accountsMap}
                                keyStoreMap={keyStoreMap}
                                currencyHiddenMap={currencyHiddenMap}
                                keyStore={keyStore}
                                networkMap={networkMap}
                                feePresetMap={feePresetMap}
                                gasCurrencyPresetMap={gasCurrencyPresetMap}
                                portfolioMap={portfolioMap}
                                sessionPassword={sessionPassword}
                                onMsg={onMsg}
                            />

                            {gnosisPayAccountOnboardedState.activatedCards
                                .length > 1 && (
                                <ListItem
                                    size="regular"
                                    aria-current={false}
                                    avatar={({ size }) => (
                                        <BoldSwap size={size} color="teal40" />
                                    )}
                                    primaryText={
                                        <FormattedMessage
                                            id="card.settings.switch-active-card"
                                            defaultMessage="Switch active card"
                                        />
                                    }
                                    shortText={
                                        <FormattedMessage
                                            id="card.settings.switch-active-card-description"
                                            defaultMessage="Active card: {card}"
                                            values={{
                                                card: formatCardNumber(
                                                    gnosisPayAccountOnboardedState
                                                        .selectedCard
                                                        .lastFourDigits
                                                ),
                                            }}
                                        />
                                    }
                                    onClick={() =>
                                        onMsg({
                                            type: 'on_switch_card_clicked',
                                        })
                                    }
                                />
                            )}
                            <ListItem
                                size="regular"
                                aria-current={false}
                                avatar={({ size }) => (
                                    <BoldDisconnected
                                        size={size}
                                        color="teal40"
                                    />
                                )}
                                primaryText={
                                    <FormattedMessage
                                        id="card.settings.disconnect-from-zeal"
                                        defaultMessage="Disconnect from Zeal"
                                    />
                                }
                                shortText={
                                    <Chain>
                                        {cardReadonlySigner.label}
                                        {Web3.address.format(
                                            cardReadonlySigner.address
                                        )}
                                    </Chain>
                                }
                                onClick={() =>
                                    onMsg({
                                        type: 'on_disconnect_card_clicked',
                                    })
                                }
                            />
                        </Group>
                    </Column>
                </ScrollContainer>
            </Column>
        </Screen>
    )
}
