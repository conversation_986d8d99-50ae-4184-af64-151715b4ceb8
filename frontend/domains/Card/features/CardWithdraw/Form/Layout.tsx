import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { ListItem } from '@zeal/uikit/ListItem'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Skeleton } from '@zeal/uikit/Skeleton'
import { Spacer } from '@zeal/uikit/Spacer'
import { Text } from '@zeal/uikit/Text'

import { noop } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { notReachable } from '@zeal/toolkit/notReachable'

import { AvatarWithoutBadge } from '@zeal/domains/Account/components/Avatar'
import { CopyAddress } from '@zeal/domains/Address/components/CopyAddress'
import { GnosisPayAccountOnboardedState } from '@zeal/domains/Card'
import { DelayQueueState } from '@zeal/domains/Card/api/fetchDelayQueueState'
import { DelayQueueBusyBanner } from '@zeal/domains/Card/components/DelayQueueBusyBanner'
import { DelayQueueFailedBanner } from '@zeal/domains/Card/components/DelayQueueFailedBanner'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { MaxButton } from '@zeal/domains/Currency/components/MaxButton'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { useMoneyFormat } from '@zeal/domains/Money/hooks/useMoneyFormat'
import { NetworkMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { MetaTransactionData } from '@zeal/domains/UserOperation'

import { getWithdrawAmount, Pollable, validate } from './validate'

type Props = {
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    installationId: string
    networkMap: NetworkMap
    pollable: Pollable
    delayQueueStatePollable: PollableData<DelayQueueState, unknown>
    portfolio: ServerPortfolio2
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_amount_change'; amount: string | null }
    | {
          type: 'on_currency_click'
      }
    | {
          type: 'on_wallet_click'
      }
    | {
          type: 'on_submit_click'
          transaction: MetaTransactionData
      }

export const Layout = ({
    gnosisPayAccountOnboardedState,
    pollable,
    delayQueueStatePollable,
    networkMap,
    installationId,
    portfolio,
    onMsg,
}: Props) => {
    const { formatMessage } = useIntl()
    const { formatForInputs } = useMoneyFormat()
    const balance = getBalanceByCryptoCurrency2({
        currency: pollable.params.currency,
        serverPortfolio: portfolio,
    })
    const validationResult = validate({
        gnosisPayAccountOnboardedState,
        portfolio,
        pollable,
        delayQueueStatePollable,
        networkMap,
    })
    const errors = validationResult.getFailureReason() || {}

    const from = getWithdrawAmount({ pollable })
    const isMaxAmount = from.amount === balance.amount

    const formattedAmountForInput = formatForInputs({
        money: from,
    })

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <Clickable onClick={() => onMsg({ type: 'close' })}>
                        <Row spacing={4}>
                            <BackIcon size={24} color="iconDefault" />
                            <ActionBar.Header>
                                <FormattedMessage
                                    id="cardWithdraw.header"
                                    defaultMessage="Withdraw from card"
                                />
                            </ActionBar.Header>
                        </Row>
                    </Clickable>
                }
            />

            <Column spacing={4}>
                <AmountInput
                    content={{
                        topLeft: (
                            <IconButton
                                variant="on_light"
                                onClick={() => {
                                    onMsg({
                                        type: 'on_currency_click',
                                    })
                                }}
                            >
                                {({ color }) => (
                                    <Row spacing={4}>
                                        <CurrencyAvatar
                                            key={pollable.params.currency.id}
                                            currency={pollable.params.currency}
                                            size={24}
                                            rightBadge={({ size }) => (
                                                <Badge
                                                    size={size}
                                                    network={findNetworkByHexChainId(
                                                        pollable.params.currency
                                                            .networkHexChainId,
                                                        networkMap
                                                    )}
                                                />
                                            )}
                                        />
                                        <Text
                                            variant="title3"
                                            color="textPrimary"
                                            weight="medium"
                                        >
                                            {pollable.params.currency.code}
                                        </Text>
                                        <LightArrowDown2
                                            size={18}
                                            color={color}
                                        />
                                    </Row>
                                )}
                            </IconButton>
                        ),
                        topRight: ({ onBlur, onFocus, isFocused }) => (
                            <AmountInput.Input
                                prefix=""
                                onFocus={onFocus}
                                onBlur={onBlur}
                                label={formatMessage({
                                    id: 'cardWithdraw.amount',
                                    defaultMessage: 'Withdraw amount',
                                })}
                                fraction={pollable.params.currency.fraction}
                                autoFocus
                                readOnly={false}
                                amount={
                                    !isFocused && isMaxAmount
                                        ? formattedAmountForInput
                                        : pollable.params.amount
                                }
                                onChange={(value) => {
                                    onMsg({
                                        type: 'on_amount_change',
                                        amount: value,
                                    })
                                }}
                                onSubmitEditing={noop}
                            />
                        ),

                        bottomLeft: (
                            <MaxButton
                                installationId={installationId}
                                location="card_withdrawal"
                                balance={balance}
                                onMsg={onMsg}
                                state={errors.amount ? 'error' : 'normal'}
                            />
                        ),
                        bottomRight: (() => {
                            switch (pollable.type) {
                                case 'loaded':
                                case 'reloading':
                                case 'subsequent_failed':
                                    return pollable.data ? (
                                        <Text
                                            variant="footnote"
                                            color="textSecondary"
                                            weight="regular"
                                        >
                                            <FormattedMoneyPrecise
                                                withSymbol
                                                sign={null}
                                                money={applyRate2({
                                                    baseAmount: {
                                                        amount: fromFixedWithFraction(
                                                            pollable.params
                                                                .amount,
                                                            pollable.params
                                                                .currency
                                                                .fraction
                                                        ),
                                                        currency:
                                                            pollable.params
                                                                .currency,
                                                    },
                                                    rate: pollable.data,
                                                })}
                                            />
                                        </Text>
                                    ) : null
                                case 'loading':
                                    return (
                                        <Skeleton
                                            width={40}
                                            height={16}
                                            variant="default"
                                        />
                                    )
                                case 'error':
                                    return null

                                default:
                                    return notReachable(pollable)
                            }
                        })(),
                    }}
                    state={errors.amount ? 'error' : 'normal'}
                />

                <NextStepSeparator />

                <Group variant="default">
                    <ListItem
                        size="large"
                        onClick={() => onMsg({ type: 'on_wallet_click' })}
                        avatar={({ size }) => (
                            <AvatarWithoutBadge
                                account={pollable.params.to}
                                size={size}
                            />
                        )}
                        primaryText={pollable.params.to.label}
                        shortText={
                            <CopyAddress
                                location="send"
                                installationId={installationId}
                                size="small"
                                color="on_light"
                                address={pollable.params.to.address}
                            />
                        }
                        aria-current={false}
                        side={{
                            rightIcon: ({ size }) => (
                                <LightArrowDown2
                                    size={size}
                                    color="iconDefault"
                                />
                            ),
                        }}
                    />
                </Group>
            </Column>

            <Spacer />

            <Column spacing={12}>
                {(() => {
                    if (!errors.banner) {
                        return null
                    }

                    switch (errors.banner.type) {
                        case 'delay_queue_busy':
                            return <DelayQueueBusyBanner />
                        case 'delay_queue_failed':
                            return <DelayQueueFailedBanner />

                        case 'pollable_not_loaded':
                            return null

                        default:
                            return notReachable(errors.banner)
                    }
                })()}

                <Actions variant="default">
                    <Button
                        size="regular"
                        variant="secondary"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        <FormattedMessage
                            id="actions.cancel"
                            defaultMessage="Cancel"
                        />
                    </Button>

                    <Button
                        size="regular"
                        variant="primary"
                        disabled={!!errors.submit}
                        onClick={() =>
                            validationResult.tap((transaction) =>
                                onMsg({
                                    type: 'on_submit_click',
                                    transaction,
                                })
                            )
                        }
                    >
                        <FormattedMessage
                            id="cashback.withdrawal.withdraw"
                            defaultMessage="Withdraw"
                        />
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}
