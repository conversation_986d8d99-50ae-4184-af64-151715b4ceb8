import { notReachable } from '@zeal/toolkit'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { SignMessage } from '@zeal/domains/Card/features/SignMessage'
import { createCardRelayTransactionMessage } from '@zeal/domains/Card/helpers/createCardSafeDelayModuleTransactionMessage'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import {
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
} from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { EthSignTypedDataV4 } from '@zeal/domains/RPCRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { MetaTransactionData } from '@zeal/domains/UserOperation'

type Props = {
    transaction: MetaTransactionData
    cardOwner: Account
    keyStore: SigningKeyStore
    network: PredefinedNetwork

    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_relay_transaction_message_signed'
          relayTransaction: MetaTransactionData
          relayMessage: EthSignTypedDataV4
          relaySalt: Hexadecimal.Hexadecimal
          signature: Hexadecimal.Hexadecimal
      }
    | Extract<
          MsgOf<typeof SignMessage>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'close'
          }
      >
export const SignRelayTransactionMessage = ({
    onMsg,
    cardOwner,
    networkRPCMap,
    portfolioMap,
    transaction,
    gasCurrencyPresetMap,
    network,
    networkMap,
    feePresetMap,
    keyStoreMap,
    keyStore,
    sessionPassword,
    accountsMap,
    installationId,
    defaultCurrencyConfig,
}: Props) => {
    const { message, salt } = createCardRelayTransactionMessage({
        network,
        transaction,
    })

    return (
        <SignMessage
            defaultCurrencyConfig={defaultCurrencyConfig}
            cardOwner={cardOwner}
            keyStore={keyStore}
            messageToSign={message}
            network={network}
            networkMap={networkMap}
            networkRPCMap={networkRPCMap}
            accountsMap={accountsMap}
            feePresetMap={feePresetMap}
            gasCurrencyPresetMap={gasCurrencyPresetMap}
            installationId={installationId}
            keyStoreMap={keyStoreMap}
            portfolioMap={portfolioMap}
            sessionPassword={sessionPassword}
            onMsg={(msg) => {
                switch (msg.type) {
                    case 'on_message_signed':
                        onMsg({
                            type: 'on_relay_transaction_message_signed',
                            relayTransaction: transaction,
                            relayMessage: message,
                            relaySalt: salt,
                            signature: msg.signature as Hexadecimal.Hexadecimal, // FIXME :: @mike can we change signMessage to return Hexadecimal?
                        })
                        break
                    case 'on_4337_auto_gas_token_selection_clicked':
                    case 'on_4337_gas_currency_selected':
                    case 'close':
                        onMsg(msg)
                        break
                    /* istanbul ignore next */
                    default:
                        return notReachable(msg)
                }
            }}
        />
    )
}
