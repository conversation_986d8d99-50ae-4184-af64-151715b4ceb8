import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'

import { submitBungeeIntentQuote } from '@zeal/domains/Currency/domains/Bungee/api/submitBungeeIntentQuote'
import { EARN_PRIMARY_INVESTMENT_ASSET_TO_TAKER_TYPE_MAP } from '@zeal/domains/Earn/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { signMessage } from '@zeal/domains/RPCRequest/helpers/signMessage'

import { LoadingLayout } from './LoadingLayout'

import {
    CardTopUpEOAQuote,
    CardTopUpSafeQuote,
    SubmittedEarnSignTypedDataCardTopUp,
} from '../../types'

type Props = {
    quote:
        | Extract<
              CardTopUpEOAQuote,
              {
                  type:
                      | 'earn_eoa_sign_typed_data'
                      | 'earn_eoa_sign_typed_data_with_approval'
              }
          >
        | Extract<
              CardTopUpSafeQuote,
              {
                  type: 'earn_safe_sign_typed_data_with_optional_approval'
              }
          >

    fromAmount: CryptoMoney // FIXME :: @Nicvaniek see if we can use generics on request type to not have to pass this separately - just pass request

    networkMap: NetworkMap
    sessionPassword: string
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'on_card_top_up_success'
          submittedCardTopUp: SubmittedEarnSignTypedDataCardTopUp
      }

const fetch = async ({
    quote,
    fromAmount,
    networkMap,
    sessionPassword,
}: {
    quote:
        | Extract<
              CardTopUpEOAQuote,
              {
                  type:
                      | 'earn_eoa_sign_typed_data'
                      | 'earn_eoa_sign_typed_data_with_approval'
              }
          >
        | Extract<
              CardTopUpSafeQuote,
              {
                  type: 'earn_safe_sign_typed_data_with_optional_approval'
              }
          >
    sessionPassword: string
    networkMap: NetworkMap
    fromAmount: CryptoMoney
}): Promise<SubmittedEarnSignTypedDataCardTopUp> => {
    const network = findNetworkByHexChainId(
        fromAmount.currency.networkHexChainId,
        networkMap
    )

    const signedMessage = await signMessage({
        sessionPassword,
        request: quote.signatureRequest,
        keyStore: quote.keystore,
        network,
        dApp: null,
    })

    const requestHash = await submitBungeeIntentQuote({
        quoteId: quote.quoteId,
        quoteWiness: quote.witness,
        userSignature: signedMessage as Hexadecimal, // FIXME :: @mike can we change signMessage to return Hexadecimal?
        quoteRequestType: quote.quoteRequestType,
    })

    const takerType =
        EARN_PRIMARY_INVESTMENT_ASSET_TO_TAKER_TYPE_MAP[fromAmount.currency.id]

    if (!takerType) {
        throw new ImperativeError('Taker not found for currency', {
            currencyId: fromAmount.currency.id,
        })
    }

    return {
        type: 'earn_sign_typed_data',
        takerType,
        fromAmount,
        toAmount: quote.toAmount,
        startedAtMs: Date.now(),
        state: { type: 'waiting_for_swap' },
        quoteRequestHash: requestHash,
        actionSource: {
            type: 'internal',
            transactionEventSource: 'topupCardEarnSign',
        },
    }
}

export const SignSwapEarnStep = ({
    fromAmount,
    sessionPassword,
    quote,
    networkMap,
    installationId,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            sessionPassword,
            fromAmount,
            networkMap,
            quote,
        },
    })

    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_card_top_up_success',
                    submittedCardTopUp: loadable.data,
                })
                return
            case 'loading':
            case 'error':
                return
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveOnMsg])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return <LoadingLayout variant="step_2_of_2" />
        case 'error':
            return (
                <>
                    <LoadingLayout variant="step_2_of_2" />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: {
                                            fromAmount,
                                            sessionPassword,
                                            networkMap,
                                            quote,
                                        },
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
