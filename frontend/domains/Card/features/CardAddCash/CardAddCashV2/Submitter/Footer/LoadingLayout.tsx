import { FormattedMessage } from 'react-intl'

import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { CompressedButton } from '@zeal/uikit/Button'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Progress } from '@zeal/uikit/Progress'

import { notReachable } from '@zeal/toolkit'

type Props = {
    variant:
        | 'step_1_of_1'
        | 'step_1_of_2'
        | 'step_2_of_2'
        | 'step_1_of_3'
        | 'step_2_of_3'
        | 'step_3_of_3'
}

export const LoadingLayout = ({ variant }: Props) => {
    switch (variant) {
        case 'step_1_of_1':
            return (
                <Actions variant="default" direction="row">
                    <CompressedButton
                        size="regular"
                        variant="secondary"
                        disabled
                    >
                        {({ size, color }) => (
                            <BackIcon size={size} color={color} />
                        )}
                    </CompressedButton>
                    <Button size="regular" variant="primary" loading disabled />
                </Actions>
            )

        case 'step_1_of_2':
            return (
                <Actions variant="default" direction="row">
                    <CompressedButton
                        size="regular"
                        variant="secondary"
                        disabled
                    >
                        {({ size, color }) => (
                            <BackIcon size={size} color={color} />
                        )}
                    </CompressedButton>
                    <Progress
                        rounded
                        variant="neutral"
                        progress={60}
                        initialProgress={0}
                        title={
                            <FormattedMessage
                                id="transaction.inProgress"
                                defaultMessage="In progress"
                            />
                        }
                        subtitle={null}
                    />
                </Actions>
            )
        case 'step_1_of_3':
            return (
                <Actions variant="default" direction="row">
                    <CompressedButton
                        size="regular"
                        variant="secondary"
                        disabled
                    >
                        {({ size, color }) => (
                            <BackIcon size={size} color={color} />
                        )}
                    </CompressedButton>
                    <Progress
                        rounded
                        variant="neutral"
                        progress={30}
                        initialProgress={0}
                        title={
                            <FormattedMessage
                                id="transaction.inProgress"
                                defaultMessage="In progress"
                            />
                        }
                        subtitle={null}
                    />
                </Actions>
            )
        case 'step_2_of_3':
            return (
                <Actions variant="default" direction="row">
                    <CompressedButton
                        size="regular"
                        variant="secondary"
                        disabled
                    >
                        {({ size, color }) => (
                            <BackIcon size={size} color={color} />
                        )}
                    </CompressedButton>
                    <Progress
                        rounded
                        variant="neutral"
                        progress={60}
                        initialProgress={30}
                        title={
                            <FormattedMessage
                                id="transaction.inProgress"
                                defaultMessage="In progress"
                            />
                        }
                        subtitle={null}
                    />
                </Actions>
            )

        case 'step_2_of_2':
        case 'step_3_of_3':
            return (
                <Actions variant="default" direction="row">
                    <CompressedButton
                        size="regular"
                        variant="secondary"
                        disabled
                    >
                        {({ size, color }) => (
                            <BackIcon size={size} color={color} />
                        )}
                    </CompressedButton>

                    <Progress
                        rounded
                        variant="success"
                        progress={100}
                        initialProgress={60}
                        title={
                            <FormattedMessage
                                id="transaction.in-progress"
                                defaultMessage="In progress"
                            />
                        }
                        subtitle={null}
                    />
                </Actions>
            )

        /* istanbul ignore next */
        default:
            return notReachable(variant)
    }
}
