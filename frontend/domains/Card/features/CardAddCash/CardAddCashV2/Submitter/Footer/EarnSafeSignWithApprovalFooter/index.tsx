import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'

import { Approval } from './Approval'

import { CardTopUpSafeQuote } from '../../../types'
import { SignSwapEarnStep } from '../SignSwapEarnStep'

type Props = {
    quote: Extract<
        CardTopUpSafeQuote,
        { type: 'earn_safe_sign_typed_data_with_optional_approval' }
    >
    fromAmount: CryptoMoney
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof SignSwapEarnStep>
    | Extract<MsgOf<typeof Approval>, { type: 'close' }>

type State = { type: 'approval' } | { type: 'sign_swap' }

export const EarnSafeSignWithApprovalFooter = ({
    quote,
    onMsg,
    sessionPassword,
    networkRPCMap,
    fromAmount,
    networkMap,
    installationId,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'approval',
    })

    switch (state.type) {
        case 'approval':
            return (
                <Approval
                    installationId={installationId}
                    quote={quote}
                    sessionPassword={sessionPassword}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_approval_transaction_completed':
                                setState({ type: 'sign_swap' })
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'sign_swap':
            return (
                <SignSwapEarnStep
                    quote={quote}
                    fromAmount={fromAmount}
                    sessionPassword={sessionPassword}
                    networkMap={networkMap}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )
        default:
            return notReachable(state)
    }
}
