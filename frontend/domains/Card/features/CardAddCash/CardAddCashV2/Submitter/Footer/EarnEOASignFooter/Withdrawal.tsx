import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { fromBigInt } from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { NetworkRPCMap } from '@zeal/domains/Network'
import { submitAndMonitorTransaction } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/api/submitAndMonitorTransaction'
import { getSuggestedGasLimit } from '@zeal/domains/Transactions/helpers/getSuggestedGasLimit'

import { CardTopUpEOAQuote } from '../../../types'
import { LoadingLayout } from '../LoadingLayout'

type Props = {
    quote: Extract<
        CardTopUpEOAQuote,
        { type: 'earn_eoa_sign_typed_data_with_approval' }
    >
    fromAddress: Web3.address.Address
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' } | { type: 'on_withdrawal_transaction_completed' }

const fetch = async ({
    quote,
    fromAddress,
    sessionPassword,
    networkRPCMap,
    signal,
}: {
    quote: Extract<
        CardTopUpEOAQuote,
        { type: 'earn_eoa_sign_typed_data_with_approval' }
    >
    fromAddress: Web3.address.Address
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<void> => {
    const finishedTransaction = await submitAndMonitorTransaction({
        sendTransactionRequest: quote.withdrawalTransaction,
        nonce: quote.withdrawalFee.nonce,
        keyStore: quote.keystore,
        sessionPassword,
        network: EARN_NETWORK,
        networkRPCMap,
        fee: quote.withdrawalFee.fast,
        gas: getSuggestedGasLimit(fromBigInt(quote.withdrawalGasEstimate)),
        senderAddress: fromAddress,
        actionSource: {
            type: 'internal',
            transactionEventSource: 'topupCardEarnWithdraw',
        },
        signal,
    })

    switch (finishedTransaction.state) {
        case 'completed':
            return undefined
        case 'failed':
        case 'replaced':
            throw new ImperativeError(
                '[CardAddCash] EOA withdrawal transaction failed or replaced'
            )
        /* istanbul ignore next */
        default:
            return notReachable(finishedTransaction)
    }
}

export const Withdrawal = ({
    quote,
    fromAddress,
    sessionPassword,
    networkRPCMap,
    installationId,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            sessionPassword,
            networkRPCMap,
            fromAddress,
            quote,
        },
    })

    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_withdrawal_transaction_completed',
                })
                return
            case 'loading':
            case 'error':
                return
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveOnMsg])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return <LoadingLayout variant="step_1_of_3" />
        case 'error':
            return (
                <>
                    <LoadingLayout variant="step_1_of_3" />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: {
                                            fromAddress,
                                            sessionPassword,
                                            networkRPCMap,
                                            quote,
                                        },
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
