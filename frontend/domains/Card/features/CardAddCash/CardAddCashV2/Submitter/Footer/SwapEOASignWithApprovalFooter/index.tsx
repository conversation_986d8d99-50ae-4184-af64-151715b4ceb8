import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'

import { Approval } from './Approval'

import { CardTopUpEOAQuote } from '../../../types'
import { SignSwapStep } from '../SignSwapStep'

type Props = {
    quote: Extract<
        CardTopUpEOAQuote,
        { type: 'swap_eoa_sign_typed_data_with_approval' }
    >
    fromAmount: CryptoMoney
    fromAddress: Web3.address.Address
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof Approval>, { type: 'close' }>
    | MsgOf<typeof SignSwapStep>

type State = { type: 'approval' } | { type: 'sign_swap' }

export const SwapEOASignWithApprovalFooter = ({
    quote,
    onMsg,
    sessionPassword,
    networkRPCMap,
    fromAmount,
    fromAddress,
    installationId,
    networkMap,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'approval' })

    switch (state.type) {
        case 'approval':
            return (
                <Approval
                    quote={quote}
                    fromAddress={fromAddress}
                    fromAmount={fromAmount}
                    sessionPassword={sessionPassword}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_approval_transaction_completed':
                                setState({ type: 'sign_swap' })
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'sign_swap':
            return (
                <SignSwapStep
                    quote={quote}
                    fromAmount={fromAmount}
                    fromAddress={fromAddress}
                    sessionPassword={sessionPassword}
                    networkMap={networkMap}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )
        default:
            return notReachable(state)
    }
}
