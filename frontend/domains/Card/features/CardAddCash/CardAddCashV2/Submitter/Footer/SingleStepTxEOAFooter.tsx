import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { fromBigInt } from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { InternalTransactionActionSource } from '@zeal/domains/Main'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { signAndSubmitTransaction } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/api/signAndSubmitTransaction'
import { getSuggestedGasLimit } from '@zeal/domains/Transactions/helpers/getSuggestedGasLimit'

import { LoadingLayout } from './LoadingLayout'

import {
    CardTopUpEOAQuote,
    SubmittedEOASendCardTopUp,
    SubmittedEOASendNativeCardTopUp,
} from '../../types'

type Props = {
    // FIXME :: @Nicvaniek extract?
    quote: Extract<
        CardTopUpEOAQuote,
        { type: 'send_eoa' | 'swap_eoa_native_send_transaction' }
    >

    fromAddress: Web3.address.Address
    fromAmount: CryptoMoney
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'on_card_top_up_success'
          submittedCardTopUp:
              | SubmittedEOASendCardTopUp
              | SubmittedEOASendNativeCardTopUp
      }

const fetch = async ({
    quote,
    fromAddress,
    fromAmount,
    sessionPassword,
    networkMap,
    networkRPCMap,
    signal,
}: {
    quote: Extract<
        CardTopUpEOAQuote,
        { type: 'send_eoa' | 'swap_eoa_native_send_transaction' }
    >
    fromAddress: Web3.address.Address
    fromAmount: CryptoMoney
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<SubmittedEOASendCardTopUp | SubmittedEOASendNativeCardTopUp> => {
    const gasEstimate = fromBigInt(quote.gasEstimate)

    const network = findNetworkByHexChainId(
        fromAmount.currency.networkHexChainId,
        networkMap
    )

    switch (quote.type) {
        case 'send_eoa': {
            const actionSource: InternalTransactionActionSource = {
                type: 'internal',
                transactionEventSource: 'topupCardSend',
            }

            const submittedTransaction = await signAndSubmitTransaction({
                senderAddress: fromAddress,
                fee: quote.fee.fast,
                network,
                networkRPCMap,
                nonce: quote.fee.nonce,
                sendTransactionRequest: quote.transaction,
                keyStore: quote.keystore,
                sessionPassword,
                gas: getSuggestedGasLimit(gasEstimate),
                actionSource,
                signal,
            })

            return {
                type: 'send_eoa',
                amount: quote.toAmount,
                state: {
                    type: 'waiting_for_transaction',
                    submittedTransaction,
                },
                keyStore: quote.keystore,
                startedAtMs: submittedTransaction.queuedAt,
                actionSource,
            }
        }
        case 'swap_eoa_native_send_transaction': {
            const actionSource: InternalTransactionActionSource = {
                type: 'internal',
                transactionEventSource: 'topupCardSwapNativeSend',
            }

            const submittedTransaction = await signAndSubmitTransaction({
                senderAddress: fromAddress,
                fee: quote.fee.fast,
                network,
                networkRPCMap,
                nonce: quote.fee.nonce,
                sendTransactionRequest: quote.transaction,
                keyStore: quote.keystore,
                sessionPassword,
                gas: getSuggestedGasLimit(gasEstimate),
                actionSource,
                signal,
            })
            return {
                type: 'swap_eoa_native_send_transaction',
                fromAmount,
                toAmount: quote.toAmount,
                quoteRequestHash: quote.quoteRequestHash,

                state: {
                    type: 'waiting_for_transaction',
                    submittedTransaction,
                },
                startedAtMs: submittedTransaction.queuedAt,
                keyStore: quote.keystore,
                actionSource,
            }
        }

        /* istanbul ignore next */
        default:
            return notReachable(quote)
    }
}

export const SingleStepTxEOAFooter = ({
    quote,
    fromAmount,
    fromAddress,
    networkMap,
    networkRPCMap,
    sessionPassword,
    installationId,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            fromAddress,
            fromAmount,
            sessionPassword,
            networkRPCMap,
            networkMap,
            quote,
        },
    })

    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_card_top_up_success',
                    submittedCardTopUp: loadable.data,
                })
                return
            case 'loading':
            case 'error':
                return
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveOnMsg])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return <LoadingLayout variant="step_1_of_1" />

        case 'error':
            return (
                <>
                    <LoadingLayout variant="step_1_of_1" />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: {
                                            fromAddress,
                                            fromAmount,
                                            sessionPassword,
                                            networkRPCMap,
                                            networkMap,
                                            quote,
                                        },
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
