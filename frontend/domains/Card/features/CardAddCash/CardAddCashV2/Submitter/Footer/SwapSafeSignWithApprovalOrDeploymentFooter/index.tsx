import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'

import { PreparationTransaction } from './PreparationTransaction'

import { CardTopUpSafeQuote } from '../../../types'
import { SignSwapStep } from '../SignSwapStep'

type Props = {
    quote: Extract<
        CardTopUpSafeQuote,
        { type: 'swap_safe_sign_typed_data_with_approval_or_safe_deployment' }
    >
    fromAddress: Web3.address.Address
    fromAmount: CryptoMoney
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof SignSwapStep>
    | Extract<MsgOf<typeof PreparationTransaction>, { type: 'close' }>

type State = { type: 'preparation_transaction' } | { type: 'sign_swap' }

export const SwapSafeSignWithApprovalOrDeploymentFooter = ({
    quote,
    onMsg,
    sessionPassword,
    networkRPCMap,
    fromAmount,
    fromAddress,
    installationId,
    networkMap,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'preparation_transaction',
    })

    switch (state.type) {
        case 'preparation_transaction':
            return (
                <PreparationTransaction
                    installationId={installationId}
                    fromAmount={fromAmount}
                    quote={quote}
                    sessionPassword={sessionPassword}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break

                            case 'on_preparation_transaction_completed':
                                setState({ type: 'sign_swap' })
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'sign_swap':
            return (
                <SignSwapStep
                    quote={quote}
                    fromAddress={fromAddress}
                    fromAmount={fromAmount}
                    sessionPassword={sessionPassword}
                    networkMap={networkMap}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )
        default:
            return notReachable(state)
    }
}
