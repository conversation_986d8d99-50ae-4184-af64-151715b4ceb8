import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'

import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { GNOSIS } from '@zeal/domains/Network/constants'
import { monitorSubmittedUserOperation } from '@zeal/domains/UserOperation/api/monitorSubmittedUserOperation'
import { sign as signUserOperation } from '@zeal/domains/UserOperation/api/sign'
import { submitUserOperationToBundler } from '@zeal/domains/UserOperation/api/submitUserOperationToBundler'

import { CardTopUpSafeQuote } from '../../../types'
import { LoadingLayout } from '../LoadingLayout'

type Props = {
    quote: Extract<
        CardTopUpSafeQuote,
        { type: 'earn_safe_sign_typed_data_with_optional_approval' }
    >
    installationId: string
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'on_approval_transaction_completed' } | { type: 'close' }

const fetch = async ({
    networkMap,
    networkRPCMap,
    sessionPassword,
    signal,
    installationId,
    quote,
}: {
    quote: Extract<
        CardTopUpSafeQuote,
        { type: 'earn_safe_sign_typed_data_with_optional_approval' }
    >
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    signal?: AbortSignal
}): Promise<void> => {
    const network = GNOSIS

    const userOperationWithSignature = await signUserOperation({
        fee: quote.fee,
        keyStore: quote.keystore,
        metaTransactionDatas: quote.metaTransactionDatas,
        network,
        networkMap,
        networkRPCMap,
        entrypointNonce: quote.entrypointNonce,
        safeInstance: quote.safeInstance,
        sessionPassword,
        signal,
    })

    const submittedUserOperation = await submitUserOperationToBundler({
        userOperationWithSignature,
        network,
        networkRPCMap,
        actionSource: {
            type: 'internal',
            transactionEventSource: 'topupCardEarnWithdrawWithOptionalApproval',
        },
    })

    const finishedUserOperation = await monitorSubmittedUserOperation({
        installationId,
        network,
        networkRPCMap,
        submittedUserOperation,
        signal,
    })

    switch (finishedUserOperation.state) {
        case 'completed':
            return undefined

        case 'failed':
        case 'rejected':
            throw new ImperativeError(
                '[CardAddCash] Approval userOp failed or rejected',
                {
                    userOperationHash: finishedUserOperation.userOperationHash,
                    reasons: finishedUserOperation.state,
                }
            )

        /* istanbul ignore next */
        default:
            return notReachable(finishedUserOperation)
    }
}

export const Approval = ({
    quote,
    sessionPassword,
    networkRPCMap,
    networkMap,
    installationId,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            sessionPassword,
            networkRPCMap,
            networkMap,
            quote,
            installationId,
        },
    })

    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_approval_transaction_completed',
                })
                return
            case 'loading':
            case 'error':
                return

            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveOnMsg])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return <LoadingLayout variant="step_1_of_2" />
        case 'error':
            return (
                <>
                    <LoadingLayout variant="step_1_of_2" />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
