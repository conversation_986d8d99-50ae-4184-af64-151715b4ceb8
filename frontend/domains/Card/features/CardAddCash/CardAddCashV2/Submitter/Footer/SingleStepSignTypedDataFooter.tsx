import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'

import { submitBungeeIntentQuote } from '@zeal/domains/Currency/domains/Bungee/api/submitBungeeIntentQuote'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { signMessage } from '@zeal/domains/RPCRequest/helpers/signMessage'

import { LoadingLayout } from './LoadingLayout'

import {
    CardTopUpEOAQuote,
    CardTopUpSafeQuote,
    SubmittedSwapSignTypedDataCardTopUp,
} from '../../types'

type Props = {
    quote:
        | Extract<CardTopUpEOAQuote, { type: 'swap_eoa_sign_typed_data' }>
        | Extract<CardTopUpSafeQuote, { type: 'swap_safe_sign_typed_data' }>
    fromAmount: CryptoMoney // FIXME :: @Nicvaniek see if we can use generics on request type to not have to pass this separately - just pass request

    networkMap: NetworkMap
    sessionPassword: string
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'close'
      }
    | {
          type: 'on_card_top_up_success'
          submittedCardTopUp: SubmittedSwapSignTypedDataCardTopUp
      }

const fetch = async ({
    quote,
    fromAmount,
    networkMap,
    sessionPassword,
}: {
    quote:
        | Extract<CardTopUpEOAQuote, { type: 'swap_eoa_sign_typed_data' }>
        | Extract<CardTopUpSafeQuote, { type: 'swap_safe_sign_typed_data' }>
    sessionPassword: string
    networkMap: NetworkMap
    fromAmount: CryptoMoney
}): Promise<SubmittedSwapSignTypedDataCardTopUp> => {
    const network = findNetworkByHexChainId(
        fromAmount.currency.networkHexChainId,
        networkMap
    )

    const signedMessage = await signMessage({
        sessionPassword,
        request: quote.swapSignatureRequest,
        keyStore: quote.keystore,
        network,
        dApp: null,
    })

    const requestHash = await submitBungeeIntentQuote({
        quoteId: quote.quoteId,
        quoteWiness: quote.witness,
        userSignature: signedMessage as Hexadecimal, // FIXME :: @mike can we change signMessage to return Hexadecimal?
        quoteRequestType: quote.quoteRequestType,
    })

    return {
        type: 'swap_sign_typed_data',
        fromAmount,
        toAmount: quote.toAmount,
        quoteRequestHash: requestHash,
        actionSource: {
            type: 'internal',
            transactionEventSource: 'topupCardSwapSign',
        },
        startedAtMs: Date.now(),
        state: { type: 'waiting_for_swap' },
    }
}

export const SingleStepSignTypedDataFooter = ({
    fromAmount,
    sessionPassword,
    quote,
    networkMap,
    onMsg,
    installationId,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            sessionPassword,
            fromAmount,
            networkMap,
            quote,
        },
    })

    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_card_top_up_success',
                    submittedCardTopUp: loadable.data,
                })
                return
            case 'loading':
            case 'error':
                return
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveOnMsg])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return <LoadingLayout variant="step_1_of_1" />

        case 'error':
            return (
                <>
                    <LoadingLayout variant="step_1_of_1" />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: {
                                            fromAmount,
                                            sessionPassword,
                                            networkMap,
                                            quote,
                                        },
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
