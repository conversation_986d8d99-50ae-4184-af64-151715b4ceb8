import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'

import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { sign as signUserOperation } from '@zeal/domains/UserOperation/api/sign'
import { submitUserOperationToBundler } from '@zeal/domains/UserOperation/api/submitUserOperationToBundler'

import { LoadingLayout } from './LoadingLayout'

import {
    CardTopUpSafeQuote,
    SubmittedSafeSendCardTopUp,
    SubmittedSafeSendNativeCardTopUp,
} from '../../types'
type Props = {
    quote: Extract<
        CardTopUpSafeQuote,
        { type: 'send_safe' | 'swap_safe_native_send_transaction' }
    >
    fromAmount: CryptoMoney
    installationId: string
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_card_top_up_success'
          submittedCardTopUp:
              | SubmittedSafeSendCardTopUp
              | SubmittedSafeSendNativeCardTopUp
      }
    | { type: 'close' }

const submit = async ({
    quote,
    networkMap,
    networkRPCMap,
    sessionPassword,
    fromAmount,
    signal,
}: {
    fromAmount: CryptoMoney
    quote: Extract<
        CardTopUpSafeQuote,
        { type: 'send_safe' | 'swap_safe_native_send_transaction' }
    >
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<SubmittedSafeSendCardTopUp | SubmittedSafeSendNativeCardTopUp> => {
    switch (quote.type) {
        case 'send_safe': {
            const network = findNetworkByHexChainId(
                quote.toAmount.currency.networkHexChainId,
                networkMap
            )

            const userOperationWithSignature = await signUserOperation({
                fee: quote.fee,
                keyStore: quote.keystore,
                metaTransactionDatas: [quote.transaction],
                network,
                networkMap,
                networkRPCMap,
                entrypointNonce: quote.entrypointNonce,
                safeInstance: quote.safeInstance,
                sessionPassword,
                signal,
            })

            const submittedUserOperation = await submitUserOperationToBundler({
                userOperationWithSignature,
                network,
                networkRPCMap,
                actionSource: {
                    type: 'internal',
                    transactionEventSource: 'topupCardSend',
                },
            })

            return {
                type: 'send_safe',
                amount: quote.toAmount,
                startedAtMs: submittedUserOperation.queuedAt,
                state: {
                    type: 'waiting_for_user_operation',
                    submittedUserOperation,
                },
                actionSource: {
                    type: 'internal',
                    transactionEventSource: 'topupCardSend',
                },
            }
        }

        case 'swap_safe_native_send_transaction':
            const network = findNetworkByHexChainId(
                fromAmount.currency.networkHexChainId,
                networkMap
            )

            const userOperationWithSignature = await signUserOperation({
                fee: quote.fee,
                keyStore: quote.keystore,
                metaTransactionDatas: [quote.transaction],
                network,
                networkMap,
                networkRPCMap,
                entrypointNonce: quote.entrypointNonce,
                safeInstance: quote.safeInstance,
                sessionPassword,
                signal,
            })

            const submittedUserOperation = await submitUserOperationToBundler({
                userOperationWithSignature,
                network,
                networkRPCMap,
                actionSource: {
                    type: 'internal',
                    transactionEventSource: 'topupCardSwapNativeSend',
                },
            })

            {
                return {
                    type: 'swap_safe_native_send_transaction',
                    fromAmount,
                    toAmount: quote.toAmount,
                    quoteRequestHash: quote.quoteRequestHash,
                    startedAtMs: submittedUserOperation.queuedAt,
                    state: {
                        type: 'waiting_for_user_operation',
                        submittedUserOperation,
                    },
                    actionSource: {
                        type: 'internal',
                        transactionEventSource: 'topupCardSwapNativeSend',
                    },
                }
            }

        default:
            return notReachable(quote)
    }
}

export const SingleStepTxSafeFooter = ({
    quote,
    networkMap,
    networkRPCMap,
    sessionPassword,
    installationId,
    fromAmount,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(submit, {
        type: 'loading',
        params: {
            sessionPassword,
            networkRPCMap,
            networkMap,
            fromAmount,
            quote,
        },
    })
    const liveMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveMsg.current({
                    type: 'on_card_top_up_success',
                    submittedCardTopUp: loadable.data,
                })
                break
            case 'loading':
            case 'error':
                break

            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveMsg])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return <LoadingLayout variant="step_1_of_1" />

        case 'error':
            return (
                <>
                    <LoadingLayout variant="step_1_of_1" />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
