import { Column } from '@zeal/uikit/Column'
import { Screen } from '@zeal/uikit/Screen'

import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Footer } from './Footer'
import { ReadOnlyFeeWidget } from './ReadOnlyFeeWidget'
import { ReadOnlyFormLayout } from './ReadOnlyFormLayout'

import { CardTopUpRequest } from '../types'

type Props = {
    cardTopUpRequest: CardTopUpRequest

    accountsMap: AccountsMap
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof Footer> | { type: 'close' }

export const Submitter = ({
    cardTopUpRequest,
    sessionPassword,
    networkRPCMap,
    networkMap,
    accountsMap,
    defaultCurrencyConfig,
    installationId,
    onMsg,
}: Props) => {
    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} fill>
                <ReadOnlyFormLayout
                    cardTopUpRequest={cardTopUpRequest}
                    networkMap={networkMap}
                    accountsMap={accountsMap}
                />
                {/* FIXME @resetko-zeal HW wallet banner */}
                <ReadOnlyFeeWidget
                    cardTopUpRequest={cardTopUpRequest}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                />
                <Footer
                    sessionPassword={sessionPassword}
                    cardTopUpRequest={cardTopUpRequest}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            </Column>
        </Screen>
    )
}
