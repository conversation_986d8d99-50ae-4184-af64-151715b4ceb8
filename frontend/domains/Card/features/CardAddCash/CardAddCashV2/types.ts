import { BungeeRawRequestType, BungeeRawWitness } from '@zeal/api/bungeeApi'

import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { LoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { Result } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    CardBalance,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { DeployedTaker, Earn, TakerType } from '@zeal/domains/Earn'
import { EOA, KeyStoreMap, Safe4337, TrackOnly } from '@zeal/domains/KeyStore'
import { SafeInstance } from '@zeal/domains/KeyStore/helpers/fetchSafe4337Instance'
import { InternalTransactionActionSource } from '@zeal/domains/Main'
import { CryptoMoney, FiatMoney, Money2 } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    EthSendTransaction,
    EthSignTypedDataV4,
} from '@zeal/domains/RPCRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import {
    SubmitedTransaction,
    SubmitedTransactionFailed,
} from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction'
import {
    SubmittedUserOperation,
    SubmittedUserOperationFailed,
    SubmittedUserOperationRejected,
} from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import { FeeForecastResponse } from '@zeal/domains/Transactions/domains/FeeForecast'
import {
    MetaTransactionData,
    SponsoredUserOperationFee,
    UserOperationFee2,
} from '@zeal/domains/UserOperation'

export type QuoteError = { type: 'no_routes' } | { type: 'from_amount_zero' }

export type SourceBalancesAndCurrencies = {
    serverPortfolio: ServerPortfolio2
    cardBalance: CardBalance
    earn: Earn
    supportedTopUpCurrencies: CryptoCurrency[]
}

export type SourceBalancesAndCurrenciesLoadable = LoadableData<
    SourceBalancesAndCurrencies,
    {
        featureCacheKey: string
        cardConfig: ReadonlySignerSelectedOnboardedCardConfig
        currencyHiddenMap: CurrencyHiddenMap
        defaultCurrencyConfig: DefaultCurrencyConfig
        installationId: string
        networkMap: NetworkMap
        networkRPCMap: NetworkRPCMap
        senderAddress: Web3.address.Address
    }
>

export type QuoteParams = {
    keyStoreMap: KeyStoreMap
    form: Form
    senderAddress: Web3.address.Address
    senderPortfolio: ServerPortfolio2
    senderEarn: Earn
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    gasCurrencyPresetMap: GasCurrencyPresetMap
    safeRpcDataCacheKey: string
}

export type QuotePollable = PollableData<CardTopUpRequestQuote, QuoteParams>

export type Form = (
    | {
          type: 'send'
          amount: string | null
      }
    | {
          type: 'swap'
          fromCurrency: CryptoCurrency
          amount: string | null
      }
    | {
          type: 'earn'
          amount: string | null
          taker: DeployedTaker
      }
) & { stage: 'edit' | 'confirm' }

export type CardTopUpEOAQuote =
    | {
          // single
          type: 'send_eoa'
          keystore: EOA
          toAmount: CryptoMoney
          toAmountInDefaultCurrency: FiatMoney | null
          transaction: EthSendTransaction
          fee: FeeForecastResponse
          gasEstimate: bigint
      }
    | {
          // single
          type: 'swap_eoa_sign_typed_data'
          keystore: EOA
          toAmount: CryptoMoney
          toAmountInDefaultCurrency: FiatMoney | null
          swapSignatureRequest: EthSignTypedDataV4

          quoteId: string
          witness: BungeeRawWitness
          quoteRequestType: BungeeRawRequestType
          quoteRequestHash: Hexadecimal.Hexadecimal
      }
    | {
          // double
          type: 'swap_eoa_sign_typed_data_with_approval'
          keystore: EOA
          toAmount: CryptoMoney
          toAmountInDefaultCurrency: FiatMoney | null
          swapSignatureRequest: EthSignTypedDataV4
          approvalTransaction: EthSendTransaction
          fee: FeeForecastResponse
          gasEstimate: bigint

          quoteId: string
          witness: BungeeRawWitness
          quoteRequestHash: Hexadecimal.Hexadecimal
          quoteRequestType: BungeeRawRequestType
      }
    | {
          // signle
          type: 'swap_eoa_native_send_transaction'
          keystore: EOA
          toAmount: CryptoMoney
          toAmountInDefaultCurrency: FiatMoney | null
          transaction: EthSendTransaction
          fee: FeeForecastResponse
          quoteRequestHash: Hexadecimal.Hexadecimal
          gasEstimate: bigint
      }
    | {
          // double
          type: 'earn_eoa_sign_typed_data_with_approval'
          keystore: EOA
          fromAmountInUserCurrency: Money2
          toAmount: CryptoMoney
          toAmountInDefaultCurrency: FiatMoney | null
          approvalTransaction: EthSendTransaction
          approvalFee: FeeForecastResponse
          approvalGasEstimate: bigint
          withdrawalTransaction: EthSendTransaction
          withdrawalFee: FeeForecastResponse
          withdrawalGasEstimate: bigint
          signatureRequest: EthSignTypedDataV4

          quoteId: string
          witness: BungeeRawWitness
          quoteRequestType: BungeeRawRequestType
          quoteRequestHash: Hexadecimal.Hexadecimal
      }
    | {
          // signle
          type: 'earn_eoa_sign_typed_data'
          keystore: EOA
          fromAmountInUserCurrency: Money2
          toAmount: CryptoMoney
          toAmountInDefaultCurrency: FiatMoney | null
          withdrawalTransaction: EthSendTransaction
          withdrawalFee: FeeForecastResponse
          withdrawalGasEstimate: bigint
          signatureRequest: EthSignTypedDataV4

          quoteId: string
          witness: BungeeRawWitness
          quoteRequestType: BungeeRawRequestType
          quoteRequestHash: Hexadecimal.Hexadecimal
      }

export type CardTopUpSafeQuote =
    | {
          //signle
          type: 'send_safe'
          keystore: Safe4337
          toAmount: CryptoMoney
          toAmountInDefaultCurrency: FiatMoney | null

          safeInstance: SafeInstance
          entrypointNonce: bigint // FIXME @resetko-zeal I think we can skip parsing and keep this one Hexadecimal everywhere, because we don't need to manipulate it
          transaction: MetaTransactionData
          fee: SponsoredUserOperationFee
      }
    | {
          // double shot
          type: 'swap_safe_sign_typed_data_with_approval_or_safe_deployment'
          keystore: Safe4337
          toAmount: CryptoMoney
          toAmountInDefaultCurrency: FiatMoney | null

          safeInstance: SafeInstance
          entrypointNonce: bigint
          swapSignatureRequest: EthSignTypedDataV4
          preparationTransaction: MetaTransactionData
          fee: UserOperationFee2

          quoteId: string
          witness: BungeeRawWitness
          quoteRequestType: BungeeRawRequestType
          quoteRequestHash: Hexadecimal.Hexadecimal
      }
    | {
          // signle shot
          type: 'swap_safe_sign_typed_data' // TODO :: @Nicvaniek can we combine this with the EOA one?
          keystore: Safe4337
          toAmount: CryptoMoney
          toAmountInDefaultCurrency: FiatMoney | null
          swapSignatureRequest: EthSignTypedDataV4

          quoteId: string
          witness: BungeeRawWitness
          quoteRequestType: BungeeRawRequestType
          quoteRequestHash: Hexadecimal.Hexadecimal
      }
    | {
          // signle shot
          type: 'swap_safe_native_send_transaction'
          keystore: Safe4337
          toAmount: CryptoMoney
          toAmountInDefaultCurrency: FiatMoney | null

          safeInstance: SafeInstance
          entrypointNonce: bigint
          transaction: MetaTransactionData
          quoteRequestHash: Hexadecimal.Hexadecimal
          fee: UserOperationFee2
      }
    | {
          // double
          type: 'earn_safe_sign_typed_data_with_optional_approval'

          keystore: Safe4337
          fromAmountInUserCurrency: Money2
          toAmount: CryptoMoney
          toAmountInDefaultCurrency: FiatMoney | null

          safeInstance: SafeInstance
          entrypointNonce: bigint
          metaTransactionDatas: MetaTransactionData[]
          fee: SponsoredUserOperationFee
          signatureRequest: EthSignTypedDataV4

          quoteId: string
          witness: BungeeRawWitness
          quoteRequestType: BungeeRawRequestType
          quoteRequestHash: Hexadecimal.Hexadecimal
      }

export type CardTopUpTrackOnlyQuote =
    | {
          type: 'swap_track_only'
          keystore: TrackOnly
          toAmount: CryptoMoney
          toAmountInDefaultCurrency: FiatMoney | null
      }
    | {
          type: 'earn_track_only'
          keystore: TrackOnly
          toAmount: CryptoMoney
          toAmountInDefaultCurrency: FiatMoney | null
      }
    | {
          type: 'send_track_only'
          keystore: TrackOnly
          toAmount: CryptoMoney
          toAmountInDefaultCurrency: FiatMoney | null
      }

export type CardTopUpQuote =
    | CardTopUpEOAQuote
    | CardTopUpSafeQuote
    | CardTopUpTrackOnlyQuote

// FIXME @resetko-zeal goes to domain?
export type CardTopUpRequestQuote = {
    fromAmount: CryptoMoney
    fromAmountInDefaultCurrency: FiatMoney | null

    maxBalance: Money2

    quote: Result<QuoteError, CardTopUpQuote>
}

export type CardTopUpRequest = {
    fromAmount: CryptoMoney
    fromAmountInDefaultCurrency: FiatMoney | null
    senderAddress: Web3.address.Address

    maxBalanceAtSubmit: Money2
    cardBalanceAtSubmit: CardBalance

    quote: CardTopUpEOAQuote | CardTopUpSafeQuote
}

export type SubmittedEOASendCardTopUp = {
    type: 'send_eoa'
    amount: CryptoMoney
    actionSource: InternalTransactionActionSource
    keyStore: EOA
    startedAtMs: number
    state:
        | CardTopUpWaitingForTransaction
        | CardTopUpCompleted
        | CardTopUpFailedTransaction
}

export type SubmittedSafeSendCardTopUp = {
    type: 'send_safe'
    amount: CryptoMoney
    actionSource: InternalTransactionActionSource
    startedAtMs: number
    state:
        | CardTopUpWaitingForUserOperation
        | CardTopUpCompleted
        | CardTopUpFailedUserOperation
}

export type SubmittedEOASendNativeCardTopUp = {
    type: 'swap_eoa_native_send_transaction'
    fromAmount: CryptoMoney
    toAmount: CryptoMoney
    quoteRequestHash: Hexadecimal.Hexadecimal
    actionSource: InternalTransactionActionSource
    keyStore: EOA
    startedAtMs: number
    state:
        | CardTopUpWaitingForTransaction
        | CardTopUpWaitingForSwap
        | CardTopUpCompleted
        | CardTopUpFailedTransaction
        | CardTopUpFailedBungee
}

export type SubmittedSafeSendNativeCardTopUp = {
    type: 'swap_safe_native_send_transaction'
    fromAmount: CryptoMoney
    toAmount: CryptoMoney
    quoteRequestHash: Hexadecimal.Hexadecimal
    actionSource: InternalTransactionActionSource
    startedAtMs: number
    state:
        | CardTopUpWaitingForUserOperation
        | CardTopUpWaitingForSwap
        | CardTopUpCompleted
        | CardTopUpFailedUserOperation
        | CardTopUpFailedBungee
}

export type SubmittedSwapSignTypedDataCardTopUp = {
    type: 'swap_sign_typed_data'
    fromAmount: CryptoMoney
    toAmount: CryptoMoney
    quoteRequestHash: Hexadecimal.Hexadecimal
    actionSource: InternalTransactionActionSource
    startedAtMs: number
    state: CardTopUpWaitingForSwap | CardTopUpCompleted | CardTopUpFailedBungee
}

export type SubmittedEarnSignTypedDataCardTopUp = {
    type: 'earn_sign_typed_data'
    takerType: TakerType
    fromAmount: CryptoMoney
    toAmount: CryptoMoney
    quoteRequestHash: Hexadecimal.Hexadecimal
    actionSource: InternalTransactionActionSource
    startedAtMs: number
    state: CardTopUpWaitingForSwap | CardTopUpCompleted | CardTopUpFailedBungee
}

export type CardTopUpCompleted = {
    type: 'completed'
}

export type CardTopUpFailedTransaction = {
    type: 'failed_tx'
    transaction: SubmitedTransactionFailed
}

export type CardTopUpFailedUserOperation = {
    type: 'failed_user_operation'
    userOperation: SubmittedUserOperationFailed | SubmittedUserOperationRejected
}

export type CardTopUpFailedBungee = {
    type: 'failed_bungee'
    quoteRequestHash: Hexadecimal.Hexadecimal
}

export type CardTopUpWaitingForTransaction = {
    type: 'waiting_for_transaction'
    submittedTransaction: SubmitedTransaction
}

export type CardTopUpWaitingForUserOperation = {
    type: 'waiting_for_user_operation'
    submittedUserOperation: SubmittedUserOperation
}

export type CardTopUpWaitingForSwap = {
    type: 'waiting_for_swap'
}

export type CardTopUpProgressState =
    | CardTopUpCompleted
    | CardTopUpFailedTransaction
    | CardTopUpFailedUserOperation
    | CardTopUpFailedBungee
    | CardTopUpWaitingForTransaction
    | CardTopUpWaitingForUserOperation
    | CardTopUpWaitingForSwap

export type SubmittedCardTopUp =
    | SubmittedEOASendCardTopUp
    | SubmittedEarnSignTypedDataCardTopUp
    | SubmittedSwapSignTypedDataCardTopUp
    | SubmittedEOASendNativeCardTopUp
    | SubmittedSafeSendCardTopUp
    | SubmittedSafeSendNativeCardTopUp
