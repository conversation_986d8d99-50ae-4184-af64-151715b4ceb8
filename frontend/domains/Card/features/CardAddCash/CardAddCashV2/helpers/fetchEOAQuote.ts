import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { failure, success } from '@zeal/toolkit/Result'

import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import { fetchBungeeIntentQuote } from '@zeal/domains/Currency/domains/Bungee/api/fetchBungeeIntentQuote'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { createWithdrawalTransaction } from '@zeal/domains/Earn/helpers/createWithdrawalTransaction'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { EOA } from '@zeal/domains/KeyStore'
import { CryptoMoney, Money2 } from '@zeal/domains/Money'
import { sub2 } from '@zeal/domains/Money/helpers/sub'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { createTransferEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/createERC20EthSendTransaction'
import { fetchFeeForecast } from '@zeal/domains/Transactions/domains/FeeForecast/api/fetchFeeForecast'
import { getSuggestedGasLimit } from '@zeal/domains/Transactions/helpers/getSuggestedGasLimit'

import { fetchRateIfNeeded, fetchRatesIfNeeded } from './fetchRatesIfNeeded'
import { getEarnMaxBalanceAndInvestmentAssetAmount } from './getEarnMaxBalanceAndInvestmentAssetAmount'

import {
    APPROVAL_GAS,
    BUNGEE_NATIVE_TRANSFER_GAS,
    CARD_TOKEN_TRANSFER_GAS,
    DUMMY_BUNGEE_NATIVE_TRANSFER,
    EARN_WITHDRAWAL_GAS,
} from '../constants'
import { CardTopUpRequestQuote, QuoteParams } from '../types'

export const fetchEOAQuote = async ({
    form,
    defaultCurrencyConfig,
    cardConfig,
    senderPortfolio,
    keyStore,
    senderEarn,
    networkMap,
    networkRPCMap,
    senderAddress,
    signal,
}: Omit<
    QuoteParams,
    'keyStoreMap' | 'safeRpcDataCacheKey' | 'gasCurrencyPresetMap'
> & {
    keyStore: EOA
    signal?: AbortSignal
}): Promise<CardTopUpRequestQuote> => {
    switch (form.type) {
        case 'send': {
            const amount: CryptoMoney = {
                amount: fromFixedWithFraction(
                    form.amount,
                    cardConfig.currency.fraction
                ),
                currency: cardConfig.currency,
            }

            const maxBalance = getBalanceByCryptoCurrency2({
                currency: cardConfig.currency,
                serverPortfolio: senderPortfolio,
            })

            const transaction = createTransferEthSendTransaction({
                network: CARD_NETWORK,
                from: senderAddress,
                amount,
                to: cardConfig.lastSeenSafeAddress,
            })

            const gasEstimate = Hexadecimal.fromBigInt(CARD_TOKEN_TRANSFER_GAS)

            const [rate, fee] = await Promise.all([
                fetchRateIfNeeded({
                    cryptoCurrency: cardConfig.currency,
                    serverPortfolio: senderPortfolio,
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    signal,
                }),
                fetchFeeForecast({
                    network: CARD_NETWORK,
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    address: senderAddress,
                    sendTransactionRequest: transaction,
                    selectedPreset: { type: 'Fast' },
                    gasEstimate,
                    gasLimit: getSuggestedGasLimit(gasEstimate),
                    signal,
                }),
            ])

            const amountInDefaultCurrency = rate
                ? applyRate2({ baseAmount: amount, rate })
                : null

            return {
                maxBalance,
                fromAmount: amount,
                fromAmountInDefaultCurrency: amountInDefaultCurrency,
                quote: success({
                    type: 'send_eoa',
                    keystore: keyStore,
                    toAmount: amount,
                    toAmountInDefaultCurrency: amountInDefaultCurrency,
                    transaction,
                    fee,
                    gasEstimate: Hexadecimal.toBigInt(gasEstimate),
                }),
            }
        }
        case 'swap': {
            const fromNetwork = findNetworkByHexChainId(
                form.fromCurrency.networkHexChainId,
                networkMap
            )

            const fromAmount: CryptoMoney = {
                amount: fromFixedWithFraction(
                    form.amount,
                    form.fromCurrency.fraction
                ),
                currency: form.fromCurrency,
            }

            if (fromAmount.currency.id === fromNetwork.nativeCurrency.id) {
                const gasEstimate = Hexadecimal.fromBigInt(
                    BUNGEE_NATIVE_TRANSFER_GAS
                )

                const [rates, quote, fee] = await Promise.all([
                    fetchRatesIfNeeded({
                        cryptoCurrencies: [
                            fromAmount.currency,
                            cardConfig.currency,
                        ],
                        serverPortfolio: senderPortfolio,
                        defaultCurrencyConfig,
                        networkMap,
                        networkRPCMap,
                        signal,
                    }),
                    fetchBungeeIntentQuote({
                        request: {
                            fromAddress: senderAddress,
                            fromAmount,
                            toCurrency: cardConfig.currency,
                            toAddress: cardConfig.lastSeenSafeAddress,
                        },
                        networkMap,
                        networkRPCMap,
                        signal,
                    }),
                    fetchFeeForecast({
                        network: fromNetwork,
                        defaultCurrencyConfig,
                        networkMap,
                        networkRPCMap,
                        address: senderAddress,
                        sendTransactionRequest: DUMMY_BUNGEE_NATIVE_TRANSFER, // FIXME :: @Nicvaniek test if this works. If not, we need to fetch fee forecast using the actual tx data from quote
                        selectedPreset: { type: 'Fast' },
                        gasEstimate,
                        gasLimit: getSuggestedGasLimit(gasEstimate),
                        signal,
                    }),
                ])

                const fromRate = rates[fromAmount.currency.id] || null

                const fromAmountInDefaultCurrency = fromRate
                    ? applyRate2({ baseAmount: fromAmount, rate: fromRate })
                    : null

                const nativeTokenBalance = getBalanceByCryptoCurrency2({
                    currency: fromAmount.currency,
                    serverPortfolio: senderPortfolio,
                })

                const maxBalance =
                    nativeTokenBalance.amount >
                    fee.fast.maxPriceInNativeCurrency.amount
                        ? sub2(
                              nativeTokenBalance,
                              fee.fast.maxPriceInNativeCurrency
                          )
                        : nativeTokenBalance

                if (!quote) {
                    return {
                        fromAmount,
                        fromAmountInDefaultCurrency,
                        maxBalance,
                        quote: failure({ type: 'no_routes' }),
                    }
                }

                switch (quote.type) {
                    case 'bungee_erc20_intent_quote':
                        throw new ImperativeError(
                            'Got ERC20 bungee quote for native swap'
                        )
                    case 'bungee_native_token_intent_quote':
                        const toRate = rates[cardConfig.currency.id] || null

                        const toAmountInDefaultCurrency = toRate
                            ? applyRate2({
                                  baseAmount: quote.outputAmount,
                                  rate: toRate,
                              })
                            : null

                        return {
                            fromAmount,
                            fromAmountInDefaultCurrency,
                            maxBalance,
                            quote: success({
                                type: 'swap_eoa_native_send_transaction',
                                quoteRequestHash: quote.requestHash,
                                keystore: keyStore,
                                toAmount: quote.outputAmount,
                                toAmountInDefaultCurrency,
                                transaction: quote.transaction,
                                fee,
                                gasEstimate: Hexadecimal.toBigInt(gasEstimate),
                            }),
                        }
                    default:
                        return notReachable(quote)
                }
            }

            const [rates, quote] = await Promise.all([
                fetchRatesIfNeeded({
                    cryptoCurrencies: [
                        fromAmount.currency,
                        cardConfig.currency,
                    ],
                    serverPortfolio: senderPortfolio,
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    signal,
                }),
                fetchBungeeIntentQuote({
                    request: {
                        fromAddress: senderAddress,
                        fromAmount,
                        toCurrency: cardConfig.currency,
                        toAddress: cardConfig.lastSeenSafeAddress,
                    },
                    networkMap,
                    networkRPCMap,
                    signal,
                }),
            ])

            const fromRate = rates[fromAmount.currency.id] || null

            const fromAmountInDefaultCurrency = fromRate
                ? applyRate2({ baseAmount: fromAmount, rate: fromRate })
                : null

            const maxBalance = getBalanceByCryptoCurrency2({
                currency: fromAmount.currency,
                serverPortfolio: senderPortfolio,
            })

            if (!quote) {
                return {
                    fromAmount,
                    fromAmountInDefaultCurrency,
                    maxBalance,
                    quote: failure({ type: 'no_routes' }),
                }
            }

            switch (quote.type) {
                case 'bungee_native_token_intent_quote':
                    throw new ImperativeError(
                        'Got native bungee quote for ERC20 swap'
                    )
                case 'bungee_erc20_intent_quote':
                    const toRate = rates[cardConfig.currency.id] || null

                    const toAmountInDefaultCurrency = toRate
                        ? applyRate2({
                              baseAmount: quote.outputAmount,
                              rate: toRate,
                          })
                        : null

                    if (!quote.permitApprovalTransaction) {
                        return {
                            fromAmount,
                            fromAmountInDefaultCurrency,
                            maxBalance,
                            quote: success({
                                type: 'swap_eoa_sign_typed_data',
                                quoteRequestHash: quote.requestHash,
                                quoteId: quote.quoteId,
                                keystore: keyStore,
                                toAmount: quote.outputAmount,
                                toAmountInDefaultCurrency,
                                swapSignatureRequest: quote.signTypedData,
                                witness: quote.witness,
                                quoteRequestType: quote.quoteRequestType,
                            }),
                        }
                    }

                    const gasEstimate = Hexadecimal.fromBigInt(APPROVAL_GAS)

                    const fee = await fetchFeeForecast({
                        network: fromNetwork,
                        defaultCurrencyConfig,
                        networkMap,
                        networkRPCMap,
                        address: senderAddress,
                        sendTransactionRequest: quote.permitApprovalTransaction,
                        selectedPreset: { type: 'Fast' },
                        gasEstimate,
                        gasLimit: getSuggestedGasLimit(gasEstimate),
                        signal,
                    })

                    return {
                        fromAmount,
                        fromAmountInDefaultCurrency,
                        maxBalance,
                        quote: success({
                            type: 'swap_eoa_sign_typed_data_with_approval',
                            quoteRequestHash: quote.requestHash,
                            quoteId: quote.quoteId,
                            keystore: keyStore,
                            toAmount: quote.outputAmount,
                            toAmountInDefaultCurrency,
                            swapSignatureRequest: quote.signTypedData,
                            witness: quote.witness,
                            quoteRequestType: quote.quoteRequestType,
                            approvalTransaction:
                                quote.permitApprovalTransaction,
                            fee,
                            gasEstimate: Hexadecimal.toBigInt(gasEstimate),
                        }),
                    }
                default:
                    return notReachable(quote)
            }
        }
        case 'earn': {
            const takerPortfolio = senderEarn.takerPortfolioMap[form.taker.type]
            const userCurrency = takerPortfolio.userCurrencyRate.quote

            const fromAmountInUserCurrency = {
                amount: form.amount
                    ? fromFixedWithFraction(form.amount, userCurrency.fraction)
                    : 0n,
                currency: userCurrency,
            } as Money2

            const fromRate = takerPortfolio.userCurrencyToDefaultCurrencyRate

            const fromAmountInDefaultCurrency = fromRate
                ? applyRate2({
                      baseAmount: fromAmountInUserCurrency,
                      rate: fromRate,
                  })
                : null

            const { maxBalanceInUserCurrency, investmentAssetAmount } =
                getEarnMaxBalanceAndInvestmentAssetAmount({
                    fromAmountInUserCurrency,
                    takerPortfolio,
                })

            const withdrawalTransaction = createWithdrawalTransaction({
                investmentAssetAmount: investmentAssetAmount.amount,
                ownerAddress: senderAddress,
                holderAddress: senderEarn.holder,
                takerAddress: form.taker.address,
            })

            const withdrawalGasEstimate =
                Hexadecimal.fromBigInt(EARN_WITHDRAWAL_GAS)

            const [toRate, quote, withdrawalFee] = await Promise.all([
                fetchRateIfNeeded({
                    cryptoCurrency: cardConfig.currency,
                    serverPortfolio: senderPortfolio,
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    signal,
                }),
                fetchBungeeIntentQuote({
                    request: {
                        fromAddress: senderAddress,
                        fromAmount: investmentAssetAmount,
                        toCurrency: cardConfig.currency,
                        toAddress: cardConfig.lastSeenSafeAddress,
                    },
                    networkMap,
                    networkRPCMap,
                    signal,
                }),
                fetchFeeForecast({
                    network: EARN_NETWORK,
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    address: senderAddress,
                    sendTransactionRequest: withdrawalTransaction,
                    selectedPreset: { type: 'Fast' },
                    gasEstimate: withdrawalGasEstimate,
                    gasLimit: getSuggestedGasLimit(withdrawalGasEstimate),
                    signal,
                }),
            ])

            if (!quote) {
                return {
                    fromAmount: investmentAssetAmount,
                    fromAmountInDefaultCurrency,
                    maxBalance: maxBalanceInUserCurrency,
                    quote: failure({ type: 'no_routes' }),
                }
            }

            switch (quote.type) {
                case 'bungee_native_token_intent_quote':
                    throw new ImperativeError(
                        'Got native bungee quote for ERC20 swap'
                    )
                case 'bungee_erc20_intent_quote':
                    const toAmountInDefaultCurrency = toRate
                        ? applyRate2({
                              baseAmount: quote.outputAmount,
                              rate: toRate,
                          })
                        : null

                    if (!quote.permitApprovalTransaction) {
                        return {
                            fromAmount: investmentAssetAmount,
                            fromAmountInDefaultCurrency,
                            maxBalance: maxBalanceInUserCurrency,
                            quote: success({
                                type: 'earn_eoa_sign_typed_data',
                                quoteRequestHash: quote.requestHash,
                                quoteId: quote.quoteId,
                                keystore: keyStore,
                                fromAmountInUserCurrency,
                                toAmount: quote.outputAmount,
                                toAmountInDefaultCurrency,
                                signatureRequest: quote.signTypedData,
                                witness: quote.witness,
                                withdrawalTransaction,
                                withdrawalFee,
                                withdrawalGasEstimate: Hexadecimal.toBigInt(
                                    withdrawalGasEstimate
                                ),
                                quoteRequestType: quote.quoteRequestType,
                            }),
                        }
                    }

                    const approvalGasEstimate =
                        Hexadecimal.fromBigInt(APPROVAL_GAS)

                    const approvalFee = await fetchFeeForecast({
                        network: EARN_NETWORK,
                        defaultCurrencyConfig,
                        networkMap,
                        networkRPCMap,
                        address: senderAddress,
                        sendTransactionRequest: quote.permitApprovalTransaction,
                        selectedPreset: { type: 'Fast' },
                        gasEstimate: approvalGasEstimate,
                        gasLimit: getSuggestedGasLimit(approvalGasEstimate),
                        signal,
                    })

                    return {
                        fromAmount: investmentAssetAmount,
                        fromAmountInDefaultCurrency,
                        maxBalance: maxBalanceInUserCurrency,
                        quote: success({
                            type: 'earn_eoa_sign_typed_data_with_approval',
                            quoteRequestHash: quote.requestHash,
                            quoteId: quote.quoteId,
                            keystore: keyStore,
                            fromAmountInUserCurrency,
                            toAmount: quote.outputAmount,
                            toAmountInDefaultCurrency,
                            signatureRequest: quote.signTypedData,
                            witness: quote.witness,
                            quoteRequestType: quote.quoteRequestType,
                            withdrawalTransaction,
                            withdrawalFee,
                            withdrawalGasEstimate: Hexadecimal.toBigInt(
                                withdrawalGasEstimate
                            ),
                            approvalTransaction:
                                quote.permitApprovalTransaction,
                            approvalFee,
                            approvalGasEstimate:
                                Hexadecimal.toBigInt(approvalGasEstimate),
                        }),
                    }
                default:
                    return notReachable(quote)
            }
        }
        default:
            return notReachable(form)
    }
}
