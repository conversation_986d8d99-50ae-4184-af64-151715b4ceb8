import { FormattedMessage } from 'react-intl'

import { Actions } from '@zeal/uikit/Actions'
import { BannerSolid } from '@zeal/uikit/BannerSolid'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { KeyPadFloatIntut } from '@zeal/uikit/Input/FloatInput'
import { Screen } from '@zeal/uikit/Screen'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { AppErrorBanner } from '@zeal/domains/Error/components/AppErrorBanner'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'

import { FormInputsLayout } from './FormInputsLayout'

import { QuotePollable, SourceBalancesAndCurrenciesLoadable } from '../../types'
import { EditStageFormError, validateEditStage } from '../../validation'

type Props = {
    accountsMap: AccountsMap
    quotePollable: QuotePollable
    sourceBalancesAndCurrenciesLoadable: SourceBalancesAndCurrenciesLoadable
    installationId: string

    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_from_amount_change'; amount: string | null }
    | MsgOf<typeof FormInputsLayout>
    | MsgOf<typeof ErrorBanner>
    | MsgOf<typeof CTA>

export const EditStage = ({
    quotePollable,
    accountsMap,
    sourceBalancesAndCurrenciesLoadable,
    installationId,
    onMsg,
}: Props) => {
    const errors =
        validateEditStage({
            pollable: quotePollable,
            loadable: sourceBalancesAndCurrenciesLoadable,
        }).getFailureReason() || {}

    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} fill alignY="stretch">
                <FormInputsLayout
                    quotePollable={quotePollable}
                    sourceBalancesAndCurrenciesLoadable={
                        sourceBalancesAndCurrenciesLoadable
                    }
                    accountsMap={accountsMap}
                    maxBalanceError={errors.maxBalanceError || null}
                    toAmountInDefaultCurrencyError={
                        errors.toAmountInDefaultCurrencyError || null
                    }
                    installationId={installationId}
                    onMsg={onMsg}
                />

                <ErrorBanner
                    aboveKeypadError={errors.aboveKeypadErrorBanner}
                    installationId={installationId}
                    onMsg={onMsg}
                />

                <KeyPadFloatIntut
                    disabled={false}
                    fraction={18}
                    value={quotePollable.params.form.amount}
                    onChange={(amount) =>
                        onMsg({ type: 'on_from_amount_change', amount })
                    }
                />

                <CTA errors={errors} onMsg={onMsg} />
            </Column>
        </Screen>
    )
}

const ErrorBanner = ({
    aboveKeypadError,
    installationId,
    onMsg,
}: {
    aboveKeypadError: EditStageFormError['aboveKeypadErrorBanner']
    installationId: string
    onMsg: (msg: { type: 'on_error_retry_clicked' }) => void
}) => {
    switch (aboveKeypadError?.type) {
        case undefined:
        case 'pollable_still_loading':
        case 'from_amount_zero':
            return null
        case 'no_routes':
            return (
                <BannerSolid
                    rounded
                    variant="warning"
                    title={
                        <FormattedMessage
                            id="card-add-cash.edit-staget.banner.no-routes-found"
                            defaultMessage="No routes, try a different token or amount"
                        />
                    }
                />
            )
        case 'pollable_errored':
            return (
                <AppErrorBanner
                    error={parseAppError(aboveKeypadError.error)}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'try_again_clicked':
                                onMsg({ type: 'on_error_retry_clicked' })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg.type)
                        }
                    }}
                    installationId={installationId}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(aboveKeypadError)
    }
}

const CTA = ({
    errors,
    onMsg,
}: {
    errors: EditStageFormError
    onMsg: (msg: { type: 'on_continue_clicked' } | { type: 'close' }) => void
}) => {
    return (
        <Actions variant="default" direction="row">
            <Button
                size="regular"
                variant="secondary"
                onClick={() => {
                    onMsg({ type: 'close' })
                }}
            >
                <FormattedMessage
                    id="card-add-cash.edit-stage.cta.cancel"
                    defaultMessage="Cancel"
                />
            </Button>
            <Button
                size="regular"
                variant="primary"
                disabled={!!errors.submitButton}
                onClick={() => {
                    onMsg({ type: 'on_continue_clicked' })
                }}
            >
                {(() => {
                    switch (errors.submitButton?.type) {
                        case undefined:
                        case 'loadable_not_loaded':
                        case 'pollable_errored':
                        case 'pollable_still_loading':
                        case 'no_routes':
                            return (
                                <FormattedMessage
                                    id="card-add-cash.edit-stage.cta.continue"
                                    defaultMessage="Continue"
                                />
                            )
                        case 'amount_required':
                        case 'from_amount_zero':
                            return (
                                <FormattedMessage
                                    id="card-add-cash.edit-stage.cta.enter-amount"
                                    defaultMessage="Enter amount"
                                />
                            )
                        case 'not_enough_balance':
                            return (
                                <FormattedMessage
                                    id="card-add-cash.edit-stage.cta.reduce-to-max"
                                    defaultMessage="Reduce to max"
                                />
                            )

                        /* istanbul ignore next */
                        default:
                            return notReachable(errors.submitButton)
                    }
                })()}
            </Button>
        </Actions>
    )
}
