import { FormattedMessage } from 'react-intl'

import { Actions } from '@zeal/uikit/Actions'
import { BannerSolid } from '@zeal/uikit/BannerSolid'
import { Button, CompressedButton } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Refresh } from '@zeal/uikit/Icon/Refresh'
import { Screen } from '@zeal/uikit/Screen'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { getFormattedPercentage } from '@zeal/toolkit/Percentage'

import { AccountsMap } from '@zeal/domains/Account'
import { AppErrorBanner } from '@zeal/domains/Error/components/AppErrorBanner'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'

import { FeeWidget } from './FeeWidget'

import {
    CardTopUpRequest,
    QuotePollable,
    SourceBalancesAndCurrenciesLoadable,
} from '../../../types'
import {
    ConfirmStageError,
    validateConfirmStageAsYouType,
    validateConfirmStageSubmit,
} from '../../../validation'
import { FormInputsLayout } from '../FormInputsLayout'

type Props = {
    accountsMap: AccountsMap
    quotePollable: QuotePollable
    sourceBalancesAndCurrenciesLoadable: SourceBalancesAndCurrenciesLoadable
    installationId: string

    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof ErrorBanner>
    | MsgOf<typeof FeeWidget>
    | Extract<
          MsgOf<typeof CTA>,
          { type: 'on_confirm_form_close' | 'import_keys_button_clicked' }
      >
    | Extract<
          MsgOf<typeof FormInputsLayout>,
          {
              type:
                  | 'on_sender_account_clicked'
                  | 'on_source_asset_clicked'
                  | 'on_from_amount_change'
                  | 'on_from_amount_input_clicked'
          }
      >
    | { type: 'on_topup_clicked'; topUpRequest: CardTopUpRequest }

export const ConfirmStage = ({
    quotePollable,
    installationId,
    accountsMap,
    sourceBalancesAndCurrenciesLoadable,
    onMsg,
}: Props) => {
    const errors =
        validateConfirmStageAsYouType({
            pollable: quotePollable,
            loadable: sourceBalancesAndCurrenciesLoadable,
        }).getFailureReason() || {}

    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'on_confirm_form_close' })}
        >
            <Column spacing={12} fill alignY="stretch">
                <FormInputsLayout
                    quotePollable={quotePollable}
                    sourceBalancesAndCurrenciesLoadable={
                        sourceBalancesAndCurrenciesLoadable
                    }
                    accountsMap={accountsMap}
                    maxBalanceError={null}
                    toAmountInDefaultCurrencyError={
                        errors.toAmountInDefaultCurrencyError || null
                    }
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_sender_account_clicked':
                            case 'on_source_asset_clicked':
                            case 'on_from_amount_change':
                            case 'on_from_amount_input_clicked':
                                onMsg(msg)
                                break
                            case 'on_continue_clicked':
                                validateConfirmStageSubmit({
                                    pollable: quotePollable,
                                    loadable:
                                        sourceBalancesAndCurrenciesLoadable,
                                }).tap((topUpRequest) => {
                                    onMsg({
                                        type: 'on_topup_clicked',
                                        topUpRequest,
                                    })
                                })
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
                <Column spacing={12} alignY="end">
                    <ErrorBanner
                        aboveKeypadError={errors.errorBanner}
                        installationId={installationId}
                        onMsg={onMsg}
                    />

                    <FeeWidget
                        sourceBalancesAndCurrenciesLoadable={
                            sourceBalancesAndCurrenciesLoadable
                        }
                        quotePollable={quotePollable}
                        onMsg={onMsg}
                    />

                    <CTA
                        error={errors.submitButton}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'on_deposit_clicked':
                                    validateConfirmStageSubmit({
                                        pollable: quotePollable,
                                        loadable:
                                            sourceBalancesAndCurrenciesLoadable,
                                    }).tap((topUpRequest) => {
                                        onMsg({
                                            type: 'on_topup_clicked',
                                            topUpRequest,
                                        })
                                    })
                                    break
                                case 'on_confirm_form_close':
                                case 'import_keys_button_clicked':
                                    onMsg(msg)
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </Column>
            </Column>
        </Screen>
    )
}

const ErrorBanner = ({
    aboveKeypadError,
    installationId,
    onMsg,
}: {
    aboveKeypadError: ConfirmStageError['errorBanner']
    installationId: string
    onMsg: (
        msg:
            | { type: 'on_value_loss_error_revert_clicked' }
            | { type: 'on_error_retry_clicked' }
    ) => void
}) => {
    switch (aboveKeypadError?.type) {
        case undefined:
        case 'pollable_still_loading':
        case 'from_amount_zero':
            return null
        case 'value_loss_error':
            return (
                <BannerSolid
                    rounded
                    variant="warning"
                    title={
                        <FormattedMessage
                            id="card-add-cash.confirm-stage.banner.value-loss"
                            defaultMessage="You’ll lose {loss} of value"
                            values={{
                                loss: getFormattedPercentage(
                                    aboveKeypadError.percentageOfLoss
                                ),
                            }}
                        />
                    }
                    right={
                        <Tertiary
                            size="regular"
                            color="warning"
                            onClick={() =>
                                onMsg({
                                    type: 'on_value_loss_error_revert_clicked',
                                })
                            }
                        >
                            {({ color, textVariant, textWeight }) => (
                                <>
                                    <Refresh size={14} color={color} />
                                    <Text
                                        color={color}
                                        variant={textVariant}
                                        weight={textWeight}
                                    >
                                        <FormattedMessage
                                            id="card-add-cash.confirm-stage.banner.value-loss.revert"
                                            defaultMessage="Revert"
                                        />
                                    </Text>
                                </>
                            )}
                        </Tertiary>
                    }
                />
            )
        case 'not_enought_balance_for_fee':
            return (
                <BannerSolid
                    rounded
                    variant="warning"
                    title={
                        <FormattedMessage
                            id="card-add-cash.confirm-stage.banner.not-enough-balance-for-fee"
                            defaultMessage="You need {amount} more {symbol} to pay fees"
                            values={{
                                amount: (
                                    <FormattedMoneyPrecise
                                        withSymbol={false}
                                        sign={null}
                                        money={aboveKeypadError.neededAmount}
                                    />
                                ),
                                symbol: aboveKeypadError.neededAmount.currency
                                    .symbol,
                            }}
                        />
                    }
                />
            )
        case 'no_routes':
            return (
                <BannerSolid
                    rounded
                    variant="warning"
                    title={
                        <FormattedMessage
                            id="card-add-cash.confirm-stage.banner.no-routes-found"
                            defaultMessage="No routes, try a different token or amount"
                        />
                    }
                />
            )
        case 'pollable_errored':
            return (
                <AppErrorBanner
                    error={parseAppError(aboveKeypadError.error)}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'try_again_clicked':
                                onMsg({ type: 'on_error_retry_clicked' })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg.type)
                        }
                    }}
                    installationId={installationId}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(aboveKeypadError)
    }
}

const CTA = ({
    error,
    onMsg,
}: {
    error: ConfirmStageError['submitButton']
    onMsg: (
        msg:
            | { type: 'on_confirm_form_close' }
            | { type: 'on_deposit_clicked' }
            | { type: 'import_keys_button_clicked' }
    ) => void
}) => {
    return (
        <Actions variant="default" direction="row">
            <CompressedButton
                size="regular"
                variant="secondary"
                onClick={() => {
                    onMsg({ type: 'on_confirm_form_close' })
                }}
            >
                {({ size, color }) => <BackIcon size={size} color={color} />}
            </CompressedButton>
            {(() => {
                switch (error?.type) {
                    case undefined:
                        return (
                            <Button
                                size="regular"
                                variant="primary"
                                onClick={() =>
                                    onMsg({ type: 'on_deposit_clicked' })
                                }
                            >
                                <FormattedMessage
                                    id="actions.deposit"
                                    defaultMessage="Deposit"
                                />
                            </Button>
                        )
                    case 'loadable_not_loaded':
                        return (
                            <Button
                                size="regular"
                                variant="primary"
                                loading
                                disabled
                            />
                        )

                    case 'no_routes':
                    case 'pollable_errored':
                    case 'pollable_still_loading':
                    case 'not_enought_balance_for_fee':
                    case 'from_amount_zero':
                        // FIXME @resetko-zeal do we need more granular labels here?
                        return (
                            <Button size="regular" variant="primary" disabled>
                                <FormattedMessage
                                    id="actions.deposit"
                                    defaultMessage="Deposit"
                                />
                            </Button>
                        )
                    case 'keys_not_imported':
                        return (
                            <Button
                                size="regular"
                                variant="primary"
                                onClick={() => {
                                    onMsg({
                                        type: 'import_keys_button_clicked',
                                    })
                                }}
                            >
                                <FormattedMessage
                                    id="actions.import-keys"
                                    defaultMessage="Import keys"
                                />
                            </Button>
                        )

                    default:
                        return notReachable(error)
                }
            })()}
        </Actions>
    )
}
