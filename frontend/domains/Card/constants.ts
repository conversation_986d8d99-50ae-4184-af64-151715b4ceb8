import * as Web3 from '@zeal/toolkit/Web3'

import { CountryISOCode } from '@zeal/domains/Country'
import { GNOSIS } from '@zeal/domains/Network/constants'

export const GNOSIS_OG_NFT = Web3.address.staticFromString(
    '0x106c07DD0e77eF4D9C0c612e70E843afAA8E5699'
)

export const APPLE_PAY_NOT_SUPPORTED_COUNTRIES = ['GB', 'CH'] // TODO @kate delete that when gnosis will support apple/google pay for GB and CH

export const GNOSIS_PAY_SESSION_VALIDITY_MINUTES = 45 // Tokens are valid for 1 hour, but we set it to 45min to have a buffer

export const DELAY_QUEUE_POLL_INTERVAL_MS = 10_000

export const CARD_NETWORK = GNOSIS

export const ON_CHAIN_BALANCE_PENDING_BLOCKS_COUNT = 35n

export const SENSITIVE_SECRET_VIEW_TIMEOUT_SECONDS = 15

export const GNOSIS_PAY_APP_HOSTNAME = 'app.gnosispay.com'

export const CARD_TRANSACTIONS_CACHE_COUNT = 10

export const ZEAL_GNOSIS_PAY_PARTNER_ID = 'cmb5bl8cn0w34l59rcyuhzewk'

export const ZEAL_GNOSIS_PAY_COUPON_CODE = 'ZEAL100'

export const GNOSIS_PAY_DASHBOARD_COMPLETE_CARD_ORDER_URL =
    'https://app.gnosispay.com/order/verify/kyc/sumsub'
export const GNOSIS_PAY_DASHBOARD_URL = 'https://app.gnosispay.com/dashboard'
export const GNOSIS_PAY_DASHBOARD_CARD_URL =
    'https://app.gnosispay.com/dashboard/card'
export const GNOSIS_PAY_ADD_CARD_OWNER_HELP =
    'https://www.youtube.com/shorts/vOTpFWHhdnE?feature=share'
export const GNOSIS_PAY_TNC_URL =
    'https://legal.gnosispay.com/en/articles/8911632-gnosis-pay-terms-of-service'

export const GNOSIS_PAY_MONOVATE_TNC_URL =
    'https://legal.gnosispay.com/en/articles/8911633-monavate-cardholder-terms-eea'

export const GNOSIS_PAY_KEYS_SAFE_TO_REPORT = [
    'accepted',
    'acceptedAt',
    'acceptedVersion',
    'activatedAt',
    'active',
    'accountStatus',
    'address',
    'approved',
    'availableFeatures',
    'cardOrders',
    'cards',
    'chainId',
    'consolidatedStatus',
    'couponCode',
    'createdAt',
    'currentVersion',
    'emailVerified',
    'fiatSymbol',
    'hasAccess',
    'hasNoApprovals',
    'id',
    'isDeployed',
    'isPhoneValidated',
    'isSourceOfFundsAnswered',
    'kycProviderId',
    'kycProviders',
    'kycStatus',
    'marketingCampaign',
    'orders',
    'personalizationSource',
    'phoneVerified',
    'processingStatus',
    'providerType',
    'referalCouponCode',
    'referrerCode',
    'safe',
    'safeAccount',
    'safeWallets',
    'signInWallets',
    'sowEmailSentAt',
    'sourceOfFunds',
    'state',
    'status',
    'terms',
    'tokenSymbol',
    'totalAmountEUR',
    'totalDiscountEUR',
    'transactionHash',
    'type',
    'updatedAt',
    'url',
    'user',
    'userId',
    'verificationStatus',
    'verifiedEOA',
    'virtual',
]

export const DELAY_MODULE_ABI = [
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                internalType: 'uint256',
                name: 'queueNonce',
                type: 'uint256',
            },
            {
                indexed: true,
                internalType: 'bytes32',
                name: 'txHash',
                type: 'bytes32',
            },
            {
                indexed: false,
                internalType: 'address',
                name: 'to',
                type: 'address',
            },
            {
                indexed: false,
                internalType: 'uint256',
                name: 'value',
                type: 'uint256',
            },
            {
                indexed: false,
                internalType: 'bytes',
                name: 'data',
                type: 'bytes',
            },
            {
                indexed: false,
                internalType: 'enum Enum.Operation',
                name: 'operation',
                type: 'uint8',
            },
        ],
        name: 'TransactionAdded',
        type: 'event',
    },
    {
        inputs: [{ internalType: 'bytes', name: 'initParams', type: 'bytes' }],
        name: 'setUp',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'uint256', name: '_nonce', type: 'uint256' }],
        name: 'getTxCreatedAt',
        outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            {
                internalType: 'address',
                name: 'module',
                type: 'address',
            },
        ],
        name: 'enableModule',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'prevModule', type: 'address' },
            { internalType: 'address', name: 'module', type: 'address' },
        ],
        name: 'disableModule',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            {
                internalType: 'address',
                name: 'to',
                type: 'address',
            },
            {
                internalType: 'uint256',
                name: 'value',
                type: 'uint256',
            },
            {
                internalType: 'bytes',
                name: 'data',
                type: 'bytes',
            },
            {
                internalType: 'enum Enum.Operation',
                name: 'operation',
                type: 'uint8',
            },
        ],
        name: 'execTransactionFromModule',
        outputs: [
            {
                internalType: 'bool',
                name: 'success',
                type: 'bool',
            },
        ],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'start', type: 'address' },
            { internalType: 'uint256', name: 'pageSize', type: 'uint256' },
        ],
        name: 'getModulesPaginated',
        outputs: [
            { internalType: 'address[]', name: 'array', type: 'address[]' },
            { internalType: 'address', name: 'next', type: 'address' },
        ],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [],
        name: 'txNonce',
        outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [],
        name: 'queueNonce',
        outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
] as const

export const CARD_SAFE_ABI = [
    { inputs: [], stateMutability: 'nonpayable', type: 'constructor' },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'address',
                name: 'owner',
                type: 'address',
            },
        ],
        name: 'AddedOwner',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                internalType: 'bytes32',
                name: 'approvedHash',
                type: 'bytes32',
            },
            {
                indexed: true,
                internalType: 'address',
                name: 'owner',
                type: 'address',
            },
        ],
        name: 'ApproveHash',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'address',
                name: 'handler',
                type: 'address',
            },
        ],
        name: 'ChangedFallbackHandler',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'address',
                name: 'guard',
                type: 'address',
            },
        ],
        name: 'ChangedGuard',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'uint256',
                name: 'threshold',
                type: 'uint256',
            },
        ],
        name: 'ChangedThreshold',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'address',
                name: 'module',
                type: 'address',
            },
        ],
        name: 'DisabledModule',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'address',
                name: 'module',
                type: 'address',
            },
        ],
        name: 'EnabledModule',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'bytes32',
                name: 'txHash',
                type: 'bytes32',
            },
            {
                indexed: false,
                internalType: 'uint256',
                name: 'payment',
                type: 'uint256',
            },
        ],
        name: 'ExecutionFailure',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                internalType: 'address',
                name: 'module',
                type: 'address',
            },
        ],
        name: 'ExecutionFromModuleFailure',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                internalType: 'address',
                name: 'module',
                type: 'address',
            },
        ],
        name: 'ExecutionFromModuleSuccess',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'bytes32',
                name: 'txHash',
                type: 'bytes32',
            },
            {
                indexed: false,
                internalType: 'uint256',
                name: 'payment',
                type: 'uint256',
            },
        ],
        name: 'ExecutionSuccess',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'address',
                name: 'owner',
                type: 'address',
            },
        ],
        name: 'RemovedOwner',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                internalType: 'address',
                name: 'sender',
                type: 'address',
            },
            {
                indexed: false,
                internalType: 'uint256',
                name: 'value',
                type: 'uint256',
            },
        ],
        name: 'SafeReceived',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                internalType: 'address',
                name: 'initiator',
                type: 'address',
            },
            {
                indexed: false,
                internalType: 'address[]',
                name: 'owners',
                type: 'address[]',
            },
            {
                indexed: false,
                internalType: 'uint256',
                name: 'threshold',
                type: 'uint256',
            },
            {
                indexed: false,
                internalType: 'address',
                name: 'initializer',
                type: 'address',
            },
            {
                indexed: false,
                internalType: 'address',
                name: 'fallbackHandler',
                type: 'address',
            },
        ],
        name: 'SafeSetup',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                internalType: 'bytes32',
                name: 'msgHash',
                type: 'bytes32',
            },
        ],
        name: 'SignMsg',
        type: 'event',
    },
    { stateMutability: 'nonpayable', type: 'fallback' },
    {
        inputs: [],
        name: 'VERSION',
        outputs: [{ internalType: 'string', name: '', type: 'string' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'owner', type: 'address' },
            { internalType: 'uint256', name: '_threshold', type: 'uint256' },
        ],
        name: 'addOwnerWithThreshold',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'bytes32', name: 'hashToApprove', type: 'bytes32' },
        ],
        name: 'approveHash',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: '', type: 'address' },
            { internalType: 'bytes32', name: '', type: 'bytes32' },
        ],
        name: 'approvedHashes',
        outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'uint256', name: '_threshold', type: 'uint256' },
        ],
        name: 'changeThreshold',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'bytes32', name: 'dataHash', type: 'bytes32' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            { internalType: 'bytes', name: 'signatures', type: 'bytes' },
            {
                internalType: 'uint256',
                name: 'requiredSignatures',
                type: 'uint256',
            },
        ],
        name: 'checkNSignatures',
        outputs: [],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'bytes32', name: 'dataHash', type: 'bytes32' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            { internalType: 'bytes', name: 'signatures', type: 'bytes' },
        ],
        name: 'checkSignatures',
        outputs: [],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'prevModule', type: 'address' },
            { internalType: 'address', name: 'module', type: 'address' },
        ],
        name: 'disableModule',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [],
        name: 'domainSeparator',
        outputs: [{ internalType: 'bytes32', name: '', type: 'bytes32' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'address', name: 'module', type: 'address' }],
        name: 'enableModule',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'uint256', name: 'value', type: 'uint256' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            {
                internalType: 'enum Enum.Operation',
                name: 'operation',
                type: 'uint8',
            },
            { internalType: 'uint256', name: 'safeTxGas', type: 'uint256' },
            { internalType: 'uint256', name: 'baseGas', type: 'uint256' },
            { internalType: 'uint256', name: 'gasPrice', type: 'uint256' },
            { internalType: 'address', name: 'gasToken', type: 'address' },
            {
                internalType: 'address',
                name: 'refundReceiver',
                type: 'address',
            },
            { internalType: 'uint256', name: '_nonce', type: 'uint256' },
        ],
        name: 'encodeTransactionData',
        outputs: [{ internalType: 'bytes', name: '', type: 'bytes' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'uint256', name: 'value', type: 'uint256' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            {
                internalType: 'enum Enum.Operation',
                name: 'operation',
                type: 'uint8',
            },
            { internalType: 'uint256', name: 'safeTxGas', type: 'uint256' },
            { internalType: 'uint256', name: 'baseGas', type: 'uint256' },
            { internalType: 'uint256', name: 'gasPrice', type: 'uint256' },
            { internalType: 'address', name: 'gasToken', type: 'address' },
            {
                internalType: 'address payable',
                name: 'refundReceiver',
                type: 'address',
            },
            { internalType: 'bytes', name: 'signatures', type: 'bytes' },
        ],
        name: 'execTransaction',
        outputs: [{ internalType: 'bool', name: 'success', type: 'bool' }],
        stateMutability: 'payable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'uint256', name: 'value', type: 'uint256' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            {
                internalType: 'enum Enum.Operation',
                name: 'operation',
                type: 'uint8',
            },
        ],
        name: 'execTransactionFromModule',
        outputs: [{ internalType: 'bool', name: 'success', type: 'bool' }],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'uint256', name: 'value', type: 'uint256' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            {
                internalType: 'enum Enum.Operation',
                name: 'operation',
                type: 'uint8',
            },
        ],
        name: 'execTransactionFromModuleReturnData',
        outputs: [
            { internalType: 'bool', name: 'success', type: 'bool' },
            { internalType: 'bytes', name: 'returnData', type: 'bytes' },
        ],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [],
        name: 'getChainId',
        outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'start', type: 'address' },
            { internalType: 'uint256', name: 'pageSize', type: 'uint256' },
        ],
        name: 'getModulesPaginated',
        outputs: [
            { internalType: 'address[]', name: 'array', type: 'address[]' },
            { internalType: 'address', name: 'next', type: 'address' },
        ],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [],
        name: 'getOwners',
        outputs: [{ internalType: 'address[]', name: '', type: 'address[]' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'uint256', name: 'offset', type: 'uint256' },
            { internalType: 'uint256', name: 'length', type: 'uint256' },
        ],
        name: 'getStorageAt',
        outputs: [{ internalType: 'bytes', name: '', type: 'bytes' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [],
        name: 'getThreshold',
        outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'uint256', name: 'value', type: 'uint256' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            {
                internalType: 'enum Enum.Operation',
                name: 'operation',
                type: 'uint8',
            },
            { internalType: 'uint256', name: 'safeTxGas', type: 'uint256' },
            { internalType: 'uint256', name: 'baseGas', type: 'uint256' },
            { internalType: 'uint256', name: 'gasPrice', type: 'uint256' },
            { internalType: 'address', name: 'gasToken', type: 'address' },
            {
                internalType: 'address',
                name: 'refundReceiver',
                type: 'address',
            },
            { internalType: 'uint256', name: '_nonce', type: 'uint256' },
        ],
        name: 'getTransactionHash',
        outputs: [{ internalType: 'bytes32', name: '', type: 'bytes32' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'address', name: 'module', type: 'address' }],
        name: 'isModuleEnabled',
        outputs: [{ internalType: 'bool', name: '', type: 'bool' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'address', name: 'owner', type: 'address' }],
        name: 'isOwner',
        outputs: [{ internalType: 'bool', name: '', type: 'bool' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [],
        name: 'nonce',
        outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'prevOwner', type: 'address' },
            { internalType: 'address', name: 'owner', type: 'address' },
            { internalType: 'uint256', name: '_threshold', type: 'uint256' },
        ],
        name: 'removeOwner',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'uint256', name: 'value', type: 'uint256' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            {
                internalType: 'enum Enum.Operation',
                name: 'operation',
                type: 'uint8',
            },
        ],
        name: 'requiredTxGas',
        outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'address', name: 'handler', type: 'address' }],
        name: 'setFallbackHandler',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'address', name: 'guard', type: 'address' }],
        name: 'setGuard',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address[]', name: '_owners', type: 'address[]' },
            { internalType: 'uint256', name: '_threshold', type: 'uint256' },
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            {
                internalType: 'address',
                name: 'fallbackHandler',
                type: 'address',
            },
            { internalType: 'address', name: 'paymentToken', type: 'address' },
            { internalType: 'uint256', name: 'payment', type: 'uint256' },
            {
                internalType: 'address payable',
                name: 'paymentReceiver',
                type: 'address',
            },
        ],
        name: 'setup',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'bytes32', name: '', type: 'bytes32' }],
        name: 'signedMessages',
        outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            {
                internalType: 'address',
                name: 'targetContract',
                type: 'address',
            },
            { internalType: 'bytes', name: 'calldataPayload', type: 'bytes' },
        ],
        name: 'simulateAndRevert',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'prevOwner', type: 'address' },
            { internalType: 'address', name: 'oldOwner', type: 'address' },
            { internalType: 'address', name: 'newOwner', type: 'address' },
        ],
        name: 'swapOwner',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    { stateMutability: 'payable', type: 'receive' },
] as const

export const countriesSupportedByGnosisPay: Array<CountryISOCode> = [
    'AT',
    'BE',
    'BG',
    'BR',
    'HR',
    'CY',
    'CZ',
    'DK',
    'EE',
    'FI',
    'FR',
    'DE',
    'GR',
    'HU',
    'IS',
    'IE',
    'IT',
    'LV',
    'LI',
    'LT',
    'LU',
    'MT',
    'NL',
    'NO',
    'PL',
    'PT',
    'RO',
    'SK',
    'SI',
    'ES',
    'SE',
    'CH',
    'GB',
]

export const ROLES_ABI = [
    {
        inputs: [
            { internalType: 'address', name: '_owner', type: 'address' },
            { internalType: 'address', name: '_avatar', type: 'address' },
            { internalType: 'address', name: '_target', type: 'address' },
        ],
        stateMutability: 'nonpayable',
        type: 'constructor',
    },
    {
        inputs: [{ internalType: 'address', name: 'module', type: 'address' }],
        name: 'AlreadyDisabledModule',
        type: 'error',
    },
    {
        inputs: [{ internalType: 'address', name: 'module', type: 'address' }],
        name: 'AlreadyEnabledModule',
        type: 'error',
    },
    { inputs: [], name: 'ArraysDifferentLength', type: 'error' },
    { inputs: [], name: 'CalldataOutOfBounds', type: 'error' },
    {
        inputs: [
            {
                internalType: 'enum PermissionChecker.Status',
                name: 'status',
                type: 'uint8',
            },
            { internalType: 'bytes32', name: 'info', type: 'bytes32' },
        ],
        name: 'ConditionViolation',
        type: 'error',
    },
    { inputs: [], name: 'FunctionSignatureTooShort', type: 'error' },
    {
        inputs: [{ internalType: 'bytes32', name: '', type: 'bytes32' }],
        name: 'HashAlreadyConsumed',
        type: 'error',
    },
    { inputs: [], name: 'InvalidInitialization', type: 'error' },
    {
        inputs: [{ internalType: 'address', name: 'module', type: 'address' }],
        name: 'InvalidModule',
        type: 'error',
    },
    { inputs: [], name: 'InvalidPageSize', type: 'error' },
    { inputs: [], name: 'MalformedMultiEntrypoint', type: 'error' },
    { inputs: [], name: 'ModuleTransactionFailed', type: 'error' },
    { inputs: [], name: 'NoMembership', type: 'error' },
    {
        inputs: [{ internalType: 'address', name: 'sender', type: 'address' }],
        name: 'NotAuthorized',
        type: 'error',
    },
    { inputs: [], name: 'NotInitializing', type: 'error' },
    {
        inputs: [{ internalType: 'address', name: 'owner', type: 'address' }],
        name: 'OwnableInvalidOwner',
        type: 'error',
    },
    {
        inputs: [{ internalType: 'address', name: 'account', type: 'address' }],
        name: 'OwnableUnauthorizedAccount',
        type: 'error',
    },
    { inputs: [], name: 'SetupModulesAlreadyCalled', type: 'error' },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'bytes32',
                name: 'roleKey',
                type: 'bytes32',
            },
            {
                indexed: false,
                internalType: 'address',
                name: 'targetAddress',
                type: 'address',
            },
            {
                indexed: false,
                internalType: 'bytes4',
                name: 'selector',
                type: 'bytes4',
            },
            {
                indexed: false,
                internalType: 'enum ExecutionOptions',
                name: 'options',
                type: 'uint8',
            },
        ],
        name: 'AllowFunction',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'bytes32',
                name: 'roleKey',
                type: 'bytes32',
            },
            {
                indexed: false,
                internalType: 'address',
                name: 'targetAddress',
                type: 'address',
            },
            {
                indexed: false,
                internalType: 'enum ExecutionOptions',
                name: 'options',
                type: 'uint8',
            },
        ],
        name: 'AllowTarget',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'address',
                name: 'module',
                type: 'address',
            },
            {
                indexed: false,
                internalType: 'bytes32[]',
                name: 'roleKeys',
                type: 'bytes32[]',
            },
            {
                indexed: false,
                internalType: 'bool[]',
                name: 'memberOf',
                type: 'bool[]',
            },
        ],
        name: 'AssignRoles',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                internalType: 'address',
                name: 'previousAvatar',
                type: 'address',
            },
            {
                indexed: true,
                internalType: 'address',
                name: 'newAvatar',
                type: 'address',
            },
        ],
        name: 'AvatarSet',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'bytes32',
                name: 'allowanceKey',
                type: 'bytes32',
            },
            {
                indexed: false,
                internalType: 'uint128',
                name: 'consumed',
                type: 'uint128',
            },
            {
                indexed: false,
                internalType: 'uint128',
                name: 'newBalance',
                type: 'uint128',
            },
        ],
        name: 'ConsumeAllowance',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'address',
                name: 'module',
                type: 'address',
            },
        ],
        name: 'DisabledModule',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'address',
                name: 'module',
                type: 'address',
            },
        ],
        name: 'EnabledModule',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                internalType: 'address',
                name: 'module',
                type: 'address',
            },
        ],
        name: 'ExecutionFromModuleFailure',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                internalType: 'address',
                name: 'module',
                type: 'address',
            },
        ],
        name: 'ExecutionFromModuleSuccess',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'bytes32',
                name: '',
                type: 'bytes32',
            },
        ],
        name: 'HashExecuted',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'bytes32',
                name: '',
                type: 'bytes32',
            },
        ],
        name: 'HashInvalidated',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'uint64',
                name: 'version',
                type: 'uint64',
            },
        ],
        name: 'Initialized',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                internalType: 'address',
                name: 'previousOwner',
                type: 'address',
            },
            {
                indexed: true,
                internalType: 'address',
                name: 'newOwner',
                type: 'address',
            },
        ],
        name: 'OwnershipTransferred',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'bytes32',
                name: 'roleKey',
                type: 'bytes32',
            },
            {
                indexed: false,
                internalType: 'address',
                name: 'targetAddress',
                type: 'address',
            },
            {
                indexed: false,
                internalType: 'bytes4',
                name: 'selector',
                type: 'bytes4',
            },
        ],
        name: 'RevokeFunction',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'bytes32',
                name: 'roleKey',
                type: 'bytes32',
            },
            {
                indexed: false,
                internalType: 'address',
                name: 'targetAddress',
                type: 'address',
            },
        ],
        name: 'RevokeTarget',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                internalType: 'address',
                name: 'initiator',
                type: 'address',
            },
            {
                indexed: true,
                internalType: 'address',
                name: 'owner',
                type: 'address',
            },
            {
                indexed: true,
                internalType: 'address',
                name: 'avatar',
                type: 'address',
            },
            {
                indexed: false,
                internalType: 'address',
                name: 'target',
                type: 'address',
            },
        ],
        name: 'RolesModSetup',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'bytes32',
                name: 'roleKey',
                type: 'bytes32',
            },
            {
                indexed: false,
                internalType: 'address',
                name: 'targetAddress',
                type: 'address',
            },
            {
                indexed: false,
                internalType: 'bytes4',
                name: 'selector',
                type: 'bytes4',
            },
            {
                components: [
                    { internalType: 'uint8', name: 'parent', type: 'uint8' },
                    {
                        internalType: 'enum ParameterType',
                        name: 'paramType',
                        type: 'uint8',
                    },
                    {
                        internalType: 'enum Operator',
                        name: 'operator',
                        type: 'uint8',
                    },
                    { internalType: 'bytes', name: 'compValue', type: 'bytes' },
                ],
                indexed: false,
                internalType: 'struct ConditionFlat[]',
                name: 'conditions',
                type: 'tuple[]',
            },
            {
                indexed: false,
                internalType: 'enum ExecutionOptions',
                name: 'options',
                type: 'uint8',
            },
        ],
        name: 'ScopeFunction',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'bytes32',
                name: 'roleKey',
                type: 'bytes32',
            },
            {
                indexed: false,
                internalType: 'address',
                name: 'targetAddress',
                type: 'address',
            },
        ],
        name: 'ScopeTarget',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'bytes32',
                name: 'allowanceKey',
                type: 'bytes32',
            },
            {
                indexed: false,
                internalType: 'uint128',
                name: 'balance',
                type: 'uint128',
            },
            {
                indexed: false,
                internalType: 'uint128',
                name: 'maxRefill',
                type: 'uint128',
            },
            {
                indexed: false,
                internalType: 'uint128',
                name: 'refill',
                type: 'uint128',
            },
            {
                indexed: false,
                internalType: 'uint64',
                name: 'period',
                type: 'uint64',
            },
            {
                indexed: false,
                internalType: 'uint64',
                name: 'timestamp',
                type: 'uint64',
            },
        ],
        name: 'SetAllowance',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'address',
                name: 'module',
                type: 'address',
            },
            {
                indexed: false,
                internalType: 'bytes32',
                name: 'defaultRoleKey',
                type: 'bytes32',
            },
        ],
        name: 'SetDefaultRole',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: false,
                internalType: 'address',
                name: 'to',
                type: 'address',
            },
            {
                indexed: false,
                internalType: 'bytes4',
                name: 'selector',
                type: 'bytes4',
            },
            {
                indexed: false,
                internalType: 'contract ITransactionUnwrapper',
                name: 'adapter',
                type: 'address',
            },
        ],
        name: 'SetUnwrapAdapter',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                internalType: 'address',
                name: 'previousTarget',
                type: 'address',
            },
            {
                indexed: true,
                internalType: 'address',
                name: 'newTarget',
                type: 'address',
            },
        ],
        name: 'TargetSet',
        type: 'event',
    },
    {
        inputs: [
            { internalType: 'bytes32', name: 'roleKey', type: 'bytes32' },
            { internalType: 'address', name: 'targetAddress', type: 'address' },
            { internalType: 'bytes4', name: 'selector', type: 'bytes4' },
            {
                internalType: 'enum ExecutionOptions',
                name: 'options',
                type: 'uint8',
            },
        ],
        name: 'allowFunction',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'bytes32', name: 'roleKey', type: 'bytes32' },
            { internalType: 'address', name: 'targetAddress', type: 'address' },
            {
                internalType: 'enum ExecutionOptions',
                name: 'options',
                type: 'uint8',
            },
        ],
        name: 'allowTarget',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'bytes32', name: '', type: 'bytes32' }],
        name: 'allowances',
        outputs: [
            { internalType: 'uint128', name: 'refill', type: 'uint128' },
            { internalType: 'uint128', name: 'maxRefill', type: 'uint128' },
            { internalType: 'uint64', name: 'period', type: 'uint64' },
            { internalType: 'uint128', name: 'balance', type: 'uint128' },
            { internalType: 'uint64', name: 'timestamp', type: 'uint64' },
        ],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'module', type: 'address' },
            { internalType: 'bytes32[]', name: 'roleKeys', type: 'bytes32[]' },
            { internalType: 'bool[]', name: 'memberOf', type: 'bool[]' },
        ],
        name: 'assignRoles',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [],
        name: 'avatar',
        outputs: [{ internalType: 'address', name: '', type: 'address' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: '', type: 'address' },
            { internalType: 'bytes32', name: '', type: 'bytes32' },
        ],
        name: 'consumed',
        outputs: [{ internalType: 'bool', name: '', type: 'bool' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'address', name: '', type: 'address' }],
        name: 'defaultRoles',
        outputs: [{ internalType: 'bytes32', name: '', type: 'bytes32' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'prevModule', type: 'address' },
            { internalType: 'address', name: 'module', type: 'address' },
        ],
        name: 'disableModule',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'address', name: 'module', type: 'address' }],
        name: 'enableModule',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'uint256', name: 'value', type: 'uint256' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            {
                internalType: 'enum Enum.Operation',
                name: 'operation',
                type: 'uint8',
            },
        ],
        name: 'execTransactionFromModule',
        outputs: [{ internalType: 'bool', name: 'success', type: 'bool' }],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'uint256', name: 'value', type: 'uint256' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            {
                internalType: 'enum Enum.Operation',
                name: 'operation',
                type: 'uint8',
            },
        ],
        name: 'execTransactionFromModuleReturnData',
        outputs: [
            { internalType: 'bool', name: 'success', type: 'bool' },
            { internalType: 'bytes', name: 'returnData', type: 'bytes' },
        ],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'uint256', name: 'value', type: 'uint256' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            {
                internalType: 'enum Enum.Operation',
                name: 'operation',
                type: 'uint8',
            },
            { internalType: 'bytes32', name: 'roleKey', type: 'bytes32' },
            { internalType: 'bool', name: 'shouldRevert', type: 'bool' },
        ],
        name: 'execTransactionWithRole',
        outputs: [{ internalType: 'bool', name: 'success', type: 'bool' }],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'uint256', name: 'value', type: 'uint256' },
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            {
                internalType: 'enum Enum.Operation',
                name: 'operation',
                type: 'uint8',
            },
            { internalType: 'bytes32', name: 'roleKey', type: 'bytes32' },
            { internalType: 'bool', name: 'shouldRevert', type: 'bool' },
        ],
        name: 'execTransactionWithRoleReturnData',
        outputs: [
            { internalType: 'bool', name: 'success', type: 'bool' },
            { internalType: 'bytes', name: 'returnData', type: 'bytes' },
        ],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'start', type: 'address' },
            { internalType: 'uint256', name: 'pageSize', type: 'uint256' },
        ],
        name: 'getModulesPaginated',
        outputs: [
            { internalType: 'address[]', name: 'array', type: 'address[]' },
            { internalType: 'address', name: 'next', type: 'address' },
        ],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'bytes32', name: 'hash', type: 'bytes32' }],
        name: 'invalidate',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'address', name: '_module', type: 'address' }],
        name: 'isModuleEnabled',
        outputs: [{ internalType: 'bool', name: '', type: 'bool' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'bytes', name: 'data', type: 'bytes' },
            { internalType: 'bytes32', name: 'salt', type: 'bytes32' },
        ],
        name: 'moduleTxHash',
        outputs: [{ internalType: 'bytes32', name: '', type: 'bytes32' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [],
        name: 'owner',
        outputs: [{ internalType: 'address', name: '', type: 'address' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [],
        name: 'renounceOwnership',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'bytes32', name: 'roleKey', type: 'bytes32' },
            { internalType: 'address', name: 'targetAddress', type: 'address' },
            { internalType: 'bytes4', name: 'selector', type: 'bytes4' },
        ],
        name: 'revokeFunction',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'bytes32', name: 'roleKey', type: 'bytes32' },
            { internalType: 'address', name: 'targetAddress', type: 'address' },
        ],
        name: 'revokeTarget',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'bytes32', name: 'roleKey', type: 'bytes32' },
            { internalType: 'address', name: 'targetAddress', type: 'address' },
            { internalType: 'bytes4', name: 'selector', type: 'bytes4' },
            {
                components: [
                    { internalType: 'uint8', name: 'parent', type: 'uint8' },
                    {
                        internalType: 'enum ParameterType',
                        name: 'paramType',
                        type: 'uint8',
                    },
                    {
                        internalType: 'enum Operator',
                        name: 'operator',
                        type: 'uint8',
                    },
                    { internalType: 'bytes', name: 'compValue', type: 'bytes' },
                ],
                internalType: 'struct ConditionFlat[]',
                name: 'conditions',
                type: 'tuple[]',
            },
            {
                internalType: 'enum ExecutionOptions',
                name: 'options',
                type: 'uint8',
            },
        ],
        name: 'scopeFunction',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'bytes32', name: 'roleKey', type: 'bytes32' },
            { internalType: 'address', name: 'targetAddress', type: 'address' },
        ],
        name: 'scopeTarget',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'bytes32', name: 'key', type: 'bytes32' },
            { internalType: 'uint128', name: 'balance', type: 'uint128' },
            { internalType: 'uint128', name: 'maxRefill', type: 'uint128' },
            { internalType: 'uint128', name: 'refill', type: 'uint128' },
            { internalType: 'uint64', name: 'period', type: 'uint64' },
            { internalType: 'uint64', name: 'timestamp', type: 'uint64' },
        ],
        name: 'setAllowance',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'address', name: '_avatar', type: 'address' }],
        name: 'setAvatar',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'module', type: 'address' },
            { internalType: 'bytes32', name: 'roleKey', type: 'bytes32' },
        ],
        name: 'setDefaultRole',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'address', name: '_target', type: 'address' }],
        name: 'setTarget',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'bytes4', name: 'selector', type: 'bytes4' },
            {
                internalType: 'contract ITransactionUnwrapper',
                name: 'adapter',
                type: 'address',
            },
        ],
        name: 'setTransactionUnwrapper',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'bytes', name: 'initParams', type: 'bytes' }],
        name: 'setUp',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [],
        name: 'target',
        outputs: [{ internalType: 'address', name: '', type: 'address' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { internalType: 'address', name: 'newOwner', type: 'address' },
        ],
        name: 'transferOwnership',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'bytes32', name: '', type: 'bytes32' }],
        name: 'unwrappers',
        outputs: [
            {
                internalType: 'contract ITransactionUnwrapper',
                name: '',
                type: 'address',
            },
        ],
        stateMutability: 'view',
        type: 'function',
    },
] as const
