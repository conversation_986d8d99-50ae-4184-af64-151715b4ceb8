import { get } from '@zeal/api/gnosisApi'

import { ImperativeError } from '@zeal/toolkit/Error'
import { memoizeOne } from '@zeal/toolkit/Function/memoizeOne'
import { parse as parseJSON } from '@zeal/toolkit/JSON'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import { bigint, object, shape, string } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    fetchBalanceOfToken,
    requestBalanceOfToken,
} from '@zeal/domains/Address/api/fetchBalanceOf'
import {
    CARD_NETWORK,
    ON_CHAIN_BALANCE_PENDING_BLOCKS_COUNT,
} from '@zeal/domains/Card/constants'
import { CryptoCurrency, FiatCurrency } from '@zeal/domains/Currency'
import { fetchPriceChange } from '@zeal/domains/Currency/api/fetchPriceChange'
import { GNOSIS_GNO } from '@zeal/domains/Currency/constants'
import { convertStableCoinCurrencyToFiatCurrency } from '@zeal/domains/Currency/helpers/convertStableCoinCurrencyToFiatCurrency'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import {
    fetchRate,
    fetchRatesForDefaultCurrency,
} from '@zeal/domains/FXRate/api/fetchRate'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { FiatMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import {
    fetchRPCBatch2,
    fetchRPCResponseWithRetry,
} from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { loginWithCache } from './login'

import {
    CardBalance,
    CardSafeFullyConfigured,
    CardSlientSignKeyStore,
    GnosisPayLoginInfo,
} from '..'

type Params = {
    gnosisPayLoginInfo: GnosisPayLoginInfo
    cardCryptoCurrency: CryptoCurrency
    cardAddress: Web3.address.Address
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    signal?: AbortSignal
}

const getFiatFractionAdjustment = (
    cardCryptoCurrency: CryptoCurrency,
    cardFiatCurrency: FiatCurrency
): bigint => {
    const baseFraction = cardCryptoCurrency.rateFraction
    const quoteFraction = cardFiatCurrency.rateFraction

    return BigInt(10 ** (quoteFraction - baseFraction))
}

export const fetchBalance = async ({
    gnosisPayLoginInfo,
    cardCryptoCurrency,
    cardAddress,
    networkRPCMap,
    defaultCurrencyConfig,
    networkMap,
    signal,
}: Params): Promise<CardBalance> => {
    const [response, gnoAmount, rate, priceChange24h] = await Promise.all([
        get(
            '/account-balances',
            {
                auth: { type: 'bearer_token', token: gnosisPayLoginInfo.token },
            },
            signal
        ),
        fetchBalanceOfToken({
            address: cardAddress,
            tokenAddress: GNOSIS_GNO.address,
            network: CARD_NETWORK,
            networkRPCMap,
            signal,
            block: 'latest',
        }),
        fetchRate({
            cryptoCurrency: GNOSIS_GNO,
            defaultCurrencyConfig,
            networkMap,
            networkRPCMap,
            signal,
        }),
        fetchPriceChange({
            cryptoCurrency: GNOSIS_GNO,
            networkMap,
            signal,
        }),
    ])

    const cardFiatCurrency = convertStableCoinCurrencyToFiatCurrency({
        cryptoCurrency: cardCryptoCurrency,
    })

    const fiatFractionAdjustment = getFiatFractionAdjustment(
        cardCryptoCurrency,
        cardFiatCurrency
    )

    const cardBalance = string(response)
        .andThen(parseJSON)
        .andThen(object)
        .andThen((obj) =>
            shape({
                total: bigint(obj.total),
                spendable: bigint(obj.spendable),
                pending: bigint(obj.pending),
            }).map(({ total, pending, spendable }) => ({
                total: {
                    amount: total * fiatFractionAdjustment,
                    currency: cardFiatCurrency,
                },
                totalInDefaultCurrency: null,
                spendable: {
                    amount: spendable * fiatFractionAdjustment,
                    currency: cardFiatCurrency,
                },
                pending: {
                    amount: pending * fiatFractionAdjustment,
                    currency: cardFiatCurrency,
                },
            }))
        )
        .getSuccessResultOrThrow('Failed to parse account balance')

    return {
        ...cardBalance,
        cashbackTokenBalance: { amount: gnoAmount, currency: GNOSIS_GNO },
        cashbackTokenBalanceInDefaultCurrency: rate
            ? applyRate2({
                  baseAmount: { amount: gnoAmount, currency: GNOSIS_GNO },
                  rate,
              })
            : null,
        cashBackPriceChange24H: priceChange24h,
        cashBackRate: rate,
    }
}

type ParamsFetchBalanceOfCardOnChain = {
    cardCryptoCurrency: CryptoCurrency
    cardAddress: Web3.address.Address
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    signal?: AbortSignal
}

export const fetchBalanceOfCardOnChain = async ({
    cardCryptoCurrency,
    cardAddress,
    defaultCurrencyConfig,
    networkRPCMap,
    networkMap,
    signal,
}: ParamsFetchBalanceOfCardOnChain): Promise<CardBalance> => {
    const currentBlockResponse = await fetchRPCResponseWithRetry({
        network: CARD_NETWORK,
        networkRPCMap,
        request: {
            id: generateRandomNumber(),
            jsonrpc: '2.0',
            method: 'eth_blockNumber',
            params: [],
        },
    })

    const currentBlock = bigint(currentBlockResponse).getSuccessResultOrThrow(
        'failed to parse safe eth_blockNumber during confirmation check'
    )

    if (currentBlock <= ON_CHAIN_BALANCE_PENDING_BLOCKS_COUNT) {
        captureError(
            new ImperativeError(
                `current block less than ON_CHAIN_BALANCE_PENDING_BLOCKS_COUNT ${ON_CHAIN_BALANCE_PENDING_BLOCKS_COUNT}`
            )
        )
    }
    const address = cardAddress

    const [[current, past, gnoAmount], rates, priceChange24h] =
        await Promise.all([
            fetchRPCBatch2(
                [
                    requestBalanceOfToken({
                        address,
                        tokenAddress: cardCryptoCurrency.address,
                        block: 'latest',
                    }),
                    requestBalanceOfToken({
                        address,
                        tokenAddress: cardCryptoCurrency.address,
                        block:
                            currentBlock -
                            ON_CHAIN_BALANCE_PENDING_BLOCKS_COUNT,
                    }),
                    requestBalanceOfToken({
                        address,
                        tokenAddress: GNOSIS_GNO.address,
                        block: 'latest',
                    }),
                ],
                {
                    network: CARD_NETWORK,
                    networkRPCMap,
                }
            ),

            fetchRatesForDefaultCurrency({
                cryptoCurrencies: [cardCryptoCurrency, GNOSIS_GNO],
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                signal,
            }),

            fetchPriceChange({
                cryptoCurrency: GNOSIS_GNO,
                networkMap,
                signal,
            }),
        ])

    const cardFiatCurrency = convertStableCoinCurrencyToFiatCurrency({
        cryptoCurrency: cardCryptoCurrency,
    })

    const fiatFractionAdjustment = getFiatFractionAdjustment(
        cardCryptoCurrency,
        cardFiatCurrency
    )

    const cardRate = rates[cardCryptoCurrency.id]
    const gnoRate = rates[GNOSIS_GNO.id]

    const totalInDefaultCurrency: FiatMoney | null = cardRate
        ? applyRate2({
              baseAmount: {
                  amount: current,
                  currency: cardCryptoCurrency,
              },
              rate: cardRate,
          })
        : null

    if (current > past) {
        const pending = current - past

        return {
            total: {
                amount: current * fiatFractionAdjustment,
                currency: cardFiatCurrency,
            },
            totalInDefaultCurrency,
            spendable: {
                amount: (current - pending) * fiatFractionAdjustment,
                currency: cardFiatCurrency,
            },
            pending: {
                amount: pending * fiatFractionAdjustment,
                currency: cardFiatCurrency,
            },
            cashbackTokenBalance: { amount: gnoAmount, currency: GNOSIS_GNO },
            cashbackTokenBalanceInDefaultCurrency: gnoRate
                ? applyRate2({
                      baseAmount: { amount: gnoAmount, currency: GNOSIS_GNO },
                      rate: gnoRate,
                  })
                : null,
            cashBackPriceChange24H: priceChange24h,
            cashBackRate: gnoRate,
        }
    }
    return {
        total: {
            amount: current * fiatFractionAdjustment,
            currency: cardFiatCurrency,
        },
        totalInDefaultCurrency,
        spendable: {
            amount: current * fiatFractionAdjustment,
            currency: cardFiatCurrency,
        },
        cashbackTokenBalance: { amount: gnoAmount, currency: GNOSIS_GNO },
        pending: {
            amount: 0n,
            currency: cardFiatCurrency,
        },
        cashbackTokenBalanceInDefaultCurrency: gnoRate
            ? applyRate2({
                  baseAmount: { amount: gnoAmount, currency: GNOSIS_GNO },
                  rate: gnoRate,
              })
            : null,
        cashBackPriceChange24H: priceChange24h,
        cashBackRate: gnoRate,
    }
}

export const fetchBalanceOfCardOnChainWithCache = memoizeOne(
    ({
        cacheKey: _,
        signal: __,
        ...rest
    }: ParamsFetchBalanceOfCardOnChain & {
        cacheKey: string
    }): Promise<CardBalance> => fetchBalanceOfCardOnChain(rest),
    ({ cardAddress, cacheKey }) => `${cardAddress}${cacheKey}`
)

export const fetchBalanceWithSilentSign = async ({
    keyStore,
    sessionPassword,
    readonlySignerAddress,
    networkRPCMap,
    defaultCurrencyConfig,
    networkMap,
    signal,
    cardSafe,
}: {
    cardSafe: CardSafeFullyConfigured
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    readonlySignerAddress: Web3.address.Address
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig

    signal?: AbortSignal
}): Promise<CardBalance> => {
    const gnosisPayLoginInfo = await loginWithCache({
        keyStore,
        readonlySignerAddress,
        sessionPassword,
        signal,
    })

    return fetchBalance({
        gnosisPayLoginInfo,
        cardCryptoCurrency: cardSafe.cryptoCurrency,
        cardAddress: cardSafe.address,
        defaultCurrencyConfig,
        networkMap,
        signal,
        networkRPCMap,
    })
}
