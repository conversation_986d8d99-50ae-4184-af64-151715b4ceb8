import { notReachable } from '@zeal/toolkit'

import { CARD_NETWORK } from '@zeal/domains/Card/constants'
// eslint-disable-next-line zeal-domains/no-feature-deep-import
import { SubmittedCardTopUp } from '@zeal/domains/Card/features/CardAddCash/CardAddCashV2/types' // FIXME :: @Nicvaniek
import { fetchBungeeIntentQuoteStatus } from '@zeal/domains/Currency/domains/Bungee/api/fetchBungeeIntentQuoteStatus'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { fetchTransaction } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/api/fetchTransaction'
import { fetchSubmittedUserOperation } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation/api/fetchSubmittedUserOperation'

export const fetchSubmittedCardTopUp = async ({
    submittedCardTopUp,
    networkMap,
    networkRPCMap,
    signal,
}: {
    submittedCardTopUp: SubmittedCardTopUp
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<SubmittedCardTopUp> => {
    switch (submittedCardTopUp.type) {
        case 'send_eoa':
            switch (submittedCardTopUp.state.type) {
                case 'waiting_for_transaction': {
                    const submittedTx = await fetchTransaction({
                        transaction:
                            submittedCardTopUp.state.submittedTransaction,
                        networkRPCMap,
                        network: CARD_NETWORK,
                    })

                    switch (submittedTx.state) {
                        case 'queued':
                        case 'included_in_block':
                        case 'replaced':
                            return submittedCardTopUp
                        case 'failed':
                            return {
                                ...submittedCardTopUp,
                                state: {
                                    type: 'failed_tx',
                                    transaction: submittedTx,
                                },
                            }
                        case 'completed':
                            return {
                                ...submittedCardTopUp,
                                state: { type: 'completed' },
                            }
                        default:
                            return notReachable(submittedTx)
                    }
                }
                case 'failed_tx':
                case 'completed':
                    return submittedCardTopUp
                default:
                    return notReachable(submittedCardTopUp.state)
            }
        case 'send_safe':
            switch (submittedCardTopUp.state.type) {
                case 'waiting_for_user_operation': {
                    const submittedUserOp = await fetchSubmittedUserOperation({
                        submittedUserOperation:
                            submittedCardTopUp.state.submittedUserOperation,
                        networkRPCMap,
                        network: CARD_NETWORK,
                    })

                    switch (submittedUserOp.state) {
                        case 'bundled':
                        case 'pending':
                            return submittedCardTopUp
                        case 'failed':
                        case 'rejected':
                            return {
                                ...submittedCardTopUp,
                                state: {
                                    type: 'failed_user_operation',
                                    userOperation: submittedUserOp,
                                },
                            }
                        case 'completed':
                            return {
                                ...submittedCardTopUp,
                                state: { type: 'completed' },
                            }
                        default:
                            return notReachable(submittedUserOp)
                    }
                }
                case 'failed_user_operation':
                case 'completed':
                    return submittedCardTopUp
                default:
                    return notReachable(submittedCardTopUp.state)
            }
        case 'swap_eoa_native_send_transaction': {
            const network = findNetworkByHexChainId(
                submittedCardTopUp.fromAmount.currency.networkHexChainId,
                networkMap
            )

            switch (submittedCardTopUp.state.type) {
                case 'waiting_for_transaction': {
                    const submittedTx = await fetchTransaction({
                        transaction:
                            submittedCardTopUp.state.submittedTransaction,
                        networkRPCMap,
                        network,
                    })

                    switch (submittedTx.state) {
                        case 'queued':
                        case 'included_in_block':
                        case 'replaced':
                            return submittedCardTopUp
                        case 'failed':
                            return {
                                ...submittedCardTopUp,
                                state: {
                                    type: 'failed_tx',
                                    transaction: submittedTx,
                                },
                            }
                        case 'completed':
                            return await fetchSubmittedCardTopUp({
                                submittedCardTopUp: {
                                    ...submittedCardTopUp,
                                    state: { type: 'waiting_for_swap' },
                                },
                                networkMap,
                                networkRPCMap,
                                signal,
                            })
                        default:
                            return notReachable(submittedTx)
                    }
                }
                case 'waiting_for_swap':
                    const swapState = await fetchBungeeIntentQuoteStatus({
                        requestHash: submittedCardTopUp.quoteRequestHash,
                        signal,
                    })

                    switch (swapState.type) {
                        case 'in_progress':
                            return submittedCardTopUp
                        case 'cancelled':
                        case 'expired':
                        case 'refunded':
                            return {
                                ...submittedCardTopUp,
                                state: {
                                    type: 'failed_bungee',
                                    quoteRequestHash:
                                        submittedCardTopUp.quoteRequestHash,
                                },
                            }
                        case 'completed':
                            return {
                                ...submittedCardTopUp,
                                state: { type: 'completed' },
                            }
                        default:
                            return notReachable(swapState)
                    }
                case 'completed':
                case 'failed_tx':
                case 'failed_bungee':
                    return submittedCardTopUp
                default:
                    return notReachable(submittedCardTopUp.state)
            }
        }
        case 'swap_safe_native_send_transaction': {
            const network = findNetworkByHexChainId(
                submittedCardTopUp.fromAmount.currency.networkHexChainId,
                networkMap
            )

            switch (submittedCardTopUp.state.type) {
                case 'waiting_for_user_operation': {
                    const submittedUserOp = await fetchSubmittedUserOperation({
                        submittedUserOperation:
                            submittedCardTopUp.state.submittedUserOperation,
                        networkRPCMap,
                        network,
                    })

                    switch (submittedUserOp.state) {
                        case 'bundled':
                        case 'pending':
                            return submittedCardTopUp
                        case 'failed':
                        case 'rejected':
                            return {
                                ...submittedCardTopUp,
                                state: {
                                    type: 'failed_user_operation',
                                    userOperation: submittedUserOp,
                                },
                            }
                        case 'completed':
                            return await fetchSubmittedCardTopUp({
                                submittedCardTopUp: {
                                    ...submittedCardTopUp,
                                    state: { type: 'waiting_for_swap' },
                                },
                                networkMap,
                                networkRPCMap,
                                signal,
                            })
                        default:
                            return notReachable(submittedUserOp)
                    }
                }
                case 'waiting_for_swap':
                    const swapState = await fetchBungeeIntentQuoteStatus({
                        requestHash: submittedCardTopUp.quoteRequestHash,
                        signal,
                    })

                    switch (swapState.type) {
                        case 'in_progress':
                            return submittedCardTopUp
                        case 'cancelled':
                        case 'expired':
                        case 'refunded':
                            return {
                                ...submittedCardTopUp,
                                state: {
                                    type: 'failed_bungee',
                                    quoteRequestHash:
                                        submittedCardTopUp.quoteRequestHash,
                                },
                            }
                        case 'completed':
                            return {
                                ...submittedCardTopUp,
                                state: { type: 'completed' },
                            }
                        default:
                            return notReachable(swapState)
                    }
                case 'completed':
                case 'failed_bungee':
                case 'failed_user_operation':
                    return submittedCardTopUp
                default:
                    return notReachable(submittedCardTopUp.state)
            }
        }
        case 'earn_sign_typed_data':
        case 'swap_sign_typed_data': {
            switch (submittedCardTopUp.state.type) {
                case 'waiting_for_swap':
                    const swapState = await fetchBungeeIntentQuoteStatus({
                        requestHash: submittedCardTopUp.quoteRequestHash,
                        signal,
                    })

                    switch (swapState.type) {
                        case 'in_progress':
                            return submittedCardTopUp
                        case 'cancelled':
                        case 'expired':
                        case 'refunded':
                            return {
                                ...submittedCardTopUp,
                                state: {
                                    type: 'failed_bungee',
                                    quoteRequestHash:
                                        submittedCardTopUp.quoteRequestHash,
                                },
                            }
                        case 'completed':
                            return {
                                ...submittedCardTopUp,
                                state: { type: 'completed' },
                            }
                        default:
                            return notReachable(swapState)
                    }

                case 'completed':
                case 'failed_bungee':
                    return submittedCardTopUp
                default:
                    return notReachable(submittedCardTopUp.state)
            }
        }
        default:
            return notReachable(submittedCardTopUp)
    }
}
