import { post } from '@zeal/api/gnosisApi'

import { notReachable } from '@zeal/toolkit'
import { withRetries } from '@zeal/toolkit/Function'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

import { loginWithCache } from '@zeal/domains/Card/api/login'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { OperationType } from '@zeal/domains/UserOperation'

import { CardDelayRelayTransactionRequest, CardSlientSignKeyStore } from '..'

type Params = {
    request: CardDelayRelayTransactionRequest
    readonlySignerAddress: Web3.address.Address
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    signal?: AbortSignal
}

const NUM_RETRIES = 2
const RETRY_DELAY_MS = 1000

export const submitDelayRelayTransaction = async ({
    sessionPassword,
    keyStore,
    readonlySignerAddress,
    request,
    signal,
}: Params): Promise<void> => {
    const gnosisPayLoginInfo = await loginWithCache({
        signal,
        keyStore,
        readonlySignerAddress,
        sessionPassword,
    })

    const signedData = Hexadecimal.concat(
        request.relayTransaction.data,
        request.relaySalt,
        request.signature
    )

    return post(
        '/delay-relay',
        {
            auth: { type: 'bearer_token', token: gnosisPayLoginInfo.token },
            body: {
                chainId: Number(request.network.hexChainId),
                target: request.relayTransaction.to,
                signedData,
                safeAddress: request.cardSafe,
                operationType: (() => {
                    switch (request.transaction.operation) {
                        case OperationType.Call:
                            return 'CALL'
                        case OperationType.DelegateCall:
                            return 'DELEGATECALL'
                        /* istanbul ignore next */
                        default:
                            return notReachable(request.transaction.operation)
                    }
                })(),
                transactionData: request.transaction,
            },
        },
        signal
    ).then(() => undefined)
}

export const submitDelayRelayTransactionWithRetriesForBusyDelayRelay = (
    params: Params
): Promise<void> => {
    try {
        return submitDelayRelayTransaction(params)
    } catch (error) {
        const parsed = parseAppError(error)

        switch (parsed.type) {
            case 'gnosis_pay_card_delay_relay_not_empty':
                return withRetries({
                    retries: NUM_RETRIES,
                    delayMs: RETRY_DELAY_MS,
                    fn: submitDelayRelayTransaction,
                })(params)

            default:
                throw error
        }
    }
}
