import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import {
    match,
    number,
    object,
    oneOf,
    Result,
    shape,
} from '@zeal/toolkit/Result'

// eslint-disable-next-line zeal-domains/no-feature-deep-import
import {
    CardTopUpCompleted,
    CardTopUpFailedBungee,
    CardTopUpFailedTransaction,
    CardTopUpFailedUserOperation,
    CardTopUpWaitingForSwap,
    CardTopUpWaitingForTransaction,
    CardTopUpWaitingForUserOperation,
    SubmittedEarnSignTypedDataCardTopUp,
    SubmittedEOASendCardTopUp,
    SubmittedEOASendNativeCardTopUp,
    SubmittedSafeSendCardTopUp,
    SubmittedSafeSendNativeCardTopUp,
    SubmittedSwapSignTypedDataCardTopUp,
} from '@zeal/domains/Card/features/CardAddCash/CardAddCashV2/types' // FIXME :: @Nicvaniek
import { parseTakerType } from '@zeal/domains/Earn/parsers/parseEarn'
import { parseEOA } from '@zeal/domains/KeyStore/parsers/parse'
import { parseInternalTransactionActionSource } from '@zeal/domains/Main/parsers/parseInternalTransactionActionSource'
import { parseCryptoMoneyFromStorage } from '@zeal/domains/Money/helpers/parse'
import {
    parseSubmitedTransaction,
    parseSubmittedTransactionFailed,
} from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/parsers/parseSubmitedTransaction'
import {
    parseSubmittedUserOperation,
    parseSubmittedUserOperationFailed,
} from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation/parsers/parseSubmittedUserOperationType'

export const parseSubmittedCardTopUp = (input: unknown) =>
    oneOf(input, [
        parseSubmittedEOASendCardTopUp(input),
        parseSubmittedSafeSendCardTopUp(input),
        parseSubmittedEOASendNativeCardTopUp(input),
        parseSubmittedSafeSendNativeCardTopUp(input),
        parseSubmittedSwapSignTypedDataCardTopUp(input),
        parseSubmittedEarnSignTypedDataCardTopUp(input),
    ])

const parseSubmittedEOASendCardTopUp = (
    input: unknown
): Result<unknown, SubmittedEOASendCardTopUp> =>
    object(input).andThen((obj) =>
        shape({
            type: match(obj.type, 'send_eoa' as const),
            amount: parseCryptoMoneyFromStorage(obj.amount),
            actionSource: parseInternalTransactionActionSource(
                obj.actionSource
            ),
            keyStore: parseEOA(obj.keyStore),
            startedAtMs: number(obj.startedAtMs),
            state: oneOf(obj.state, [
                parseCardTopUpWaitingForTransaction(obj.state),
                parseCardTopUpCompleted(obj.state),
                parseCardTopUpFailedTransaction(obj.state),
            ]),
        })
    )

const parseSubmittedSafeSendCardTopUp = (
    input: unknown
): Result<unknown, SubmittedSafeSendCardTopUp> =>
    object(input).andThen((obj) =>
        shape({
            type: match(obj.type, 'send_safe' as const),
            amount: parseCryptoMoneyFromStorage(obj.amount),
            actionSource: parseInternalTransactionActionSource(
                obj.actionSource
            ),
            startedAtMs: number(obj.startedAtMs),
            state: oneOf(obj.state, [
                parseCardTopUpWaitingForUserOperation(obj.state),
                parseCardTopUpCompleted(obj.state),
                parseCardTopUpFailedUserOperation(obj.state),
            ]),
        })
    )

const parseSubmittedEOASendNativeCardTopUp = (
    input: unknown
): Result<unknown, SubmittedEOASendNativeCardTopUp> =>
    object(input).andThen((obj) =>
        shape({
            type: match(obj.type, 'swap_eoa_native_send_transaction' as const),
            fromAmount: parseCryptoMoneyFromStorage(obj.fromAmount),
            toAmount: parseCryptoMoneyFromStorage(obj.toAmount),
            quoteRequestHash: Hexadecimal.parse(obj.quoteRequestHash),
            actionSource: parseInternalTransactionActionSource(
                obj.actionSource
            ),
            keyStore: parseEOA(obj.keyStore),
            startedAtMs: number(obj.startedAtMs),
            state: oneOf(obj.state, [
                parseCardTopUpWaitingForTransaction(obj.state),
                parseCardTopUpWaitingForSwap(obj.state),
                parseCardTopUpCompleted(obj.state),
                parseCardTopUpFailedTransaction(obj.state),
                parseCardTopUpFailedBungee(obj.state),
            ]),
        })
    )

const parseSubmittedSafeSendNativeCardTopUp = (
    input: unknown
): Result<unknown, SubmittedSafeSendNativeCardTopUp> =>
    object(input).andThen((obj) =>
        shape({
            type: match(obj.type, 'swap_safe_native_send_transaction' as const),
            fromAmount: parseCryptoMoneyFromStorage(obj.fromAmount),
            toAmount: parseCryptoMoneyFromStorage(obj.toAmount),
            quoteRequestHash: Hexadecimal.parse(obj.quoteRequestHash),
            actionSource: parseInternalTransactionActionSource(
                obj.actionSource
            ),
            startedAtMs: number(obj.startedAtMs),
            state: oneOf(obj.state, [
                parseCardTopUpWaitingForUserOperation(obj.state),
                parseCardTopUpWaitingForSwap(obj.state),
                parseCardTopUpCompleted(obj.state),
                parseCardTopUpFailedUserOperation(obj.state),
                parseCardTopUpFailedBungee(obj.state),
            ]),
        })
    )

const parseSubmittedSwapSignTypedDataCardTopUp = (
    input: unknown
): Result<unknown, SubmittedSwapSignTypedDataCardTopUp> =>
    object(input).andThen((obj) =>
        shape({
            type: match(obj.type, 'swap_sign_typed_data' as const),
            fromAmount: parseCryptoMoneyFromStorage(obj.fromAmount),
            toAmount: parseCryptoMoneyFromStorage(obj.toAmount),
            quoteRequestHash: Hexadecimal.parse(obj.quoteRequestHash),
            actionSource: parseInternalTransactionActionSource(
                obj.actionSource
            ),
            startedAtMs: number(obj.startedAtMs),
            state: oneOf(obj.state, [
                parseCardTopUpWaitingForSwap(obj.state),
                parseCardTopUpCompleted(obj.state),
                parseCardTopUpFailedBungee(obj.state),
            ]),
        })
    )

const parseSubmittedEarnSignTypedDataCardTopUp = (
    input: unknown
): Result<unknown, SubmittedEarnSignTypedDataCardTopUp> =>
    object(input).andThen((obj) =>
        shape({
            type: match(obj.type, 'earn_sign_typed_data' as const),
            takerType: parseTakerType(obj.takerType),
            fromAmount: parseCryptoMoneyFromStorage(obj.fromAmount),
            toAmount: parseCryptoMoneyFromStorage(obj.toAmount),
            quoteRequestHash: Hexadecimal.parse(obj.quoteRequestHash),
            actionSource: parseInternalTransactionActionSource(
                obj.actionSource
            ),
            startedAtMs: number(obj.startedAtMs),
            state: oneOf(obj.state, [
                parseCardTopUpWaitingForSwap(obj.state),
                parseCardTopUpCompleted(obj.state),
                parseCardTopUpFailedBungee(obj.state),
            ]),
        })
    )

// Progress states
const parseCardTopUpCompleted = (
    input: unknown
): Result<unknown, CardTopUpCompleted> =>
    object(input).andThen((obj) =>
        shape({
            type: match(obj.type, 'completed' as const),
        })
    )

const parseCardTopUpFailedTransaction = (
    input: unknown
): Result<unknown, CardTopUpFailedTransaction> =>
    object(input).andThen((obj) =>
        shape({
            type: match(obj.type, 'failed_tx' as const),
            transaction: parseSubmittedTransactionFailed(obj.transaction),
        })
    )

const parseCardTopUpFailedUserOperation = (
    input: unknown
): Result<unknown, CardTopUpFailedUserOperation> =>
    object(input).andThen((obj) =>
        shape({
            type: match(obj.type, 'failed_user_operation' as const),
            userOperation: parseSubmittedUserOperationFailed(obj.userOperation),
        })
    )

const parseCardTopUpFailedBungee = (
    input: unknown
): Result<unknown, CardTopUpFailedBungee> =>
    object(input).andThen((obj) =>
        shape({
            type: match(obj.type, 'failed_bungee' as const),
            quoteRequestHash: Hexadecimal.parse(obj.quoteRequestHash),
        })
    )

const parseCardTopUpWaitingForTransaction = (
    input: unknown
): Result<unknown, CardTopUpWaitingForTransaction> =>
    object(input).andThen((obj) =>
        shape({
            type: match(obj.type, 'waiting_for_transaction' as const),
            submittedTransaction: parseSubmitedTransaction(
                obj.submittedTransaction
            ),
        })
    )

const parseCardTopUpWaitingForUserOperation = (
    input: unknown
): Result<unknown, CardTopUpWaitingForUserOperation> =>
    object(input).andThen((obj) =>
        shape({
            type: match(obj.type, 'waiting_for_user_operation' as const),
            submittedUserOperation: parseSubmittedUserOperation(
                obj.submittedUserOperation
            ),
        })
    )

const parseCardTopUpWaitingForSwap = (
    input: unknown
): Result<unknown, CardTopUpWaitingForSwap> =>
    object(input).andThen((obj) =>
        shape({
            type: match(obj.type, 'waiting_for_swap' as const),
        })
    )
