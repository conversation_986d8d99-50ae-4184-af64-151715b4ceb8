import * as Crypto from '@zeal/toolkit/Crypto'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

import { ZODIAC_MODULE_PROXY_FACTORY } from '@zeal/domains/Address/constants'
import { DELAY_MODULE_ABI } from '@zeal/domains/Card/constants'

const DELAY_MODULE_MASTER_COPY = Web3.address.staticFromString(
    '******************************************'
)

export const predictDelayModAddress = (
    safe: Web3.address.Address
): Web3.address.Address => {
    const bytecode = Hexadecimal.concat(
        '0x602d8060093d393df3363d3d373d3d3d363d73' as const,
        DELAY_MODULE_MASTER_COPY,
        '0x5af43d82803e903d91602b57fd5bf3' as const
    )

    const initializer = Web3.abi.encodeAbiParameters(
        [
            { type: 'address' },
            { type: 'address' },
            { type: 'address' },
            { type: 'uint256' },
            { type: 'uint256' },
        ] as const,
        [
            safe as `0x${string}`,
            safe as `0x${string}`,
            safe as `0x${string}`,
            0n,
            0n,
        ]
    )

    const delayModuleSetupData = Web3.abi.encodeFunctionData({
        abi: DELAY_MODULE_ABI,
        functionName: 'setUp',
        args: [initializer],
    })

    const setupPacked = Web3.abi.encodePacked(['bytes'], [delayModuleSetupData])

    const setupKeccak = Crypto.keccak256(setupPacked)

    const saltPacked = Web3.abi.encodePacked(
        ['bytes32', 'uint256'],
        [setupKeccak, 0n]
    )

    const saltKeccak = Crypto.keccak256(saltPacked)

    return Web3.contract.getCreate2Address({
        bytecode,
        from: ZODIAC_MODULE_PROXY_FACTORY,
        salt: saltKeccak,
    })
}
