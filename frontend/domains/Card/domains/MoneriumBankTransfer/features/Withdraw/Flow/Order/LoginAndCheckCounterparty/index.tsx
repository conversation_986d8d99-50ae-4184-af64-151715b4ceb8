import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import {
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import {
    MoneriumAuthInfo,
    MoneriumOfframpOrder,
} from '@zeal/domains/Card/domains/MoneriumBankTransfer'
import { getLoginMsgToSign } from '@zeal/domains/Card/domains/MoneriumBankTransfer/helpers/getLoginMsgToSign'
import { SignMessage } from '@zeal/domains/Card/features/SignMessage'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { PersonalSign } from '@zeal/domains/RPCRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { CheckAddress } from './CheckAddress'
import { Login } from './Login'

import { LinkAddress } from '../LinkAddress'

type Props = {
    order: MoneriumOfframpOrder

    cardConfig:
        | ReadonlySignerSelectedOnboardedCardConfig
        | ReadonlySignerSelectedCardConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    keyStore: SigningKeyStore
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof SignMessage>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >
    | Extract<MsgOf<typeof Login>, { type: 'close' }>
    | Extract<
          MsgOf<typeof CheckAddress>,
          { type: 'close' | 'monerium_adress_checked' }
      >
    | MsgOf<typeof LinkAddress>

type State =
    | {
          type: 'sign'
          messageToSign: { message: string; personalSign: PersonalSign }
      }
    | {
          type: 'login'
          signature: Hexadecimal
          message: string
      }
    | {
          type: 'check_addresses'
          authInfo: MoneriumAuthInfo
      }
    | {
          type: 'link_address'
          authInfo: MoneriumAuthInfo
      }

export const LoginAndCheckCounterparty = ({
    defaultCurrencyConfig,
    networkMap,
    installationId,
    networkRPCMap,
    keyStoreMap,
    keyStore,
    accountsMap,
    cardConfig,
    order,
    feePresetMap,
    sessionPassword,
    gasCurrencyPresetMap,
    portfolioMap,
    onMsg,
}: Props) => {
    const cardOwner = accountsMap[cardConfig.readonlySignerAddress]

    const [state, setState] = useState<State>({
        type: 'sign',
        messageToSign: getLoginMsgToSign({
            network: CARD_NETWORK,
            address: cardOwner.address,
        }),
    })

    switch (state.type) {
        case 'sign':
            return (
                <SignMessage
                    cardOwner={cardOwner}
                    messageToSign={state.messageToSign.personalSign}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    installationId={installationId}
                    keyStore={keyStore}
                    network={CARD_NETWORK}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    keyStoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                                onMsg(msg)
                                break
                            case 'on_message_signed':
                                setState({
                                    type: 'login',
                                    signature: msg.signature as Hexadecimal, // FIXME :: @mike can we change signMessage to return Hexadecimal?
                                    message: state.messageToSign.message,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'login':
            return (
                <Login
                    message={state.message}
                    signature={state.signature}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'monerium_auth_info_loaded':
                                setState({
                                    type: 'check_addresses',
                                    authInfo: msg.authInfo,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'check_addresses':
            return (
                <CheckAddress
                    authInfo={state.authInfo}
                    network={CARD_NETWORK}
                    order={order}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'monerium_adress_checked':
                                onMsg(msg)
                                break
                            case 'on_monerium_adress_link_request':
                                setState({
                                    type: 'link_address',
                                    authInfo: state.authInfo,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'link_address':
            return (
                <LinkAddress
                    accountsMap={accountsMap}
                    authInfo={state.authInfo}
                    network={CARD_NETWORK}
                    order={order}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    installationId={installationId}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    keyStoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={onMsg}
                />
            )

        /* istanbul ignore next */
        default:
            notReachable(state)
    }
}
