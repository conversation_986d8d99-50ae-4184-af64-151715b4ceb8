import { useEffect } from 'react'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { GnosisPayAccountOnboardedState } from '@zeal/domains/Card'
import { ROLES_ABI } from '@zeal/domains/Card/constants'
import { fetchBouncerAddress } from '@zeal/domains/Card/domains/SpendLimit/api/fetchBouncerAddress'
import { CARD_SPEND_LIMIT_ALLOWANCE_KEY } from '@zeal/domains/Card/domains/SpendLimit/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { NetworkRPCMap } from '@zeal/domains/Network'
import { MetaTransactionData, OperationType } from '@zeal/domains/UserOperation'

import { SubmittedForm } from './Form'

type Props = {
    form: SubmittedForm
    networkRPCMap: NetworkRPCMap
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'close'
      }
    | {
          type: 'transaction_created'
          setAllowanceMetaTransaction: MetaTransactionData
      }

const ONE_DAY_IN_SECONDS = 86400n

const fetch = async ({
    gnosisPayAccountOnboardedState,
    networkRPCMap,
    form,
    signal,
}: {
    networkRPCMap: NetworkRPCMap
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    form: SubmittedForm
    signal?: AbortSignal
}): Promise<MetaTransactionData> => {
    const bouncerAddress = await fetchBouncerAddress({
        cardSafeAddress: gnosisPayAccountOnboardedState.cardSafe.address,
        networkRPCMap,
        signal,
    })

    return {
        to: bouncerAddress,
        operation: OperationType.Call,
        value: '0x0',
        data: Web3.abi.encodeFunctionData({
            abi: ROLES_ABI,
            functionName: 'setAllowance',
            args: [
                CARD_SPEND_LIMIT_ALLOWANCE_KEY,
                form.spendLimit.amount,
                form.spendLimit.amount,
                form.spendLimit.amount,
                ONE_DAY_IN_SECONDS,
                0n,
            ],
        }),
    }
}
export const DataLoader = ({
    gnosisPayAccountOnboardedState,
    networkRPCMap,
    installationId,
    onMsg,
    form,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            gnosisPayAccountOnboardedState,
            networkRPCMap,
            form,
        },
    })

    const liveMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
            case 'error':
                break
            case 'loaded':
                liveMsg.current({
                    type: 'transaction_created',
                    setAllowanceMetaTransaction: loadable.data,
                })
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [liveMsg, loadable])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return (
                <LoadingLayout
                    title={null}
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={
                            <ActionBar
                                left={
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => onMsg({ type: 'close' })}
                                    >
                                        {({ color }) => (
                                            <BackIcon size={24} color={color} />
                                        )}
                                    </IconButton>
                                }
                            />
                        }
                        onClose={() => onMsg({ type: 'close' })}
                    />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        default:
            notReachable(loadable)
    }
}
