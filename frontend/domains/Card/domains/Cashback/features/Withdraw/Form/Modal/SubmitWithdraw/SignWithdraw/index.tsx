import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { NULL_ADDRESS } from '@zeal/domains/Address/constants'
import { CashbackWithdrawalRequest } from '@zeal/domains/Card/domains/Cashback'
import { SignMessage } from '@zeal/domains/Card/features/SignMessage'
import { createCardSafeDelayModuleTransaction } from '@zeal/domains/Card/helpers/createCardSafeDelayModuleTransaction'
import { createCardRelayTransactionMessage } from '@zeal/domains/Card/helpers/createCardSafeDelayModuleTransactionMessage'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { FAKE_GNOSIS_PAY_DAPP } from '@zeal/domains/DApp/constants'
import { KeyStoreMap, Safe4337, SigningKeyStore } from '@zeal/domains/KeyStore'
import { ConstructSafeTransactionHash } from '@zeal/domains/KeyStore/domains/Safe/features/ConstructSafeTransactionHash'
import { SignSafeTransactionHash } from '@zeal/domains/KeyStore/domains/Safe/features/SignSafeTransactionHash'
import {
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
} from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { EthSignTypedDataV4 } from '@zeal/domains/RPCRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { MetaTransactionData, OperationType } from '@zeal/domains/UserOperation'
import { SAFE_ABI } from '@zeal/domains/UserOperation/constants'

type Props = {
    cardOwner: Account
    keyStore: SigningKeyStore

    cashbackWithdrawalRequest: CashbackWithdrawalRequest

    network: PredefinedNetwork
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof SignMessage>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >
    | {
          type: 'on_withdraw_signed'
          relayTransaction: MetaTransactionData
          relayMessage: EthSignTypedDataV4
          relaySalt: Hexadecimal.Hexadecimal
          signature: Hexadecimal.Hexadecimal
      }

type State =
    | {
          type: 'sign_relay_transaction'
          relayTransaction: MetaTransactionData
          relayMessage: EthSignTypedDataV4
          relaySalt: Hexadecimal.Hexadecimal
      }
    | {
          type: 'construct_safe_transaction_hash'
          keyStore: Safe4337
          delayModuleTransaction: MetaTransactionData
      }
    | {
          type: 'sign_safe_transaction_hash'
          keyStore: Safe4337
          delayModuleTransaction: MetaTransactionData
          transactionHash: Hexadecimal.Hexadecimal
      }

export const SignWithdraw = ({
    cardOwner,
    keyStore,
    onMsg,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keyStoreMap,
    networkMap,
    network,
    networkRPCMap,
    portfolioMap,
    sessionPassword,
    cashbackWithdrawalRequest,
    defaultCurrencyConfig,
}: Props) => {
    const [state, setState] = useState<State>(() => {
        const delayModuleTransaction = createCardSafeDelayModuleTransaction({
            transaction: cashbackWithdrawalRequest.transaction,
            cardSafeAddress: cashbackWithdrawalRequest.cardSafe,
        })

        switch (keyStore.type) {
            case 'private_key_store':
            case 'ledger':
            case 'secret_phrase_key':
            case 'trezor':
                const { message, salt } = createCardRelayTransactionMessage({
                    network: cashbackWithdrawalRequest.network,
                    transaction: delayModuleTransaction,
                })

                return {
                    type: 'sign_relay_transaction',
                    relayTransaction: delayModuleTransaction,
                    relayMessage: message,
                    relaySalt: salt,
                }

            case 'safe_4337':
                return {
                    type: 'construct_safe_transaction_hash',
                    delayModuleTransaction,
                    keyStore,
                }
            default:
                return notReachable(keyStore)
        }
    })

    switch (state.type) {
        case 'sign_relay_transaction':
            return (
                <SignMessage
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    key={state.type}
                    messageToSign={state.relayMessage}
                    network={network}
                    cardOwner={cardOwner}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStore={keyStore}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'close':
                                onMsg(msg)
                                break

                            case 'on_message_signed':
                                onMsg({
                                    type: 'on_withdraw_signed',
                                    relayMessage: state.relayMessage,
                                    relaySalt: state.relaySalt,
                                    relayTransaction: state.relayTransaction,
                                    signature:
                                        msg.signature as Hexadecimal.Hexadecimal, // FIXME :: @mike can we change signMessage to return Hexadecimal?
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'construct_safe_transaction_hash':
            return (
                <ConstructSafeTransactionHash
                    installationId={installationId}
                    account={cardOwner}
                    transaction={state.delayModuleTransaction}
                    network={network}
                    networkRPCMap={networkRPCMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break

                            case 'on_safe_transaction_hash_constructed':
                                setState({
                                    type: 'sign_safe_transaction_hash',
                                    keyStore: state.keyStore,
                                    transactionHash: msg.transactionHash,
                                    delayModuleTransaction:
                                        state.delayModuleTransaction,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'sign_safe_transaction_hash':
            return (
                <SignSafeTransactionHash
                    actionSource={{
                        type: 'internal_sign',
                        dAppSiteInfo: FAKE_GNOSIS_PAY_DAPP,
                        transactionEventSource:
                            'gnosisPaySignSafeRelayTransaction',
                    }}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    key={state.type}
                    portfolio={null}
                    transactionHash={state.transactionHash}
                    network={network}
                    account={cardOwner}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStore={state.keyStore}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'drag':
                            case 'on_minimize_click':
                            case 'on_expand_request':
                            case 'on_wrong_network_accepted':
                                throw new ImperativeError(
                                    `Message cannot be fired in SignWithdraw flow`,
                                    { msg }
                                )

                            case 'close':
                            case 'on_safe_deployemnt_cancelled':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_safe_deployment_error_popup_cancel_clicked':
                                onMsg({ type: 'close' })
                                break

                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                                onMsg(msg)
                                break

                            case 'safe_transaction_hash_signed': {
                                const delayModuleTransaction =
                                    state.delayModuleTransaction

                                const safeTransaction: MetaTransactionData = {
                                    data: Web3.abi.encodeFunctionData({
                                        abi: SAFE_ABI,
                                        functionName: 'execTransaction',
                                        args: [
                                            delayModuleTransaction.to,
                                            BigInt(
                                                delayModuleTransaction.value
                                            ),
                                            delayModuleTransaction.data,
                                            OperationType.Call,
                                            0n,
                                            0n,
                                            0n,
                                            NULL_ADDRESS,
                                            NULL_ADDRESS,
                                            msg.signature,
                                        ],
                                    }),
                                    operation: OperationType.Call,
                                    to: cardOwner.address as `0x${string}`,
                                    value: '0x0',
                                }

                                const { message, salt } =
                                    createCardRelayTransactionMessage({
                                        network:
                                            cashbackWithdrawalRequest.network,
                                        transaction: safeTransaction,
                                    })

                                setState({
                                    type: 'sign_relay_transaction',
                                    relayMessage: message,
                                    relaySalt: salt,
                                    relayTransaction: safeTransaction,
                                })
                                break
                            }

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        default:
            return notReachable(state)
    }
}
