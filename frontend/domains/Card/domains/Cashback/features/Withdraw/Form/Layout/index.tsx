import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { ListItem } from '@zeal/uikit/ListItem'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Skeleton } from '@zeal/uikit/Skeleton'
import { Spacer } from '@zeal/uikit/Spacer'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'

import { Account } from '@zeal/domains/Account'
import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import { AvatarWithoutBadge } from '@zeal/domains/Account/components/Avatar'
import { CopyAddress } from '@zeal/domains/Address/components/CopyAddress'
import { GnosisPayAccountOnboardedState } from '@zeal/domains/Card'
import { DelayQueueState } from '@zeal/domains/Card/api/fetchDelayQueueState'
import { DelayQueueBusyBanner } from '@zeal/domains/Card/components/DelayQueueBusyBanner'
import { DelayQueueFailedBanner } from '@zeal/domains/Card/components/DelayQueueFailedBanner'
import {
    CashbackWithdrawalRequest,
    EligibleForCashback,
} from '@zeal/domains/Card/domains/Cashback'
import { FormattedCashbackPercentage } from '@zeal/domains/Card/domains/Cashback/components/FormattedCashbackPercentage'
import { getCashbackPercentage } from '@zeal/domains/Card/domains/Cashback/helpers/getCashbackPercentage'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { MaxButton } from '@zeal/domains/Currency/components/MaxButton'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { useMoneyFormat } from '@zeal/domains/Money/hooks/useMoneyFormat'
import { NetworkMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'

import {
    getAmountAfterWithdrawal,
    getBalance,
    getWithdrawAmount,
    Pollable,
    validate,
} from './validation'

type Props = {
    cardOwner: Account
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    cardCashback: EligibleForCashback
    pollable: Pollable
    delayQueueStatePollable: PollableData<DelayQueueState, unknown>
    networkMap: NetworkMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_amount_change'; amount: string | null }
    | { type: 'on_to_account_clicked' }
    | {
          type: 'on_form_submitted'
          cashbackWithdrawRequest: CashbackWithdrawalRequest
      }

export const Layout = ({
    gnosisPayAccountOnboardedState,
    cardCashback,
    cardOwner,
    pollable,
    delayQueueStatePollable,
    networkMap,
    installationId,
    onMsg,
}: Props) => {
    const { formatMessage } = useIntl()
    const { formatForInputs } = useMoneyFormat()
    const validationResult = validate({
        cardCashback,
        cardSafe: gnosisPayAccountOnboardedState.cardSafe.address,
        pollable,
        delayQueueStatePollable,
        networkMap,
    })

    const errors = validationResult.getFailureReason() || {}

    const { currency, to } = pollable.params

    const balance = getBalance({ cardCashback, pollable })

    const from = getWithdrawAmount({ pollable })
    const isMaxAmount = from.amount === balance.amount

    const formattedAmountForInput = formatForInputs({
        money: from,
    })

    const amountAfterWithdrawal = getAmountAfterWithdrawal({
        cardCashback,
        pollable,
    })

    const cashbackBefore = getCashbackPercentage({
        amount: balance,
        earlyAdopter: cardCashback.earlyAdopter,
    })

    const cashbackAfter = getCashbackPercentage({
        amount: amountAfterWithdrawal,
        earlyAdopter: cardCashback.earlyAdopter,
    })

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                top={<ActionBarAccountIndicator account={cardOwner} />}
                left={
                    <Clickable onClick={() => onMsg({ type: 'close' })}>
                        <Row spacing={4}>
                            <BackIcon size={24} color="iconDefault" />
                            <ActionBar.Header>
                                <FormattedMessage
                                    id="cardCashbackWithdraw.header"
                                    defaultMessage="Withdraw {currency}"
                                    values={{ currency: currency.code }}
                                />
                            </ActionBar.Header>
                        </Row>
                    </Clickable>
                }
            />

            <Column spacing={12} fill alignY="stretch">
                <Column spacing={4}>
                    <AmountInput
                        content={{
                            topLeft: (
                                <Row spacing={4}>
                                    <CurrencyAvatar
                                        currency={currency}
                                        size={24}
                                        rightBadge={({ size }) => (
                                            <Badge
                                                size={size}
                                                network={findNetworkByHexChainId(
                                                    currency.networkHexChainId,
                                                    networkMap
                                                )}
                                            />
                                        )}
                                    />
                                    <Text
                                        variant="title3"
                                        color="textPrimary"
                                        weight="medium"
                                    >
                                        {currency.code}
                                    </Text>
                                </Row>
                            ),
                            topRight: ({ onBlur, onFocus, isFocused }) => (
                                <AmountInput.Input
                                    prefix=""
                                    onFocus={onFocus}
                                    onBlur={onBlur}
                                    label={formatMessage({
                                        id: 'cardCashbackWithdraw.amount',
                                        defaultMessage: 'Withdraw amount',
                                    })}
                                    fraction={currency.fraction}
                                    autoFocus
                                    readOnly={false}
                                    amount={
                                        !isFocused && isMaxAmount
                                            ? formattedAmountForInput
                                            : pollable.params.amount
                                    }
                                    onChange={(value) => {
                                        onMsg({
                                            type: 'on_amount_change',
                                            amount: value,
                                        })
                                    }}
                                    onSubmitEditing={noop}
                                />
                            ),

                            bottomLeft: (
                                <MaxButton
                                    installationId={installationId}
                                    location="cashback_withdrawal"
                                    balance={balance}
                                    onMsg={onMsg}
                                    state={errors.from ? 'error' : 'normal'}
                                />
                            ),
                            bottomRight: (() => {
                                switch (pollable.type) {
                                    case 'loaded':
                                    case 'reloading':
                                    case 'subsequent_failed':
                                        return pollable.data.rate ? (
                                            <Text
                                                variant="footnote"
                                                color="textSecondary"
                                                weight="regular"
                                            >
                                                <FormattedMoneyPrecise
                                                    withSymbol
                                                    sign={null}
                                                    money={applyRate2({
                                                        baseAmount: {
                                                            amount: fromFixedWithFraction(
                                                                pollable.params
                                                                    .amount,
                                                                currency.fraction
                                                            ),
                                                            currency,
                                                        },
                                                        rate: pollable.data
                                                            .rate,
                                                    })}
                                                />
                                            </Text>
                                        ) : null
                                    case 'loading':
                                        return (
                                            <Skeleton
                                                width={40}
                                                height={16}
                                                variant="default"
                                            />
                                        )
                                    case 'error':
                                        return null

                                    default:
                                        return notReachable(pollable)
                                }
                            })(),
                        }}
                        state={errors.from ? 'error' : 'normal'}
                    />

                    <NextStepSeparator />

                    <Group variant="default">
                        <ListItem
                            size="large"
                            onClick={() =>
                                onMsg({ type: 'on_to_account_clicked' })
                            }
                            avatar={({ size }) => (
                                <AvatarWithoutBadge account={to} size={size} />
                            )}
                            primaryText={to.label}
                            shortText={
                                <CopyAddress
                                    location="send"
                                    installationId={installationId}
                                    size="small"
                                    color="on_light"
                                    address={to.address}
                                />
                            }
                            aria-current={false}
                            side={{
                                rightIcon: ({ size }) => (
                                    <LightArrowDown2
                                        size={size}
                                        color="iconDefault"
                                    />
                                ),
                            }}
                        />
                    </Group>
                </Column>

                <Group variant="default">
                    <Row spacing={4}>
                        <Text
                            variant="callout"
                            weight="medium"
                            color="textPrimary"
                        >
                            <FormattedMessage
                                id="cashback.withdrawal.yourcashback"
                                defaultMessage="Your Cashback"
                            />
                        </Text>

                        <Spacer />

                        <Text
                            variant="callout"
                            weight="medium"
                            color="textPrimary"
                        >
                            {from.amount === 0n ||
                            cashbackAfter === cashbackBefore ? (
                                <FormattedCashbackPercentage
                                    cashbackAmount={cashbackBefore}
                                />
                            ) : (
                                <FormattedMessage
                                    id="cashback.withdrawal.change"
                                    defaultMessage="{from} to {to}"
                                    values={{
                                        from: (
                                            <FormattedCashbackPercentage
                                                cashbackAmount={cashbackBefore}
                                            />
                                        ),
                                        to: (
                                            <Text color="textStatusWarning">
                                                <FormattedCashbackPercentage
                                                    cashbackAmount={
                                                        cashbackAfter
                                                    }
                                                />
                                            </Text>
                                        ),
                                    }}
                                />
                            )}
                        </Text>
                    </Row>
                </Group>
                <Spacer />
            </Column>

            <Spacer />

            <Column spacing={12}>
                {(() => {
                    if (!errors.banner) {
                        return null
                    }

                    switch (errors.banner.type) {
                        case 'delay_queue_busy':
                            return <DelayQueueBusyBanner />
                        case 'delay_queue_failed':
                            return <DelayQueueFailedBanner />

                        case 'pollable_not_loaded':
                            return null

                        default:
                            return notReachable(errors.banner)
                    }
                })()}

                <Actions variant="default">
                    <Button
                        size="regular"
                        variant="secondary"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        <FormattedMessage
                            id="actions.cancel"
                            defaultMessage="Cancel"
                        />
                    </Button>

                    <Button
                        size="regular"
                        variant="primary"
                        disabled={!!errors.submit}
                        onClick={() =>
                            validationResult.tap((cashbackWithdrawRequest) =>
                                onMsg({
                                    type: 'on_form_submitted',
                                    cashbackWithdrawRequest,
                                })
                            )
                        }
                    >
                        <FormattedMessage
                            id="cashback.withdrawal.withdraw"
                            defaultMessage="Withdraw"
                        />
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}
