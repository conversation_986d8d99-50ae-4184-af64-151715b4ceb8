import { useEffect, useState } from 'react'

import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'

import { notReachable } from '@zeal/toolkit'
import { add } from '@zeal/toolkit/Date'
import { useLoadedReloadableData } from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { isNonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { SwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { Earn, HistoricalTakerUserCurrencyRateMap } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import {
    SwapsIOTransactionActivityPending,
    temp_mapBungeeCardTopUpToActivityTx,
    TransactionActivitiesCacheMap,
    TransactionActivityV2,
} from '@zeal/domains/Transactions'
import { fetchTransactionActivities } from '@zeal/domains/Transactions/api/fetchTransactionActivities'
import { fetchTransactionActivitiesWithMinimumTransactionsCount } from '@zeal/domains/Transactions/api/fetchTransactionActivitiesWithMinimumTransactionsCount'
import { getTransactionActivitiesCache } from '@zeal/domains/Transactions/helpers/getTransactionActivitiesCache'

import { Layout } from './Layout'
import { Modal, State } from './Modal'

const MINIMUM_TRANSACTIONS_ACTIVITES_COUNT = 3
const MAXIMUM_LOAD_ATTEMPTS = 5

const PAGINATION_TIME_WINDOW_IN_DAYS = 7

type Props = {
    address: Web3.address.Address
    cardConfig: CardConfig
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    portfolio: Portfolio2
    accountsMap: AccountsMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    refreshContainerState: RefreshContainerState
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    earn: Earn
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_swaps_io_transaction_activity_swap_started'
                  | 'on_swaps_io_transaction_activity_completed'
                  | 'on_swaps_io_transaction_activity_failed'
                  | 'on_see_all_transactions_clicked'
                  | 'on_pending_send_transaction_activity_completed'
                  | 'on_pending_send_transaction_activity_failed'
                  | 'on_pending_breward_claim_transaction_activity_completed'
                  | 'on_pending_breward_claim_transaction_activity_failed'
                  | 'on_pending_areward_claim_transaction_activity_completed'
                  | 'on_pending_areward_claim_transaction_activity_failed'
                  | 'on_pending_card_top_up_failed'
          }
      >
    | {
          type: 'on_transaction_activities_loaded'
          address: Web3.address.Address
          transactionActivities: TransactionActivityV2[]
          pendingSwapsIOTransactionActivities: SwapsIOTransactionActivityPending[]
      }

const fetch = async ({
    address,
    portfolio,
    cardConfig,
    keyStoreMap,
    sessionPassword,
    transactionActivitiesCacheMap,
    startTime,
    networkRPCMap,
    networkMap,
    earnHistoricalTakerUserCurrencyRateMap,
    installationId,
    swapsIOSwapRequestsMap,
}: {
    address: Web3.address.Address
    portfolio: Portfolio2
    cardConfig: CardConfig
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    startTime: Date
    installationId: string
}): Promise<{
    transactionActivities: TransactionActivityV2[]
    pendingSwapsIOTransactionActivities: SwapsIOTransactionActivityPending[]
}> => {
    const transactionActivitiesCache = getTransactionActivitiesCache({
        address,
        transactionActivitiesCacheMap,
    })

    if (
        transactionActivitiesCache &&
        isNonEmptyArray(transactionActivitiesCache.transactionActivities)
    ) {
        const { transactionActivities, swapsIOPendignTransactionActivities } =
            await fetchTransactionActivities({
                address,
                transactionActivityFilter: {
                    type: 'network_filter',
                    network: { type: 'all_networks' },
                },
                portfolio,
                cardConfig,
                keyStoreMap,
                sessionPassword,
                startTime,
                endTime: add(
                    transactionActivitiesCache.transactionActivities[0]
                        .timestamp,
                    { seconds: 1 }
                ),
                networkRPCMap,
                networkMap,
                earnHistoricalTakerUserCurrencyRateMap,
                installationId,
                swapsIOSwapRequestsMap,
                deBankContinuationToken: null,
                swapsIOContinuationToken: null,
            })
        return {
            transactionActivities,
            pendingSwapsIOTransactionActivities:
                swapsIOPendignTransactionActivities,
        }
    }

    const { transactionActivities, swapsIOPendignTransactionActivities } =
        await fetchTransactionActivitiesWithMinimumTransactionsCount({
            address,
            currentNetwork: { type: 'all_networks' as const },
            portfolio,
            cardConfig,
            keyStoreMap,
            sessionPassword,
            startTime,
            networkRPCMap,
            networkMap,
            earnHistoricalTakerUserCurrencyRateMap,
            installationId,
            swapsIOSwapRequestsMap,
            deBankContinuationToken: null,
            swapsIOContinuationToken: null,
            minimumTransactionsCount: MINIMUM_TRANSACTIONS_ACTIVITES_COUNT,
            maximumLoadAttemps: MAXIMUM_LOAD_ATTEMPTS,
            paginationTimeWindowInDays: PAGINATION_TIME_WINDOW_IN_DAYS,
            transactionActivities: [],
        })
    return {
        transactionActivities,
        pendingSwapsIOTransactionActivities:
            swapsIOPendignTransactionActivities,
    }
}

const deduplicateTransactions = (
    transactions: TransactionActivityV2[]
): TransactionActivityV2[] => {
    return transactions.reduce<{
        seen: Set<string>
        result: TransactionActivityV2[]
    }>(
        (acc, tx) => {
            const key = (() => {
                switch (tx.type) {
                    case 'send':
                    case 'receive':
                    case 'token_approval':
                    case 'token_approval_revoked':
                    case 'swap':
                    case 'incoming_bridge':
                    case 'outgoing_bridge':
                    case 'arbitrary_smart_contract_interaction':
                    case 'deposit_from_bank':
                    case 'send_to_bank':
                    case 'withdraw_from_card':
                    case 'deposit_into_card':
                    case 'card_owners_updated':
                    case 'card_spend_limit_updated':
                    case 'cashback_deposit':
                    case 'cashback_withdraw':
                    case 'cashback_reward':
                    case 'deployed_smart_wallet_gnosis':
                    case 'recovered_smart_wallet_gnosis':
                    case 'deposit_into_earn':
                    case 'withdraw_from_earn':
                    case 'recharge_disabled':
                    case 'recharge_target_set':
                    case 'failed_transaction':
                    case 'breward_claim':
                        return `${tx.networkHexId}-${tx.hash}`

                    case 'card_transaction':
                        return `card-${tx.cardTransaction.createdAt}`

                    case 'swaps_io_into_card':
                    case 'swaps_io_into_earn':
                    case 'swaps_io_buy':
                        return `swaps-io-${tx.swapsIOSwapRequest.hash}`

                    /* istanbul ignore next */
                    default:
                        return notReachable(tx)
                }
            })()

            if (!acc.seen.has(key)) {
                acc.seen.add(key)
                acc.result.push(tx)
            }
            return acc
        },
        { seen: new Set<string>(), result: [] }
    ).result
}

export const RecentTransactionActivityWidget = ({
    address,
    cardConfig,
    keyStoreMap,
    sessionPassword,
    portfolio,
    accountsMap,
    networkMap,
    networkRPCMap,
    refreshContainerState,
    transactionActivitiesCacheMap,
    earnHistoricalTakerUserCurrencyRateMap,
    swapsIOSwapRequestsMap,
    defaultCurrencyConfig,
    installationId,
    earn,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'closed' })

    const {
        transactionActivities,
        pendingSwapsIOTransactionActivities,
        pendingSendTransactionActivities,
        pendingBRewardClaimTransactionActivity,
        pendingARewardClaimTransactionActivity,
        pendingCardTopUpTransactionActivity,
    } = getTransactionActivitiesCache({
        address,
        transactionActivitiesCacheMap,
    })

    const [loadable, setLoadable] = useLoadedReloadableData(
        fetch,
        {
            params: {
                address,
                startTime: new Date(),
                cardConfig,
                keyStoreMap,
                sessionPassword,
                portfolio,
                installationId,
                networkRPCMap,
                networkMap,
                transactionActivitiesCacheMap,
                earnHistoricalTakerUserCurrencyRateMap,
                swapsIOSwapRequestsMap,
            },
            type: 'reloading',
            data: {
                transactionActivities,
                pendingSwapsIOTransactionActivities,
            },
        },
        {
            accumulate: (newData, prevData) => {
                const mergedPendingSwapsIO = [
                    ...newData.pendingSwapsIOTransactionActivities,
                    ...prevData.pendingSwapsIOTransactionActivities,
                ]

                return {
                    transactionActivities: deduplicateTransactions([
                        ...newData.transactionActivities,
                        ...prevData.transactionActivities,
                    ]),
                    pendingSwapsIOTransactionActivities: mergedPendingSwapsIO,
                }
            },
        }
    )

    const liveOnMsg = useLiveRef(onMsg)
    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_transaction_activities_loaded',
                    address: loadable.params.address,
                    transactionActivities: loadable.data.transactionActivities,
                    pendingSwapsIOTransactionActivities:
                        loadable.data.pendingSwapsIOTransactionActivities,
                })
                break
            case 'reloading':
            case 'subsequent_failed':
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveOnMsg])

    const transactionActivitiesCacheMapLive = useLiveRef(
        transactionActivitiesCacheMap
    )
    const earnHistoricalTakerUserCurrencyRateMapLive = useLiveRef(
        earnHistoricalTakerUserCurrencyRateMap
    )
    const keyStore = getKeyStore({ address, keyStoreMap })
    const swapsIOSwapRequestsMapLive = useLiveRef(swapsIOSwapRequestsMap)
    useEffect(() => {
        switch (refreshContainerState) {
            case 'refreshing':
                setLoadable((oldLoadable) => ({
                    ...oldLoadable,
                    type: 'reloading',
                    params: {
                        ...oldLoadable.params,
                        startTime: new Date(),
                        earnHistoricalTakerUserCurrencyRateMap:
                            earnHistoricalTakerUserCurrencyRateMapLive.current,
                        transactionActivitiesCacheMap:
                            transactionActivitiesCacheMapLive.current,
                        swapsIOSwapRequestsMap:
                            swapsIOSwapRequestsMapLive.current,
                    },
                }))
                break

            case 'refreshed':
                break
            /* istanbul ignore next */
            default:
                return notReachable(refreshContainerState)
        }
    }, [
        refreshContainerState,
        setLoadable,
        earnHistoricalTakerUserCurrencyRateMapLive,
        transactionActivitiesCacheMapLive,
        swapsIOSwapRequestsMapLive,
    ])

    return (
        <>
            <Layout
                earn={earn}
                networkRPCMap={networkRPCMap}
                address={address}
                transactions={loadable.data.transactionActivities}
                pendingBRewardClaimTransactionActivity={
                    pendingBRewardClaimTransactionActivity
                }
                pendingSwapsIOTransactionActivity={
                    loadable.data.pendingSwapsIOTransactionActivities
                }
                pendingSendTransactionActivity={
                    pendingSendTransactionActivities
                }
                pendingARewardClaimTransactionActivity={
                    pendingARewardClaimTransactionActivity
                }
                pendingCardTopUpTransactionActivity={
                    pendingCardTopUpTransactionActivity
                }
                keyStore={keyStore}
                accountsMap={accountsMap}
                networkMap={networkMap}
                installationId={installationId}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_pending_send_transaction_activity_failed':
                        case 'on_pending_breward_claim_transaction_activity_failed':
                        case 'on_pending_areward_claim_transaction_activity_failed':
                        case 'on_see_all_transactions_clicked':
                            onMsg(msg)
                            break

                        case 'on_transaction_activity_clicked':
                            setState({
                                type: 'transaction_details',
                                transaction: msg.transaction,
                            })
                            break
                        case 'on_pending_send_transaction_activity_clicked':
                            setState({
                                type: 'pending_send_transaction_details',
                                transaction: msg.pendingSendTransactionActivity,
                            })
                            break
                        case 'on_pending_breward_claim_transaction_activity_completed':
                        case 'on_pending_areward_claim_transaction_activity_completed':
                            onMsg(msg)
                            setLoadable((oldLoadable) => {
                                return {
                                    ...oldLoadable,
                                    data: {
                                        ...oldLoadable.data,
                                        transactionActivities:
                                            deduplicateTransactions([
                                                msg.completedTransactionActivity,
                                                ...oldLoadable.data
                                                    .transactionActivities,
                                            ]),
                                    },
                                }
                            })
                            return

                        case 'on_pending_card_top_up_failed':
                            onMsg(msg)
                            // FIXME :: @Nicvaniek - remove this and handle the same as below branch
                            const _mappedTx =
                                temp_mapBungeeCardTopUpToActivityTx(
                                    msg.transaction
                                )
                            return null

                        case 'on_pending_send_transaction_activity_completed':
                            onMsg(msg)
                            setLoadable((oldLoadable) => {
                                return {
                                    ...oldLoadable,
                                    data: {
                                        ...oldLoadable.data,
                                        transactionActivities:
                                            deduplicateTransactions([
                                                msg.completedSendTransactionActivity,
                                                ...oldLoadable.data
                                                    .transactionActivities,
                                            ]),
                                    },
                                }
                            })
                            switch (state.type) {
                                case 'pending_send_transaction_details':
                                    setState({
                                        type: 'transaction_details',
                                        transaction:
                                            msg.completedSendTransactionActivity,
                                    })
                                    break
                                case 'transaction_details':
                                case 'closed':
                                case 'swaps_io_transaction_details':
                                    break
                                default:
                                    return notReachable(state)
                            }
                            break

                        case 'on_swaps_io_completed_transaction_activity_clicked':
                            setState({
                                type: 'swaps_io_transaction_details',
                                transaction: msg.transaction,
                            })
                            break
                        case 'on_swaps_io_pending_transaction_clicked':
                            setState({
                                type: 'swaps_io_transaction_details',
                                transaction: msg.transaction,
                            })
                            break
                        case 'on_swaps_io_transaction_activity_swap_started':
                            setLoadable((oldLoadable) => {
                                return {
                                    ...oldLoadable,
                                    data: {
                                        ...oldLoadable.data,
                                        pendingSwapsIOTransactionActivities:
                                            oldLoadable.data.pendingSwapsIOTransactionActivities.map(
                                                (trx) =>
                                                    trx.swapsIOSwapRequest
                                                        .hash ===
                                                    msg.transaction
                                                        .swapsIOSwapRequest.hash
                                                        ? msg.transaction
                                                        : trx
                                            ),
                                    },
                                }
                            })
                            onMsg(msg)
                            break
                        case 'on_swaps_io_transaction_activity_completed':
                        case 'on_swaps_io_transaction_activity_failed':
                            setLoadable((oldLoadable) => {
                                return {
                                    ...oldLoadable,
                                    data: {
                                        ...oldLoadable.data,
                                        transactionActivities:
                                            deduplicateTransactions([
                                                msg.transaction,
                                                ...oldLoadable.data
                                                    .transactionActivities,
                                            ]),
                                        pendingSwapsIOTransactionActivities:
                                            oldLoadable.data.pendingSwapsIOTransactionActivities.filter(
                                                (trx) =>
                                                    trx.swapsIOSwapRequest
                                                        .hash !==
                                                    msg.transaction
                                                        .swapsIOSwapRequest.hash
                                            ),
                                    },
                                }
                            })
                            switch (state.type) {
                                case 'closed':
                                case 'transaction_details':
                                case 'pending_send_transaction_details':
                                    break
                                case 'swaps_io_transaction_details':
                                    switch (msg.transaction.state) {
                                        case 'completed':
                                            setState({
                                                type: 'swaps_io_transaction_details',
                                                transaction: msg.transaction,
                                            })
                                            break
                                        case 'failed':
                                            // Failed transactions don't show details, close modal
                                            setState({ type: 'closed' })
                                            break
                                        default:
                                            return notReachable(msg.transaction)
                                    }
                                    break
                                default:
                                    return notReachable(state)
                            }

                            onMsg(msg)
                            break
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                historicalTakerUserCurrencyRateMap={
                    earnHistoricalTakerUserCurrencyRateMap
                }
                state={state}
                earn={portfolio.earn}
                cardConfig={cardConfig}
                accountsMap={accountsMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                defaultCurrencyConfig={defaultCurrencyConfig}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setState({ type: 'closed' })
                            break

                        default:
                            return notReachable(msg.type)
                    }
                }}
            />
        </>
    )
}
