import { useEffect } from 'react'

import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'

// eslint-disable-next-line zeal-domains/no-feature-deep-import
import { SubmittedCardTopUp } from '@zeal/domains/Card/features/CardAddCash/CardAddCashV2/types'
import { SubmitedTransactionFailed } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction'
import {
    SubmittedUserOperationFailed,
    SubmittedUserOperationRejected,
} from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation' // FIXME :: @Nicvaniek

type Props = {
    transaction: SubmittedCardTopUp
    onMsg: (msg: Msg) => void
}

type Msg = {
    type: 'on_pending_card_top_up_failed' // FIXME :: @mike use status change instead
    transaction: SubmittedCardTopUp
}

type Response =
    | { type: 'success' }
    | { type: 'failed_tx'; transaction: SubmitedTransactionFailed }
    | {
          type: 'failed_user_operation'
          userOperation:
              | SubmittedUserOperationFailed
              | SubmittedUserOperationRejected
      }
    | { type: 'failed_bungee'; quoteRequestHash: Hexadecimal.Hexadecimal }

const fetch = async (_: {
    transaction: SubmittedCardTopUp
}): Promise<Response> => {
    // FIXME :: @Nicvaniek - add retries
    throw new Error(
        `Not implemented - if native swap, first recursively monitor tx on-chain, and then poll bungee for swap completion
        if not native swap, just recursively monitor bungee
        
        We want to keep this item pending UNTIL the funds are released on the card. So we need to calculate the block in
        which it will be mined, and then wait for that block to be mined, then reload the card balance on-chain.
        `
    )
}

export const PendingCardTopUpListItem = ({ transaction }: Props) => {
    const [_loadable] = useLoadableData(fetch, {
        type: 'loading',
        params: { transaction },
    })

    useEffect(() => {
        // FIXME :: @Nicvaniek - if failed, report to databricks and throw specific Errors for Sentry + onMsg
        // FIXME :: @Nicvaniek - success -> onMsg
    }, [])

    // FIXME :: @Nicvaniek
    throw new Error(
        `Not implemented - render pending layout based on ${transaction}`
    )
}
