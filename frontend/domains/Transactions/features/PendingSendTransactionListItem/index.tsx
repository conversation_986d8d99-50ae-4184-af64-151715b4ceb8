import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { captureUserOperationFailureError } from '@zeal/domains/Error/helpers/captureTransactionFailureError'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { fetchSubmittedUserOperation } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation/api/fetchSubmittedUserOperation'
import {
    PendingSendTransactionActivity,
    SendTransactionActivity,
} from '@zeal/domains/Transactions'
import { PendingSendTransactionListItem as PendingSendTransactionListItemUI } from '@zeal/domains/Transactions/components/PendingSendTransactionListItem'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    address: Web3.address.Address
    transaction: PendingSendTransactionActivity
    accountsMap: AccountsMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_pending_send_transaction_activity_completed'
          address: Web3.address.Address
          pendingSendTransactionActivity: PendingSendTransactionActivity
          completedSendTransactionActivity: SendTransactionActivity
      }
    | {
          type: 'on_pending_send_transaction_activity_failed'
          address: Web3.address.Address
          pendingSendTransactionActivity: PendingSendTransactionActivity
      }
    | {
          type: 'on_pending_send_transaction_activity_clicked'
          pendingSendTransactionActivity: PendingSendTransactionActivity
      }

const POLL_INTERVAL_MILLISECONDS = 1000
const ON_SEND_TRANSACTION_FINISHED_DELAY = 1000

export const PendingSendTransactionListItem = ({
    address,
    transaction,
    networkMap,
    installationId,
    onMsg,
    networkRPCMap,
    accountsMap,
}: Props) => {
    const [loadable] = usePollableData(
        fetchSubmittedUserOperation,
        {
            type: 'loading',
            params: {
                network: networkMap[transaction.networkHexId],
                submittedUserOperation: transaction.submittedUserOperation,
                networkRPCMap,
            },
        },
        {
            pollIntervalMilliseconds: POLL_INTERVAL_MILLISECONDS,
            stopIf: (loadable) => {
                switch (loadable.type) {
                    case 'loaded':
                        switch (loadable.data.state) {
                            case 'pending':
                            case 'bundled':
                                return false
                            case 'completed':
                            case 'rejected':
                            case 'failed':
                                return true

                            /* istanbul ignore next */
                            default:
                                return notReachable(loadable.data)
                        }
                    case 'reloading':
                    case 'subsequent_failed':
                    case 'loading':
                    case 'error':
                        return false

                    /* istanbul ignore next */
                    default:
                        return notReachable(loadable)
                }
            },
        }
    )

    const actionSourceLive = useLiveRef(transaction.actionSource)
    const onMsgLive = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded': {
                switch (loadable.data.state) {
                    case 'pending':
                    case 'bundled':
                        break
                    case 'completed':
                        postUserEvent({
                            type: 'TransactionCompletedEvent',
                            installationId,
                            keystoreType: 'Safe',
                            network: loadable.params.network.hexChainId,
                            source: actionSourceLive.current
                                .transactionEventSource,
                        })
                        break
                    case 'rejected':
                    case 'failed':
                        captureUserOperationFailureError({
                            userOperation: loadable.data,
                            actionSource: actionSourceLive.current,
                            network: loadable.params.network,
                        })
                        postUserEvent({
                            type: 'TransactionFailedEvent',
                            installationId,
                            keystoreType: 'Safe',
                            state: loadable.data.state,
                            network: loadable.params.network.hexChainId,
                            source: actionSourceLive.current
                                .transactionEventSource,
                        })
                        break

                    /* istanbul ignore next */
                    default:
                        return notReachable(loadable.data)
                }
                break
            }
            case 'loading':
            case 'reloading':
                break
            case 'error':
            case 'subsequent_failed':
                captureError(loadable.error)
                break

            /* istanbul ignore next */
            default:
                notReachable(loadable)
        }
    }, [
        loadable,
        actionSourceLive,
        onMsgLive,
        address,
        installationId,
        transaction,
    ])

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded': {
                switch (loadable.data.state) {
                    case 'pending':
                    case 'bundled':
                        break
                    case 'completed':
                        const { bundleTransactionHash, completedAt } =
                            loadable.data

                        const timeoutId = setTimeout(
                            () =>
                                onMsgLive.current({
                                    type: 'on_pending_send_transaction_activity_completed',
                                    address,
                                    pendingSendTransactionActivity: transaction,
                                    completedSendTransactionActivity: {
                                        type: 'send',
                                        receiver: transaction.receiver,
                                        amount: transaction.amount,
                                        amountInDefaultCurrency:
                                            transaction.amountInDefaultCurrency,
                                        networkHexId: transaction.networkHexId,
                                        hash: bundleTransactionHash,
                                        timestamp: new Date(completedAt),
                                        paidFee: null,
                                    },
                                }),
                            ON_SEND_TRANSACTION_FINISHED_DELAY
                        )
                        return () => clearTimeout(timeoutId)
                    case 'rejected':
                    case 'failed':
                        const timeourId = setTimeout(
                            () =>
                                onMsgLive.current({
                                    type: 'on_pending_send_transaction_activity_failed',
                                    address,
                                    pendingSendTransactionActivity: transaction,
                                }),
                            ON_SEND_TRANSACTION_FINISHED_DELAY
                        )
                        return () => clearTimeout(timeourId)

                    /* istanbul ignore next */
                    default:
                        return notReachable(loadable.data)
                }
                break
            }
            case 'loading':
            case 'reloading':
                break
            case 'error':
            case 'subsequent_failed':
                captureError(loadable.error)
                break

            /* istanbul ignore next */
            default:
                notReachable(loadable)
        }
    }, [
        loadable,
        actionSourceLive,
        onMsgLive,
        address,
        installationId,
        transaction,
    ])

    return (
        <PendingSendTransactionListItemUI
            transaction={transaction}
            accountsMap={accountsMap}
            onClick={() =>
                onMsg({
                    type: 'on_pending_send_transaction_activity_clicked',
                    pendingSendTransactionActivity: transaction,
                })
            }
        />
    )
}
