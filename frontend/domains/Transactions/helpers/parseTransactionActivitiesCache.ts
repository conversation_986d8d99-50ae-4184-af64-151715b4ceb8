import { notReachable } from '@zeal/toolkit'
import { parseDate } from '@zeal/toolkit/Date'
import { parse as parseHexdecimal } from '@zeal/toolkit/Hexadecimal'
import {
    arrayOf,
    bigint,
    failure,
    match,
    nullableOf,
    number,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
    ValidObject,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { parseCardTransactionFromStorage } from '@zeal/domains/Card/helpers/parseCardTransactionFromStorage'
import { parseSubmittedCardTopUp } from '@zeal/domains/Card/helpers/parseSubmittedCardTopUp'
import { parseSwapsIOSwapRequestFromStorage } from '@zeal/domains/Currency/domains/SwapsIO/parsers/parseSwapsIOSwapRequestsMap'
import {
    parseDeployedTaker,
    parseTakerType,
} from '@zeal/domains/Earn/parsers/parseEarn'
import { parseInternalTransactionActionSource } from '@zeal/domains/Main/parsers/parseInternalTransactionActionSource'
import {
    parseCryptoMoneyFromStorage,
    parseFiatMoneyFromStorage,
    parseMoneyFromStorage,
} from '@zeal/domains/Money/helpers/parse'
import { parseNetworkHexId } from '@zeal/domains/Network/helpers/parse'
import { parseSmartContract } from '@zeal/domains/SmartContract/parsers/parseSmartContract'
import {
    parseIncludedInBlock,
    parseQueued,
} from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/parsers/parseSubmitedTransaction'
import { SubmittedUserOperationType } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import { parseSubmittedUserOperationType } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation/parsers/parseSubmittedUserOperationType'

import {
    ArbitrarySmartContractTransactionType,
    CardTransactionActivity,
    PendingARewardClaimTransactionActivity,
    PendingBRewardClaimTransactionActivity,
    PendingSendTransactionActivity,
    RegularTransactionActivity,
    SwapsIOBuyTransactionActivity,
    SwapsIOIntoCardTransactionActivity,
    SwapsIOIntoEarnTransactionActivity,
    SwapsIOTransactionActivityCommon,
    SwapsIOTransactionActivityCompleted,
    SwapsIOTransactionActivityFailed,
    SwapsIOTransactionActivityPending,
    TransactionActivitiesCache,
    TransactionActivityV2,
} from '..'

type UnwarpArray<T> = T extends Array<infer U> ? U : T
declare const transactionActivity: NonNullable<
    UnwarpArray<TransactionActivitiesCache[keyof TransactionActivitiesCache]>
>

const _ = () => {
    switch (transactionActivity.type) {
        case 'earn_sign_typed_data':
        case 'swap_sign_typed_data':
        case 'swap_eoa_native_send_transaction':
        case 'send_safe':
        case 'swap_safe_native_send_transaction':
        case 'arbitrary_smart_contract_interaction':
        case 'deposit_from_bank':
        case 'send_to_bank':
        case 'deposit_into_card':
        case 'withdraw_from_card':
        case 'card_owners_updated':
        case 'card_spend_limit_updated':
        case 'cashback_deposit':
        case 'cashback_withdraw':
        case 'cashback_reward':
        case 'deployed_smart_wallet_gnosis':
        case 'recovered_smart_wallet_gnosis':
        case 'deposit_into_earn':
        case 'withdraw_from_earn':
        case 'breward_claim':
        case 'swap':
        case 'recharge_disabled':
        case 'recharge_target_set':
        case 'receive':
        case 'send':
        case 'token_approval':
        case 'token_approval_revoked':
        case 'failed_transaction':
        case 'card_transaction':
        case 'swaps_io_into_card':
        case 'swaps_io_into_earn':
        case 'swaps_io_buy':
        case 'pending_send':
        case 'incoming_bridge':
        case 'outgoing_bridge':
        case 'pending_breward_claim':
        case 'pending_areward_claim':
        case 'send_eoa':
            // Don't forget to update parsers if you add new transaction activity type
            return
        /* istanbul ignore next */
        default:
            return notReachable(transactionActivity)
    }
}

export const parseTransactionActivitiesCache = (
    input: unknown
): Result<unknown, TransactionActivitiesCache> =>
    object(input).andThen((obj) =>
        shape({
            transactionActivities: arrayOf(
                obj.transactionActivities,
                parseTransactionActivityFromStorage
            ),
            pendingSwapsIOTransactionActivities: arrayOf(
                obj.pendingSwapsIOTransactionActivities,
                parseSwapsIOPendingTransactionActivities
            ),
            pendingSendTransactionActivities: arrayOf(
                obj.pendingSendTransactionActivities,
                parsePendingSendTransactionActivityFromStorage
            ),
            pendingBRewardClaimTransactionActivity: nullableOf(
                obj.pendingBRewardClaimTransactionActivity,
                parsePendingBRewardClaimTransactionActivityFromStorage
            ),
            pendingARewardClaimTransactionActivity: nullableOf(
                obj.pendingARewardClaimTransactionActivity,
                parsePendingARewardClaimTransactionActivityFromStorage
            ),
            pendingCardTopUpTransactionActivity: arrayOf(
                obj.pendingCardTopUpTransactionActivity,
                parseSubmittedCardTopUp
            ),
        })
    )

const parseTransactionActivityFromStorage = (
    input: unknown
): Result<unknown, TransactionActivityV2> =>
    object(input).andThen((obj) =>
        oneOf(obj, [
            parseRegularTransactionActivityFromStorage(obj),
            parseCardTransactionActivityFromStorage(obj),
            parseSwapsIOFinishedTransactionActivitiesFromStorage(obj),
        ])
    )

const parsePendingBRewardClaimTransactionActivityFromStorage = (
    input: unknown
): Result<unknown, PendingBRewardClaimTransactionActivity> =>
    object(input).andThen((inputObj) =>
        shape({
            type: match(inputObj.type, 'pending_breward_claim'),
            taker: parseDeployedTaker(inputObj.taker),
            submittedTransaction: oneOf(inputObj.submittedTransaction, [
                parseQueued(inputObj.submittedTransaction),
                parseIncludedInBlock(inputObj.submittedTransaction),
            ]),
            amount: parseCryptoMoneyFromStorage(inputObj.amount),
            amountInUserCurrency: parseMoneyFromStorage(
                inputObj.amountInUserCurrency
            ),
            amountInDefaultCurrency: nullableOf(
                inputObj.amountInDefaultCurrency,
                parseFiatMoneyFromStorage
            ),
        })
    )

const parsePendingARewardClaimTransactionActivityFromStorage = (
    input: unknown
): Result<unknown, PendingARewardClaimTransactionActivity> =>
    object(input).andThen((inputObj) =>
        shape({
            type: match(inputObj.type, 'pending_areward_claim'),
            amountInTakerInvestmentCurrency: parseCryptoMoneyFromStorage(
                inputObj.amount
            ),
            submittedTransaction: oneOf(inputObj.submittedTransaction, [
                parseQueued(inputObj.submittedTransaction),
                parseIncludedInBlock(inputObj.submittedTransaction),
            ]),
        })
    )

const parseSubmittedUserOperationTypeFromStorage = (
    input: unknown
): Result<unknown, SubmittedUserOperationType> =>
    oneOf(input, [
        parseSubmittedUserOperationType(input),
        success('user_operation' as const),
    ])

const parsePendingSendTransactionActivityFromStorage = (
    input: unknown
): Result<unknown, PendingSendTransactionActivity> => {
    return object(input).andThen((inputObj) =>
        shape({
            type: match(inputObj.type, 'pending_send' as const),
            receiver: Web3.address.parse(inputObj.receiver),
            amount: parseCryptoMoneyFromStorage(inputObj.amount),
            amountInDefaultCurrency: nullableOf(
                inputObj.amountInDefaultCurrency,
                parseFiatMoneyFromStorage
            ),
            networkHexId: parseNetworkHexId(inputObj.networkHexId),
            actionSource: parseInternalTransactionActionSource(
                inputObj.actionSource
            ),
            submittedUserOperation: object(
                inputObj.submittedUserOperation
            ).andThen((userOpObj) =>
                oneOf(userOpObj, [
                    shape({
                        type: parseSubmittedUserOperationTypeFromStorage(
                            userOpObj.type
                        ),
                        state: match(userOpObj.state, 'pending' as const),
                        userOperationHash: parseHexdecimal(
                            userOpObj.userOperationHash
                        ),
                        sender: Web3.address.parse(userOpObj.sender),
                        queuedAt: number(userOpObj.queuedAt),
                        submittedBlock: bigint(userOpObj.submittedBlock),
                    }),
                    shape({
                        type: parseSubmittedUserOperationTypeFromStorage(
                            userOpObj.type
                        ),
                        state: match(userOpObj.state, 'bundled' as const),
                        bundleTransactionHash: parseHexdecimal(
                            userOpObj.bundleTransactionHash
                        ),
                        userOperationHash: parseHexdecimal(
                            userOpObj.userOperationHash
                        ),
                        sender: Web3.address.parse(userOpObj.sender),
                        queuedAt: number(userOpObj.queuedAt),
                        submittedBlock: bigint(userOpObj.submittedBlock),
                    }),
                ])
            ),
        })
    )
}

const parseRegularTransactionActivityFromStorage = (
    input: ValidObject
): Result<unknown, RegularTransactionActivity> =>
    shape({
        common: shape({
            networkHexId: parseNetworkHexId(input.networkHexId),
            hash: string(input.hash),
            timestamp: parseDate(input.timestamp),
            paidFee: nullableOf(input.paidFee, (paidFeeInput) =>
                object(paidFeeInput).andThen((paidFeeObj) =>
                    shape({
                        priceInNativeCurrency: parseCryptoMoneyFromStorage(
                            paidFeeObj.priceInNativeCurrency
                        ),
                        priceInDefaultCurrency: nullableOf(
                            paidFeeObj.priceInDefaultCurrency,
                            parseFiatMoneyFromStorage
                        ),
                    })
                )
            ),
        }),
        transaction: oneOf(input, [
            oneOf(input, [
                shape({
                    type: match(
                        input.type,
                        'arbitrary_smart_contract_interaction' as const
                    ),
                    functionSignature: nullableOf(input.functionSignature, () =>
                        string(input.functionSignature)
                    ),
                    smartContract: nullableOf(input.smartContract, () =>
                        parseSmartContract(input.smartContract)
                    ),
                    transactionType:
                        parseArbitratySmartContractInteractionTransactionType(
                            input.transactionType
                        ),
                }),
                shape({
                    type: match(input.type, 'failed_transaction' as const),
                    functionSignature: nullableOf(
                        input.functionSignature,
                        parseHexdecimal
                    ),
                    smartContract: nullableOf(
                        input.smartContract,
                        parseSmartContract
                    ),
                }),
                shape({
                    type: match(input.type, 'deposit_from_bank' as const),
                    fromAmount: parseFiatMoneyFromStorage(input.fromAmount),
                    toAmount: parseCryptoMoneyFromStorage(input.toAmount),
                }),
                shape({
                    type: match(input.type, 'send_to_bank' as const),
                    fromAmount: parseCryptoMoneyFromStorage(input.fromAmount),
                    toAmount: parseFiatMoneyFromStorage(input.toAmount),
                }),
                shape({
                    type: match(input.type, 'withdraw_from_card' as const),
                    token: parseCryptoMoneyFromStorage(input.token),
                }),
                shape({
                    type: match(input.type, 'send' as const),
                    receiver: Web3.address.parse(input.receiver),
                    amount: parseCryptoMoneyFromStorage(input.amount),
                    amountInDefaultCurrency: nullableOf(
                        input.amountInDefaultCurrency,
                        parseFiatMoneyFromStorage
                    ),
                }),
                shape({
                    type: match(input.type, 'outgoing_bridge' as const),
                    toToken: parseCryptoMoneyFromStorage(input.amount),
                }),
                shape({
                    type: match(input.type, 'incoming_bridge' as const),
                    fromToken: parseCryptoMoneyFromStorage(input.amount),
                }),
                shape({
                    type: match(input.type, 'receive' as const),
                    sender: Web3.address.parse(input.sender),
                    amount: parseCryptoMoneyFromStorage(input.amount),
                    amountInDefaultCurrency: nullableOf(
                        input.amountInDefaultCurrency,
                        parseFiatMoneyFromStorage
                    ),
                }),
                shape({
                    type: match(input.type, 'swap' as const),
                    fromToken: parseCryptoMoneyFromStorage(input.fromToken),
                    toToken: parseCryptoMoneyFromStorage(input.toToken),
                }),
            ]),
            oneOf(input, [
                shape({
                    type: match(input.type, 'recharge_target_set' as const),
                    target: parseCryptoMoneyFromStorage(input.target),
                }),
                shape({
                    type: match(input.type, 'recharge_disabled' as const),
                }),
                shape({
                    type: match(input.type, 'card_owners_updated' as const),
                }),
                shape({
                    type: match(
                        input.type,
                        'card_spend_limit_updated' as const
                    ),
                    spendLimit: parseCryptoMoneyFromStorage(input.spendLimit),
                }),
            ]),
            oneOf(input, [
                shape({
                    type: match(input.type, 'cashback_deposit' as const),
                    fromAddress: Web3.address.parse(input.fromAddress),
                    amount: parseCryptoMoneyFromStorage(input.amount),
                }),
                shape({
                    type: match(input.type, 'cashback_withdraw' as const),
                    toAddress: Web3.address.parse(input.toAddress),
                    amount: parseCryptoMoneyFromStorage(input.amount),
                }),
                shape({
                    type: match(input.type, 'cashback_reward' as const),
                    amount: parseCryptoMoneyFromStorage(input.amount),
                }),
                shape({
                    type: match(input.type, 'breward_claim' as const),
                    takerType: parseTakerType(input.takerType),
                    fromAmount: success(null),
                    toAmount: parseCryptoMoneyFromStorage(input.toAmount),
                    toAmountInUserCurrency: parseMoneyFromStorage(
                        input.toAmountInUserCurrency
                    ),
                }),
                shape({
                    type: match(input.type, 'deposit_into_earn' as const),
                    takerType: parseTakerType(input.takerType),
                    fromAmount: nullableOf(
                        input.fromAmount,
                        parseCryptoMoneyFromStorage
                    ),
                    toAmount: parseCryptoMoneyFromStorage(input.toAmount),
                    toAmountInUserCurrency: parseMoneyFromStorage(
                        input.toAmountInUserCurrency
                    ),
                }),
                shape({
                    type: match(input.type, 'withdraw_from_earn' as const),
                    taker: parseDeployedTaker(input.taker),
                    toAmount: parseCryptoMoneyFromStorage(input.toAmount),
                    fromAmountInUserCurrency: parseMoneyFromStorage(
                        input.fromAmountInUserCurrency
                    ),
                }),
                shape({
                    type: oneOf(input.type, [
                        match(
                            input.type,
                            'deployed_smart_wallet_gnosis' as const
                        ),
                        match(
                            input.type,
                            'recovered_smart_wallet_gnosis' as const
                        ),
                    ]),
                }),
                shape({
                    type: oneOf(input.type, [
                        match(input.type, 'token_approval' as const),
                        match(input.type, 'token_approval_revoked' as const),
                    ]),
                    approveTo: parseSmartContract(input.approveTo),
                    limit: object(input.limit).andThen((limitObj) =>
                        oneOf(limitObj, [
                            shape({
                                type: match(limitObj.type, 'Limited' as const),
                                amount: parseCryptoMoneyFromStorage(
                                    limitObj.amount
                                ),
                            }),
                            shape({
                                type: match(
                                    limitObj.type,
                                    'Unlimited' as const
                                ),
                                amount: parseCryptoMoneyFromStorage(
                                    limitObj.amount
                                ),
                            }),
                        ])
                    ),
                }),
                shape({
                    type: match(input.type, 'deposit_into_card' as const),
                    from: oneOf(input, [
                        shape({
                            from: match(input.from, 'bank' as const),
                            toAmount: parseCryptoMoneyFromStorage(
                                input.toAmount
                            ),
                            fromAmount: parseFiatMoneyFromStorage(
                                input.fromAmount
                            ),
                        }),
                        shape({
                            from: match(input.from, 'earn' as const),
                            taker: parseDeployedTaker(input.taker),
                            toAmount: parseCryptoMoneyFromStorage(
                                input.toAmount
                            ),
                            fromAmountInUserCurrency: parseMoneyFromStorage(
                                input.fromAmountInUserCurrency
                            ),
                        }),
                        shape({
                            from: match(input.from, 'wallet' as const),
                            toAmount: parseCryptoMoneyFromStorage(
                                input.toAmount
                            ),
                            fromAmount: parseCryptoMoneyFromStorage(
                                input.fromAmount
                            ),
                        }),
                    ]),
                }).map(({ type, from }) => ({ type, ...from })),
            ]),
        ]),
    }).map(({ common, transaction }) => ({
        ...common,
        ...transaction,
    }))

const parseArbitratySmartContractInteractionTransactionType = (
    input: unknown
): Result<unknown, ArbitrarySmartContractTransactionType> =>
    object(input).andThen((transactionTypeObj) =>
        oneOf(transactionTypeObj, [
            shape({
                type: match(
                    transactionTypeObj.type,
                    'exactlyOneInOneOut' as const
                ),
                incoming: parseCryptoMoneyFromStorage(
                    transactionTypeObj.incoming
                ),
                outgoing: parseCryptoMoneyFromStorage(
                    transactionTypeObj.outgoing
                ),
            }),
            shape({
                type: match(
                    transactionTypeObj.type,
                    'multipleInAndOut' as const
                ),
                incoming: arrayOf(
                    transactionTypeObj.incoming,
                    parseCryptoMoneyFromStorage
                ),
                outgoing: arrayOf(
                    transactionTypeObj.outgoing,
                    parseCryptoMoneyFromStorage
                ),
            }),
            shape({
                type: match(
                    transactionTypeObj.type,
                    'multipleInAndOut' as const
                ),
                incoming: arrayOf(
                    transactionTypeObj.incoming,
                    parseCryptoMoneyFromStorage
                ),
                outgoing: arrayOf(
                    transactionTypeObj.outgoing,
                    parseCryptoMoneyFromStorage
                ),
            }),
            shape({
                type: match(transactionTypeObj.type, 'onlyIn' as const),
                incoming: arrayOf(
                    transactionTypeObj.incoming,
                    parseCryptoMoneyFromStorage
                ),
                priceInDefaultCurrency: nullableOf(
                    transactionTypeObj.priceInDefaultCurrency,
                    parseFiatMoneyFromStorage
                ),
            }),
            shape({
                type: match(transactionTypeObj.type, 'onlyOut' as const),
                outgoing: arrayOf(
                    transactionTypeObj.outgoing,
                    parseCryptoMoneyFromStorage
                ),
                priceInDefaultCurrency: nullableOf(
                    transactionTypeObj.priceInDefaultCurrency,
                    parseFiatMoneyFromStorage
                ),
            }),
            shape({
                type: match(transactionTypeObj.type, 'noInNoOut' as const),
            }),
        ])
    )

const parseCardTransactionActivityFromStorage = (
    input: ValidObject
): Result<unknown, CardTransactionActivity> =>
    shape({
        type: match(input.type, 'card_transaction' as const),
        cardTransaction: parseCardTransactionFromStorage(input.cardTransaction),
        timestamp: parseDate(input.timestamp),
    })

const parseSwapsIOFinishedTransactionActivitiesFromStorage = (
    input: ValidObject
): Result<
    unknown,
    SwapsIOTransactionActivityCompleted | SwapsIOTransactionActivityFailed
> =>
    shape({
        common: parseSwapsIOSwapRequestFromStorage(
            input.swapsIOSwapRequest
        ).andThen((swapsIOSwapRequest) =>
            oneOf(input, [
                shape({
                    state: match(input.state, 'completed' as const),
                    swapsIOSwapRequest: (() => {
                        switch (swapsIOSwapRequest.state) {
                            case 'cancelled_no_slash':
                            case 'cancelled_slashed':
                            case 'cancelled_awaiting_slash':
                            case 'awaiting_liq_send':
                            case 'awaiting_signature':
                            case 'awaiting_receive':
                            case 'awaiting_send':
                                return failure({ type: 'wrong_state' })
                            case 'completed_liq_sent':
                            case 'completed_sent':
                                return success(swapsIOSwapRequest)
                            /* istanbul ignore next */
                            default:
                                return notReachable(swapsIOSwapRequest)
                        }
                    })(),
                    timestamp: parseDate(input.timestamp),
                }),
                shape({
                    state: match(input.state, 'failed' as const),
                    swapsIOSwapRequest: (() => {
                        switch (swapsIOSwapRequest.state) {
                            case 'cancelled_no_slash':
                            case 'cancelled_slashed':
                                return success(swapsIOSwapRequest)
                            case 'cancelled_awaiting_slash':
                            case 'awaiting_liq_send':
                            case 'awaiting_signature':
                            case 'awaiting_receive':
                            case 'awaiting_send':
                            case 'completed_sent':
                            case 'completed_liq_sent':
                                return failure({ type: 'wrong_state' })
                            /* istanbul ignore next */
                            default:
                                return notReachable(swapsIOSwapRequest)
                        }
                    })(),
                    timestamp: parseDate(input.timestamp),
                }),
            ])
        ),
        transaction: oneOf(input, [
            parseSwapsIOBuyTransactionActivityFromStorage(input),
            parseSwapsIOIntoCardTransactionActivityFromStorage(input),
            parseSwapsIOIntoEarnTransactionActivityFromStorage(input),
        ]),
    }).map(({ common, transaction }) => ({
        ...common,
        ...transaction,
    }))

export const parseSwapsIOPendingTransactionActivities = (
    input: unknown
): Result<unknown, SwapsIOTransactionActivityPending> =>
    object(input).andThen((inputObj) =>
        shape({
            common: parseSwapsIOSwapRequestFromStorage(
                inputObj.swapsIOSwapRequest
            ).andThen((swapsIOSwapRequest) =>
                shape({
                    state: match(inputObj.state, 'pending' as const),
                    swapsIOSwapRequest: (() => {
                        switch (swapsIOSwapRequest.state) {
                            case 'cancelled_no_slash':
                            case 'cancelled_slashed':
                            case 'completed_liq_sent':
                            case 'completed_sent':
                                return failure({ type: 'wrong_state' })
                            case 'cancelled_awaiting_slash':
                            case 'awaiting_liq_send':
                            case 'awaiting_signature':
                            case 'awaiting_receive':
                            case 'awaiting_send':
                                return success(swapsIOSwapRequest)
                            /* istanbul ignore next */
                            default:
                                return notReachable(swapsIOSwapRequest)
                        }
                    })(),
                    createdAt: parseDate(inputObj.createdAt),
                })
            ),
            transaction: oneOf(inputObj, [
                parseSwapsIOBuyTransactionActivityFromStorage(inputObj),
                parseSwapsIOIntoCardTransactionActivityFromStorage(inputObj),
                parseSwapsIOIntoEarnTransactionActivityFromStorage(inputObj),
            ]),
        }).map(({ common, transaction }) => ({
            ...common,
            ...transaction,
        }))
    )

const parseSwapsIOBuyTransactionActivityFromStorage = (
    input: unknown
): Result<
    unknown,
    Omit<SwapsIOBuyTransactionActivity, keyof SwapsIOTransactionActivityCommon>
> =>
    object(input).andThen((inputObj) =>
        shape({
            type: match(inputObj.type, 'swaps_io_buy' as const),
        })
    )

const parseSwapsIOIntoCardTransactionActivityFromStorage = (
    input: unknown
): Result<
    unknown,
    Omit<
        SwapsIOIntoCardTransactionActivity,
        keyof SwapsIOTransactionActivityCommon
    >
> =>
    object(input).andThen((inputObj) =>
        shape({
            type: match(inputObj.type, 'swaps_io_into_card' as const),
            toAmount: parseFiatMoneyFromStorage(inputObj.toAmount),
        })
    )

const parseSwapsIOIntoEarnTransactionActivityFromStorage = (
    input: unknown
): Result<
    unknown,
    Omit<
        SwapsIOIntoEarnTransactionActivity,
        keyof SwapsIOTransactionActivityCommon
    >
> =>
    object(input).andThen((inputObj) =>
        shape({
            type: match(inputObj.type, 'swaps_io_into_earn' as const),
            taker: parseDeployedTaker(inputObj.taker),
            toAmountInUserCurrency: nullableOf(
                inputObj.toAmountInUserCurrency,
                parseMoneyFromStorage
            ),
            toAmount: parseCryptoMoneyFromStorage(inputObj.toAmount),
        })
    )
