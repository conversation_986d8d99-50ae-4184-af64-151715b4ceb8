import { notReachable } from '@zeal/toolkit'

import { RegularTransactionActivity } from '@zeal/domains/Transactions'

import { FormattedMoneyOrCash } from '../FormattedMoneyOrCash'

type Props = {
    transaction: RegularTransactionActivity
}

export const SideSubtitle = ({ transaction }: Props) => {
    switch (transaction.type) {
        case 'send':
            return (
                transaction.amountInDefaultCurrency && (
                    <FormattedMoneyOrCash
                        withSymbol
                        sign="-"
                        money={transaction.amountInDefaultCurrency}
                    />
                )
            )
        case 'receive':
            return (
                transaction.amountInDefaultCurrency && (
                    <FormattedMoneyOrCash
                        withSymbol
                        sign="+"
                        money={transaction.amountInDefaultCurrency}
                    />
                )
            )
        case 'token_approval':
        case 'token_approval_revoked':
            return transaction.limit.amount.currency.symbol
        case 'swap':
            return (
                <FormattedMoneyOrCash
                    withSymbol
                    sign="-"
                    money={transaction.fromToken}
                />
            )
        case 'incoming_bridge':
            return (
                <FormattedMoneyOrCash
                    withSymbol
                    sign="+"
                    money={transaction.fromToken}
                />
            )
        case 'outgoing_bridge':
            return (
                <FormattedMoneyOrCash
                    withSymbol
                    sign="-"
                    money={transaction.toToken}
                />
            )
        case 'arbitrary_smart_contract_interaction': {
            const { transactionType } = transaction

            switch (transactionType.type) {
                case 'exactlyOneInOneOut':
                    return (
                        <FormattedMoneyOrCash
                            withSymbol={true}
                            sign="-"
                            money={transactionType.outgoing}
                        />
                    )

                case 'onlyIn':
                    return (
                        transactionType.priceInDefaultCurrency && (
                            <FormattedMoneyOrCash
                                withSymbol={true}
                                sign="+"
                                money={transactionType.priceInDefaultCurrency}
                            />
                        )
                    )

                case 'onlyOut':
                    return (
                        transactionType.priceInDefaultCurrency && (
                            <FormattedMoneyOrCash
                                withSymbol={true}
                                sign="-"
                                money={transactionType.priceInDefaultCurrency}
                            />
                        )
                    )

                case 'multipleInAndOut':
                    return transactionType.outgoing.length === 1 ? (
                        <FormattedMoneyOrCash
                            withSymbol
                            sign="-"
                            money={transactionType.outgoing[0]}
                        />
                    ) : (
                        `-${transactionType.outgoing
                            .slice(0, 2)
                            .map(({ currency }) => currency.symbol)
                            .join(', ')}`
                    )
                case 'noInNoOut':
                    return null

                /* istanbul ignore next */
                default:
                    return notReachable(transactionType)
            }
        }
        case 'withdraw_from_earn':
            return (
                transaction.fromAmountInUserCurrency && (
                    <FormattedMoneyOrCash
                        withSymbol
                        sign="-"
                        money={transaction.fromAmountInUserCurrency}
                    />
                )
            )
        case 'deposit_into_earn':
            return transaction.fromAmount ? (
                <FormattedMoneyOrCash
                    withSymbol
                    sign="-"
                    money={transaction.fromAmount}
                />
            ) : null
        case 'deposit_from_bank':
            return (
                <FormattedMoneyOrCash
                    withSymbol
                    sign="+"
                    money={transaction.fromAmount}
                />
            )
        case 'send_to_bank':
            return (
                <FormattedMoneyOrCash
                    withSymbol
                    sign="-"
                    money={transaction.toAmount}
                />
            )
        case 'cashback_withdraw':
        case 'cashback_deposit':
        case 'cashback_reward':
            return null
        case 'withdraw_from_card':
            return (
                <FormattedMoneyOrCash
                    withSymbol
                    sign="-"
                    money={transaction.token}
                />
            )
        case 'deposit_into_card':
            switch (transaction.from) {
                case 'bank':
                    return (
                        <FormattedMoneyOrCash
                            withSymbol
                            sign="+"
                            money={transaction.fromAmount}
                        />
                    )
                case 'wallet':
                    return (
                        <FormattedMoneyOrCash
                            withSymbol
                            sign="-"
                            money={transaction.fromAmount}
                        />
                    )
                case 'earn':
                    return (
                        <FormattedMoneyOrCash
                            withSymbol
                            sign="-"
                            money={transaction.fromAmountInUserCurrency}
                        />
                    )
                default:
                    return notReachable(transaction)
            }
        case 'deployed_smart_wallet_gnosis':
        case 'recovered_smart_wallet_gnosis':
        case 'recharge_disabled':
        case 'card_owners_updated':
        case 'failed_transaction':
        case 'breward_claim':
        case 'recharge_target_set':
            return null
        case 'card_spend_limit_updated':
            return (
                <FormattedMoneyOrCash
                    sign={null}
                    withSymbol
                    money={transaction.spendLimit}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(transaction)
    }
}
