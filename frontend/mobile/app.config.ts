import { ConfigContext, ExpoConfig } from 'expo/config'

import { version } from '../wallet/manifest.json'

const ZEAL_ENV = process.env.ZEAL_ENV || 'local'

const VERSION_CODE_FACTOR = 1000

const verisonToVersionCode = (version: string): number =>
    version
        .split('.')
        .toReversed()
        .map(
            (item, index) => Number(item) * Math.pow(VERSION_CODE_FACTOR, index)
        )
        .reduce((acc, item) => item + acc, 0)

const getName = () => {
    switch (ZEAL_ENV) {
        case 'local':
            return `Meal [local]`
        case 'development':
            return `Meal`
        case 'production':
            return `Zeal`
        default:
            throw new Error(`Unknown environment: ${ZEAL_ENV}`)
    }
}

const getIdentifier = () => {
    switch (ZEAL_ENV) {
        case 'local':
            return 'app.zeal.wallet.local'
        case 'development':
            return 'app.zeal.wallet.development'
        case 'production':
            return `app.zeal.wallet`

        default:
            throw new Error(`Unknown environment: ${ZEAL_ENV}`)
    }
}

const getIcon = () => {
    switch (ZEAL_ENV) {
        case 'local':
        case 'development':
            return './assets/app_icon_new_dev.png'
        case 'production':
            return './assets/app_icon_new.png'

        default:
            throw new Error(`Unknown environment: ${ZEAL_ENV}`)
    }
}

const getAdaptiveIcon = (): {
    foregroundImage: string
    monochromeImage: string
    backgroundImage?: string
    backgroundColor: string
} => {
    switch (ZEAL_ENV) {
        case 'local':
        case 'development':
            // `magick convert android-adaptive-icon.png -channel RGB -negate +channel android-adaptive-icon-dev.png` inverts main icon for dev
            return {
                foregroundImage:
                    './assets/app-icons/android-adaptive-icon-dev.png',
                monochromeImage:
                    './assets/app-icons/android-adaptive-icon-dev.png',
                backgroundColor: '#F0F',
            }

        case 'production':
            return {
                foregroundImage: './assets/app-icons/android-adaptive-icon.png',
                monochromeImage: './assets/app-icons/android-adaptive-icon.png',
                backgroundColor: '#0FF',
            }
        default:
            throw new Error(`Unknown environment: ${ZEAL_ENV}`)
    }
}

const config = ({ config }: ConfigContext): ExpoConfig => ({
    ...config,
    name: getName(),
    slug: 'zeal-wallet',
    version,
    description: 'Zeal Wallet',
    owner: 'zeal-app',
    orientation: 'portrait',
    scheme: ['zeal', 'wc'],
    icon: getIcon(),
    userInterfaceStyle: 'light',
    assetBundlePatterns: ['**/*'],
    plugins: [
        ['react-native-appsflyer', {}],
        [
            '@intercom/intercom-react-native',
            {
                appId: process.env.INTERCOM_APP_ID,
                androidApiKey: process.env.INTERCOM_ANDROID_API_KEY,
                iosApiKey: process.env.INTERCOM_IOS_API_KEY,
            },
        ],
        './plugins/android-storage-next.js',
        './plugins/ios-network-caching-config.js',
        './plugins/android-notifications-icon.js',
        './plugins/android-gradle-jvm-args.js',
        './plugins/appsflyer-manifest-merge-fix.js',
        'expo-localization',
        '@react-native-firebase/app',
        '../react-native-packages/app.plugin.js', // TODO @resetko-zeal for some reason @zeal/react-native-packages doesn't work here
        [
            'expo-camera',
            {
                cameraPermission:
                    'Zeal requires camera access for ID verification and to scan QR codes.',
            },
        ],
        [
            'expo-font',
            {
                fonts: [
                    './assets/fonts/Lexend-Bold.ttf',
                    './assets/fonts/Lexend-Medium.ttf',
                    './assets/fonts/Lexend-Regular.ttf',
                    './assets/fonts/Lexend-SemiBold.ttf',
                    './assets/fonts/Inter-Bold.ttf',
                    './assets/fonts/Inter-Medium.ttf',
                    './assets/fonts/Inter-Regular.ttf',
                    './assets/fonts/Inter-SemiBold.ttf',
                ],
            },
        ],
        [
            'expo-secure-store',
            {
                faceIDPermission:
                    'Zeal uses Biometrics to protect access to your wallet.', // TODO: @Nicvaniek localization https://docs.expo.dev/guides/localization/#translating-app-metadata
            },
        ],
        [
            'expo-build-properties',
            {
                android: {
                    compileSdkVersion: 35,
                    targetSdkVersion: 35,
                    minSdkVersion: 28,
                    buildToolsVersion: '35.0.0',
                    kotlinVersion: '1.9.24',
                    extraMavenRepos: [
                        'https://maven.sumsub.com/repository/maven-public/',
                    ],
                },
                ios: {
                    useFrameworks: 'static', // Needed for firebase messaging
                    deploymentTarget: '16.2',
                    extraPods: [
                        {
                            name: 'IdensicMobileSDK',
                            version: '1.33.1',
                            source: 'https://github.com/SumSubstance/Specs.git',
                        },
                    ],
                },
            },
        ],
        ...(ZEAL_ENV !== 'local' ? ['@sentry/react-native/expo'] : []),
        [
            'expo-splash-screen',
            {
                android: {
                    backgroundColor: '#0FF',
                    image: './assets/splash-new.png',
                    imageWidth: 400,
                },
                ios: {
                    backgroundColor: '#0FF',
                    image: './assets/splash-new.png',
                    imageWidth: 400,
                },
            },
        ],
    ],
    platforms: ['ios', 'android', 'web'],
    ios: {
        googleServicesFile: `./google-services/GoogleService-Info-${ZEAL_ENV}.plist`, // Needed for firebase messaging
        supportsTablet: false,
        bundleIdentifier: getIdentifier(),
        associatedDomains: [
            'webcredentials:passkey.zealwallet.com',
            'applinks:zeal-wallet-test.onelink.me', // TODO @resetko-zeal check if we need that one
            'applinks:zeal-wallet-ref.onelink.me',
            'claim-zeal.onelink.me',
            'applinks:get.zeal.app',
            'applinks:link.zeal.app',
        ],
        buildNumber: verisonToVersionCode(version).toString(10),
        entitlements: {
            'aps-environment': 'production', // Needed for firebase messaging
        },
        infoPlist: {
            NSCameraUsageDescription:
                'Zeal requires camera access for ID verification and to scan QR codes.',
            NSMicrophoneUsageDescription:
                'Zeal requires microphone access for ID verification.',
            NSPhotoLibraryUsageDescription:
                'Zeal requires access to your photo library for ID verification.',
            NSLocationWhenInUseUsageDescription:
                'Zeal requires geolocation data to prove your location for ID verification.',
            ITSAppUsesNonExemptEncryption: false,
        },
    },
    android: {
        adaptiveIcon: getAdaptiveIcon(),
        package: getIdentifier(),
        googleServicesFile: './google-services/google-services.json', // Needed for firebase messaging
        versionCode: verisonToVersionCode(version),
        intentFilters: [
            {
                action: 'VIEW',
                autoVerify: true,
                data: [
                    {
                        scheme: 'https',
                        host: 'passkey.zealwallet.com',
                    },
                ],
                category: ['BROWSABLE', 'DEFAULT'],
            },
            {
                action: 'VIEW',
                autoVerify: true,
                data: [
                    {
                        scheme: 'https',
                        host: 'get.zeal.app',
                    },
                ],
                category: ['BROWSABLE', 'DEFAULT'],
            },
            {
                action: 'VIEW',
                autoVerify: true,
                data: [
                    {
                        scheme: 'https',
                        host: 'claim-zeal.onelink.me',
                    },
                ],
                category: ['BROWSABLE', 'DEFAULT'],
            },
            {
                action: 'VIEW',
                autoVerify: true,
                data: [
                    {
                        scheme: 'https',
                        host: 'link.zeal.app',
                        pathPrefix: '/physical-card-activation',
                    },
                ],
                category: ['BROWSABLE', 'DEFAULT'],
            },
        ],
    },
    web: {
        favicon: './assets/icon-48.png',
    },
    extra: {
        ZEAL_ENV,
        ZEAL_LOCAL_PROXY: process.env.ZEAL_LOCAL_PROXY,
        ZEAL_LOCAL_BACKEND: process.env.ZEAL_LOCAL_BACKEND,
        ZEAL_LOCAL_INDEXER: process.env.ZEAL_LOCAL_INDEXER,
        ZEAL_LOCAL_HOST: process.env.ZEAL_LOCAL_HOST,
        eas: {
            projectId: '2df7d5cd-437b-43d3-bed9-cb337adba382',
        },
    },
})

export default config
